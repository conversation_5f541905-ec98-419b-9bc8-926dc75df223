{"name": "car-service", "version": "1.0.0", "private": true, "scripts": {"local:setup": "sh ./setup/setup.sh", "local:docker": "docker compose -f docker-compose.local.yml --env-file .env.local", "local:container:start": "npm run local:docker up -- -d --remove-orphans", "local:container:stop": "npm run local:docker down", "local:container:restart": "npm run local:container:stop && npm run local:container:start", "local:container:exec": "npm run local:docker exec", "local:container:logs": "npm run local:docker logs", "local:api": "npm run build && sam local start-api", "typeorm": "npx tsx ./node_modules/typeorm/cli.js -d ./src/shared/datasources/datasource.ts", "migration:local": "npx dotenv -e .env.local -- npm run typeorm", "migration:local:create": "npx typeorm migration:create src/migrations/$npm_config_name", "migration:local:generate": "npm run migration:local -- migration:generate src/migrations/$npm_config_name", "migration:local:run": "npm run migration:local -- migration:run", "migration:local:revert": "npm run migration:local -- migration:revert", "prebuild": "rm -rf dist", "checktype": "tsc", "build:migrations": "esbuild src/migrations/*.ts --platform=node --target=es2022 --outdir=dist/migrations --out-extension:.js=.mjs", "build:functions": "esbuild src/functions/*.ts --bundle --minify --platform=node --target=es2022 --outdir=dist/functions", "build": "npm run checktype && npm run build:migrations && npm run build:functions", "package": "./scripts/package.sh", "test": "cross-env NODE_OPTIONS=--experimental-vm-modules npx jest --runInBand", "test:cov": "cross-env NODE_OPTIONS=--experimental-vm-modules npx jest --runInBand --coverage --ci --reporters=default --reporters=jest-junit", "test:bluesg-events": "npm run build:functions && sam local invoke BluesgEventsFunction --event bluesg-events.json", "format": "prettier --write .", "lint": "eslint --fix .", "check:ci:format": "prettier --check .", "check:ci:lint": "eslint .", "prepare": "husky"}, "dependencies": {"@aws-sdk/client-secrets-manager": "^3.750.0", "@aws-sdk/client-sns": "^3.758.0", "@aws-sdk/rds-signer": "^3.750.0", "@bluesg-2/event-library": "^1.0.11", "@middy/core": "^6.0.0", "@middy/http-header-normalizer": "^6.3.0", "@middy/http-json-body-parser": "^6.0.0", "@middy/http-router": "^6.1.5", "@middy/input-output-logger": "^6.1.5", "axios": "^1.8.4", "class-transformer": "^0.5.1", "configcat-node": "^11.3.1", "dotenv": "^16.4.7", "dotenv-expand": "^12.0.1", "http-method-enum": "^1.0.0", "http-status": "^2.1.0", "jsonwebtoken": "^9.0.0", "jwks-rsa": "^3.2.0", "klona": "^2.0.6", "luxon": "^3.5.0", "pg": "^8.13.3", "reflect-metadata": "^0.2.2", "tiny-types": "^1.23.0", "typeorm": "^0.3.20", "typeorm-naming-strategies": "^4.1.0", "winston": "^3.17.0", "zod": "^3.24.2"}, "devDependencies": {"@aws-sdk/client-sqs": "^3.758.0", "@commitlint/cli": "^19.7.1", "@commitlint/config-angular": "^19.7.1", "@jest/globals": "^29.7.0", "@ngneat/falso": "^7.3.0", "@testcontainers/localstack": "^10.21.0", "@testcontainers/postgresql": "^10.18.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/aws-lambda": "^8.10.147", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^8.5.9", "@types/luxon": "^3.4.2", "@types/node": "^22.13.5", "@typescript-eslint/eslint-plugin": "^8.24.0", "@typescript-eslint/parser": "^8.24.0", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "esbuild": "^0.25.0", "eslint": "^9.20.1", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "husky": "^9.1.7", "jest": "^29.7.0", "jest-junit": "^16.0.0", "lint-staged": "^15.4.3", "prettier": "^3.5.1", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.3", "typescript": "^5.7.3"}}