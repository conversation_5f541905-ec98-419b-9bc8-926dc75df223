{"version": "0.2.0", "configurations": [{"name": "vscode-jest-tests.v2.car-service", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--inspect-brk=9229", "--forceExit", "--<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "--silent=false", "--runInBand", "--testNamePattern", "${jest.testNamePattern}", "--runTestsByPath", "${jest.testFile}"], "console": "integratedTerminal", "env": {"NODE_OPTIONS": "--experimental-vm-modules", "NODE_ENV": "test"}, "internalConsoleOptions": "openOnSessionStart"}]}