import { CarModelEntity } from 'src/car-model/entities/car-model.entity';
import { CarStateEntity } from 'src/car-state/entities/car-state.entity';
import { Car, CarAvailability, CarProvider } from 'src/car/car.type';
import { BaseEntity } from 'src/shared/entities/base.entity';
import { Column, Entity, ManyToOne, OneToOne } from 'typeorm';

@Entity({ name: 'car' })
export class CarEntity extends BaseEntity {
  constructor(
    props?: Omit<
      CarEntity,
      | 'hasId'
      | 'save'
      | 'remove'
      | 'softRemove'
      | 'recover'
      | 'reload'
      | 'setCreatedBy'
      | 'setModifiedBy'
      | 'setDeletedBy'
      | 'blockRemove'
    >,
  ) {
    super();

    Object.assign(this, props);
  }

  @Column({
    type: 'uuid',
    nullable: false,
  })
  carModelId: string;

  @Column({
    type: 'varchar',
    nullable: false,
  })
  plateNumber: string;

  @Column({
    type: 'varchar',
    nullable: false,
  })
  vin: string; // Every car that is produced by car manufacturer is given a Vehicle Identification Number following ISO

  @Column({
    type: 'timestamptz',
    nullable: true,
  })
  registrationDate?: Date;

  @Column({
    type: 'enum',
    enum: CarProvider,
    comment: 'Car Provider name',
    default: CarProvider.VULOG,
  })
  managedBy: CarProvider;

  @Column({
    type: 'varchar',
    comment: 'Pointing to vulog fleet id',
    nullable: true,
  })
  externalFleetId: string;

  @Column({
    type: 'varchar',
    comment: 'Pointing to vulog car id',
    nullable: true,
  })
  externalCarId: string;

  @Column({
    type: 'uuid',
    comment: 'The station where the car is located',
    nullable: true,
  })
  stationId?: string;

  @Column({
    type: 'timestamptz',
    comment: 'When the car was located at the station',
    nullable: true,
  })
  locatedAt?: Date;

  @Column({
    type: 'enum',
    enum: CarAvailability,
    comment: 'The availability of the car',
  })
  availability: CarAvailability;

  @Column({
    type: 'timestamptz',
    comment: 'When the availability status of the car was last updated',
    nullable: true,
  })
  availabilityUpdatedAt?: Date;

  @ManyToOne(() => CarModelEntity, (carModelEntity) => carModelEntity.cars)
  carModel?: CarModelEntity;

  @OneToOne(() => CarStateEntity, (carStateEntity) => carStateEntity.car)
  state?: CarStateEntity;

  static fromCar(car: Car): CarEntity {
    return new CarEntity({
      id: car.id?.value,
      carModelId: car.carModelId.value,
      plateNumber: car.plateNumber.value,
      vin: car.vin.value,
      registrationDate: car.registrationDate.toJSDate(),
      managedBy: car.managedBy,
      externalFleetId: car.externalFleetId,
      externalCarId: car.externalCarId,
      stationId: car.stationId,
      locatedAt: car.locatedAt?.toJSDate(),
      createdBy: car.createdBy?.value,
      deletedBy: car.deletedBy?.value,
      modifiedBy: car.modifiedBy?.value,
      availability: car.availability,
      availabilityUpdatedAt: car.availabilityUpdatedAt?.toJSDate(),
    });
  }
}
