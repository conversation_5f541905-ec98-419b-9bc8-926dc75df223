import { DateTime } from 'luxon';
import { CarModelId } from 'src/car-model/car-model.type';
import {
  Car,
  CarAvailability,
  CarId,
  CarPlateNumber,
  CarProvider,
  CarVin,
  StationId,
} from 'src/car/car.type';
import { CarEntity } from 'src/car/entities/car.entity';
import {
  CarAvailabilityNotUpdatedError,
  CarAvailabilityTransitionForbidden,
  CarAvailabilityUpdateAvailabilityTooOldError,
  CarLocationNotUpdatedError,
  CarLocationUpdateLocationTooOldError,
  CarNotFoundError,
  CarNotUpdatedError,
  CarUpdateRequestOutdatedError,
} from 'src/car/errors/car.error';
import { OneCarByExternalIdQuery } from 'src/car/query/car-by-external-id.query';
import { getDataSource } from 'src/shared/datasources/datasource';
import { toILike } from 'src/shared/datasources/operator';
import { getLogger } from 'src/shared/logging';
import {
  DataSource,
  EntityManager,
  FindOptionsWhere,
  Repository,
} from 'typeorm';

const logger = getLogger('car.repository');

// https://bluesg.atlassian.net/wiki/spaces/B2/pages/690421761/ERD+Car+Availabilities+from+CRS#%3Adata-model%3A-Data-Model
const ALLOWED_CURRENT_AVAILABILITIES: Record<
  CarAvailability,
  CarAvailability[]
> = {
  [CarAvailability.AVAILABLE]: [
    CarAvailability.AVAILABLE,
    CarAvailability.OUT_OF_SERVICE,
    CarAvailability.ON_RENTAL,
    CarAvailability.RESERVED,
  ],
  [CarAvailability.OUT_OF_SERVICE]: [
    CarAvailability.OUT_OF_SERVICE,
    CarAvailability.AVAILABLE,
  ],
  [CarAvailability.RESERVED]: [
    CarAvailability.RESERVED,
    CarAvailability.AVAILABLE,
  ],
  [CarAvailability.ON_RENTAL]: [
    CarAvailability.ON_RENTAL,
    CarAvailability.AVAILABLE,
    CarAvailability.RESERVED,
  ],
};

export class CarRepository extends Repository<CarEntity> {
  constructor(dataSource: DataSource, entityManager?: EntityManager) {
    super(CarEntity, entityManager ?? dataSource.createEntityManager());
  }

  async findByProps(props: {
    carModelId?: CarModelId;
    plateNumber?: CarPlateNumber;
    vin?: CarVin;
    managedBy?: CarProvider;
    externalFleetId?: string;
    externalCarId?: string;
    includeState?: boolean;
    availability?: CarAvailability;
    stationId?: StationId;
    skip?: number;
    take?: number;
  }): Promise<{ result: Car[]; total: number }> {
    const {
      carModelId,
      plateNumber,
      vin,
      managedBy,
      externalFleetId,
      externalCarId,
      availability,
      stationId,
      skip,
      take,
      includeState = false,
    } = props;

    const whereCondition: FindOptionsWhere<CarEntity> = {};

    if (carModelId) {
      whereCondition.carModelId = carModelId.value;
    }

    if (plateNumber) {
      whereCondition.plateNumber = toILike(plateNumber.value);
    }

    if (vin) {
      whereCondition.vin = toILike(vin.value);
    }

    if (managedBy) {
      whereCondition.managedBy = managedBy;
    }

    if (externalFleetId) {
      whereCondition.externalFleetId = externalFleetId;
    }

    if (externalCarId) {
      whereCondition.externalCarId = externalCarId;
    }

    if (availability) {
      whereCondition.availability = availability;
    }

    if (stationId) {
      whereCondition.stationId = stationId.value;
    }

    const total = await this.countBy(whereCondition);
    const result = await this.find({
      relations: { carModel: true, state: includeState },
      where: whereCondition,
      skip: skip,
      take: take,
    });
    return {
      result: result.map((carEntity) => Car.fromEntity(carEntity)),
      total,
    };
  }

  async existsByProps(props: {
    id?: CarId;
    plateNumber?: CarPlateNumber;
    vin?: CarVin;
  }): Promise<boolean> {
    const whereOrConditions: FindOptionsWhere<CarEntity>[] = [];

    const { id, plateNumber, vin } = props;

    if (!!id) {
      whereOrConditions.push({ id: id.value });
    }

    if (!!plateNumber) {
      whereOrConditions.push({ plateNumber: plateNumber.value });
    }

    if (!!vin) {
      whereOrConditions.push({ vin: vin.value });
    }

    if (!whereOrConditions.length) {
      console.warn('Where condition is missing');

      return false;
    }

    return this.existsBy(whereOrConditions);
  }

  async getOneByIdOrFail(
    id: CarId,
    includeCarModel: boolean = false,
  ): Promise<Car> {
    const entity = await this.findOne({
      relations: { carModel: includeCarModel },
      where: { id: id.value },
    });

    if (!entity) {
      throw new CarNotFoundError(id.value);
    }

    return entity ? Car.fromEntity(entity) : null;
  }

  async insertOne(car: Car): Promise<Car> {
    const target: CarEntity = CarEntity.fromCar(car);

    await this.save(this.create(target));

    return this.getOneByIdOrFail(new CarId(target.id));
  }

  async getOneByProps(query: OneCarByExternalIdQuery): Promise<Car | null> {
    const { externalFleetId, externalCarId } = query;

    if (!externalFleetId && !externalCarId) {
      logger.warn('Where condition is missing');
      return null;
    }

    const whereAndConditions: FindOptionsWhere<CarEntity> = {};

    if (!!externalFleetId) {
      whereAndConditions.externalFleetId = externalFleetId.value;
    }

    if (!!externalCarId) {
      whereAndConditions.externalCarId = externalCarId.value;
    }

    const entity = await this.findOne({
      where: whereAndConditions,
    });

    return entity ? Car.fromEntity(entity) : null;
  }

  async softRemoveById(id: string): Promise<void> {
    const carEntity = await this.findOneBy({ id });

    if (!carEntity) {
      throw new CarNotFoundError(id);
    }

    await this.softRemove(carEntity);
  }

  async updateLocation(
    carId: CarId,
    stationId: string,
    locatedAt: DateTime,
  ): Promise<Car> {
    const newLocatedAt = locatedAt.toJSDate();
    const newStationId = stationId;

    const carExists = await this.existsBy({ id: carId.value });
    if (!carExists) {
      throw new CarNotFoundError(carId.value);
    }

    const updateResult = await this.createQueryBuilder()
      .update(CarEntity)
      .set({
        stationId: newStationId,
        locatedAt: newLocatedAt,
      })
      .where('id = :id', { id: carId.value })
      .andWhere('(locatedAt IS NULL OR locatedAt < :newLocatedAt)', {
        newLocatedAt,
      })
      .execute();

    if (updateResult.affected === 0) {
      const car = await this.findOneBy({ id: carId.value });
      if (car && car.locatedAt && car.locatedAt >= newLocatedAt) {
        throw new CarLocationUpdateLocationTooOldError();
      }
      throw new CarLocationNotUpdatedError();
    }

    return this.getOneByIdOrFail(carId, true);
  }

  async updateAvailability(
    carId: CarId,
    availability: CarAvailability,
    availabilityAt: DateTime,
  ): Promise<void> {
    const newAvailability = availability;
    const newAvailabilityAt = availabilityAt.toJSDate();

    const updateResult = await this.createQueryBuilder()
      .update(CarEntity)
      .set({
        availability: newAvailability,
        availabilityUpdatedAt: newAvailabilityAt,
      })
      .where('id = :id', { id: carId.value })
      .andWhere(
        '(availabilityUpdatedAt IS NULL OR availabilityUpdatedAt < :newAvailabilityAt)',
        {
          newAvailabilityAt,
        },
      )
      .execute();

    if (updateResult.affected === 0) {
      const car = await this.findOneBy({ id: carId.value });
      if (
        car &&
        car.availabilityUpdatedAt &&
        car.availabilityUpdatedAt >= newAvailabilityAt
      ) {
        throw new CarAvailabilityUpdateAvailabilityTooOldError();
      }
      throw new CarAvailabilityNotUpdatedError();
    }
  }

  async switchCarAvailability(
    carId: CarId,
    carAvailability: CarAvailability,
    previousCarId: CarId,
    previousCarAvailability: CarAvailability,
    switchedAt: DateTime,
  ): Promise<void> {
    await this.manager.transaction(async (transactionalEntityManager) => {
      const transactionRepo = new CarRepository(
        await getDataSource(),
        transactionalEntityManager,
      );

      await transactionRepo.updateAvailability(
        carId,
        carAvailability,
        switchedAt,
      );

      await transactionRepo.updateAvailability(
        previousCarId,
        previousCarAvailability,
        switchedAt,
      );
    });
  }

  async updateAvailabilityAndLocationAsOf(
    carId: CarId,
    availability: CarAvailability,
    stationId: string,
    asOf: DateTime,
  ): Promise<Car> {
    const now = DateTime.now().toJSDate();

    const carExists = await this.existsBy({ id: carId.value });
    if (!carExists) {
      throw new CarNotFoundError(carId.value);
    }

    const allowedCurrentAvailabilities =
      ALLOWED_CURRENT_AVAILABILITIES[availability];

    const updateResult = await this.createQueryBuilder()
      .update(CarEntity)
      .set({
        availability: availability,
        availabilityUpdatedAt: now,
        stationId: stationId,
        locatedAt: now,
      })
      .where('id = :id', { id: carId.value })
      .andWhere('(availability IN (:...allowedCurrentAvailabilities))', {
        allowedCurrentAvailabilities,
      })
      .andWhere(
        '(availabilityUpdatedAt IS NULL OR availabilityUpdatedAt <= :asOf)',
        {
          asOf: asOf.toJSDate(),
        },
      )
      .andWhere('(locatedAt IS NULL OR locatedAt <= :asOf)', {
        asOf: asOf.toJSDate(),
      })
      .execute();

    if (updateResult.affected === 0) {
      const car = await this.findOneBy({ id: carId.value });
      if (car) {
        if (!allowedCurrentAvailabilities.includes(car.availability)) {
          throw new CarAvailabilityTransitionForbidden();
        }
        if (
          (car.locatedAt && car.locatedAt > asOf.toJSDate()) ||
          (car.availabilityUpdatedAt &&
            car.availabilityUpdatedAt > asOf.toJSDate())
        ) {
          throw new CarUpdateRequestOutdatedError();
        }
      }

      throw new CarNotUpdatedError();
    }

    return this.getOneByIdOrFail(carId, true);
  }

  async getExternalFleetIds(managedBy: CarProvider): Promise<string[]> {
    const result = await this.createQueryBuilder('car')
      .select('DISTINCT car.externalFleetId', 'externalFleetId')
      .where('car.managedBy = :managedBy', { managedBy })
      .andWhere('car.externalFleetId IS NOT NULL')
      .getRawMany();
    return result.map((row) => row.externalFleetId);
  }
}
