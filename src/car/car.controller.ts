import { CarModelService } from 'src/car-model/car-model.service';
import { CarModelNotFoundError } from 'src/car-model/errors/car-model.error';
import { VulogCarStateService } from 'src/car-state/vulog-car-state.service';
import { CarLocationService } from 'src/car/car-location.service';
import { CarService } from 'src/car/car.service';
import { Car, CarId } from 'src/car/car.type';
import { CarStateResponseDto } from 'src/car/dtos/admin/v1/car-state.response.dto';
import { CarQuery } from 'src/car/dtos/admin/v1/car.query';
import { CarResponseDto } from 'src/car/dtos/admin/v1/car.response.dto';
import { CreateCarRequestDto } from 'src/car/dtos/admin/v1/create-car.request.dto';
import { GetCarStateQuery } from 'src/car/dtos/admin/v1/get-car-state.query';
import { UpdateCarRequestDto } from 'src/car/dtos/admin/v1/update-car.request.dto';
import {
  CreatedResponse,
  MessageResponseBody,
  OkResponse,
  PaginationResponseBody,
  StandardResponseBody,
} from 'src/shared/http-response';
import { getLogger } from 'src/shared/logging';
import { Page } from 'src/shared/query/pagination';

const logger = getLogger('car.controller');

export class CarController {
  constructor(
    private readonly carService: CarService,
    private readonly carStationService: CarLocationService,
    private readonly carModelService: CarModelService,
    private readonly vulogCarStateService: VulogCarStateService,
  ) {}

  async getAllCars(
    query: CarQuery,
  ): Promise<OkResponse<PaginationResponseBody<CarResponseDto>>> {
    const page = await this.carService.getByQuery(query);

    const responseDto: Page<CarResponseDto> = Page.transform(
      page,
      CarResponseDto.from,
    );

    logger.debug('✅ Cars retrieved successfully', {
      count: page.items.length,
      pagination: page.pagination,
    });

    return OkResponse.fromPage(responseDto);
  }

  async getCarById(
    carId: string,
  ): Promise<OkResponse<StandardResponseBody<CarResponseDto>>> {
    const car = await this.carService.getById(carId);

    logger.debug('✅ Car retrieved successfully', { id: carId });

    return OkResponse.fromResult(CarResponseDto.from(car));
  }

  async createCar(
    createCarRequest: CreateCarRequestDto,
  ): Promise<CreatedResponse<StandardResponseBody<CarResponseDto>>> {
    let car: Car = createCarRequest.toCar();

    const isCarModelExisting: boolean = await this.carModelService.existsById(
      car.carModelId,
    );

    if (!isCarModelExisting) {
      throw new CarModelNotFoundError(car.carModelId.value);
    }

    car = await this.carService.createCar(car);

    const responseDto = CarResponseDto.from(car);

    logger.info('✅ Car is created successfully', { details: responseDto });

    return CreatedResponse.fromResult(responseDto);
  }

  async deleteCar(carId: string): Promise<OkResponse<MessageResponseBody>> {
    await this.carService.delete(carId);

    logger.info('✅ Car deleted successfully', { id: carId });

    return OkResponse.fromMessage('The car has been deleted.');
  }

  async getCarState(
    query: GetCarStateQuery,
  ): Promise<OkResponse<StandardResponseBody<CarStateResponseDto>>> {
    const carState = await this.vulogCarStateService.getCarState(query);

    const responseDto = CarStateResponseDto.from(carState);

    logger.debug('✅ Getting car state', {
      carId: query.carId,
      maxAgeSeconds: query.maxAge,
    });

    return OkResponse.fromResult(responseDto);
  }

  async updateCar(
    updateLocationRequest: UpdateCarRequestDto,
  ): Promise<OkResponse<StandardResponseBody<CarResponseDto>>> {
    const car = await this.carStationService.updateCarAvailabilityAndLocation(
      new CarId(updateLocationRequest.carId),
      updateLocationRequest.availability,
      updateLocationRequest.stationId,
      updateLocationRequest.getAsOf(),
    );

    const responseDto = CarResponseDto.from(car);

    logger.info('✅ Car availability and location updated successfully', {
      carId: updateLocationRequest.carId,
      availability: updateLocationRequest.availability,
      stationId: updateLocationRequest.stationId,
      asOf: updateLocationRequest.asOf,
    });

    return OkResponse.fromResult(responseDto);
  }
}
