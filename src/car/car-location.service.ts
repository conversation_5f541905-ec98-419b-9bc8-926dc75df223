import { DateTime } from 'luxon';
import { CarAvailabilityService } from 'src/car-availability/car-availability.service';
import { CarService } from 'src/car/car.service';
import { Car, CarAvailability, CarId } from 'src/car/car.type';
import { StationNotFoundError } from 'src/station/error/station.error';
import { StationClient } from 'src/station/station-client';

export class CarLocationService {
  constructor(
    private readonly carService: CarService,
    private readonly stationClient: StationClient,
    private readonly carAvailabilityService: CarAvailabilityService,
  ) {}

  async updateCarAvailabilityAndLocation(
    carId: CarId,
    availability: CarAvailability,
    stationId: string,
    asOf: DateTime,
  ): Promise<Car> {
    const stationExists =
      await this.stationClient.checkStationExists(stationId);

    if (!stationExists) {
      throw new StationNotFoundError(stationId);
    }

    const updatedCar =
      await this.carAvailabilityService.updateCarAvailabilityAndLocation(
        carId,
        availability,
        stationId,
        asOf,
      );

    return updatedCar;
  }
}
