import { CarAvailabilityModule } from 'src/car-availability/car-availability.module';
import { CarModelModule } from 'src/car-model/car-model.module';
import { CarStateModule } from 'src/car-state/car-state.module';
import { CarLocationService } from 'src/car/car-location.service';
import { CarController } from 'src/car/car.controller';
import { CarModule } from 'src/car/car.module';
import { StationClient } from 'src/station/station-client';

export class CarApiModule {
  readonly carController: CarController;

  constructor(
    carModule: CarModule,
    carModelModule: CarModelModule,
    carStateModule: CarStateModule,
    carStationService: CarLocationService,
  ) {
    this.carController = new CarController(
      carModule.carService,
      carStationService,
      carModelModule.carModelService,
      carStateModule.vulogCarStateService,
    );
  }

  static module: CarApiModule;

  static async init(): Promise<CarApiModule> {
    if (!this.module) {
      const carModule = await CarModule.init();
      const carModelModule = await CarModelModule.init();
      const carStateModule = await CarStateModule.init();
      const carAvailabilityModule = await CarAvailabilityModule.init();
      const carStationService = new CarLocationService(
        carModule.carService,
        new StationClient(),
        carAvailabilityModule.carAvailabilityService,
      );
      this.module = new CarApiModule(
        carModule,
        carModelModule,
        carStateModule,
        carStationService,
      );
    }

    return this.module;
  }
}
