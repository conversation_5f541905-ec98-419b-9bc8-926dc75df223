import { z } from 'zod';

export const getCarStateQuerySchema = z
  .object({
    'max-age': z
      .string()
      .regex(/^\d+$/, 'max-age must be a number')
      .transform((val) => parseInt(val, 10))
      .optional()
      .default('5'), // Default: 5 seconds
  })
  .strict()
  .optional()
  .nullable();

export type IGetCarStateQuery = z.infer<typeof getCarStateQuerySchema> & {
  carId: string;
};

export class GetCarStateQuery implements IGetCarStateQuery {
  readonly carId: string;
  readonly maxAge: number;

  constructor(props: IGetCarStateQuery) {
    this.carId = props.carId;
    this.maxAge = props['max-age'];
  }
}
