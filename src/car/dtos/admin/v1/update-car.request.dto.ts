import { DateTime } from 'luxon';
import { CarAvailability } from 'src/car/car.type';
import { isoDateTimeNotInFuture } from 'src/shared/validation';
import { z } from 'zod';

export const updateCarRequestSchema = z.object({
  availability: z.nativeEnum(CarAvailability),
  stationId: z.string().uuid('Please provide correct UUID for stationId field'),
  asOf: isoDateTimeNotInFuture('asOf', 'INVALID_AS_OF_TIME_IN_FUTURE'),
});

type IUpdateCarRequestDto = z.infer<typeof updateCarRequestSchema> & {
  carId: string;
};

export class UpdateCarRequestDto implements IUpdateCarRequestDto {
  readonly carId: string;
  readonly availability: CarAvailability;
  readonly stationId: string;
  readonly asOf: string;

  constructor(props: IUpdateCarRequestDto) {
    this.carId = props.carId;
    this.availability = props.availability;
    this.stationId = props.stationId;
    this.asOf = props.asOf;
  }

  getAsOf(): DateTime {
    return DateTime.fromISO(this.asOf);
  }
}
