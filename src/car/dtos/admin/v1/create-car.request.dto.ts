import { DateTime } from 'luxon';
import { CarModelId } from 'src/car-model/car-model.type';
import {
  Car,
  CarAvailability,
  CarPlateNumber,
  CarProvider,
  CarVin,
} from 'src/car/car.type';
import { isoDateTime } from 'src/shared/validation';
import { z } from 'zod';

export const createCarRequestSchema = z.object({
  carModelId: z
    .string()
    .uuid('Please provide correct UUID for carModelId field'),
  plateNumber: z.string().min(1).max(1000),
  vin: z.string().min(1).max(1000),
  registrationDate: isoDateTime('registrationDate'),
  managedBy: z.nativeEnum(CarProvider).optional(),
  externalFleetId: z.string().optional(),
  externalCarId: z.string().optional(),
});

type ICreateCarRequestDto = z.infer<typeof createCarRequestSchema>;

export class CreateCarRequestDto implements ICreateCarRequestDto {
  readonly carModelId: string;
  readonly plateNumber: string;
  readonly vin: string;
  readonly registrationDate: string;
  readonly managedBy?: CarProvider;
  readonly externalFleetId?: string;
  readonly externalCarId?: string;
  constructor(props: ICreateCarRequestDto) {
    Object.assign(this, props);
  }

  toCar(): Car {
    return new Car(
      undefined,
      new CarModelId(this.carModelId),
      null,
      new CarPlateNumber(this.plateNumber),
      new CarVin(this.vin),
      DateTime.fromISO(this.registrationDate),
      this.managedBy,
      this.externalFleetId,
      this.externalCarId,
      undefined,
      undefined,
      CarAvailability.OUT_OF_SERVICE,
      undefined,
      undefined, // Let TypeORM generate automatically
      undefined, // Let TypeORM generate automatically
      undefined,
      undefined,
      undefined,
      undefined,
    );
  }
}
