import { CarModel } from 'src/car-model/car-model.type';
import { Car, CarAvailability, CarProvider } from 'src/car/car.type';
import { CarStateResponseDto } from 'src/car/dtos/admin/v1/car-state.response.dto';

class CarModelResponseDto {
  constructor(
    readonly id: string,
    readonly name: string,
    readonly hrid: string,
    readonly description: string,
  ) {}

  static from(carModel: CarModel): CarModelResponseDto {
    return new CarModelResponseDto(
      carModel.id.value,
      carModel.name.value,
      carModel.hrid?.value,
      carModel.description?.value,
    );
  }
}

export class CarResponseDto {
  constructor(
    readonly id: string,
    readonly carModelId: string,
    readonly carModel: CarModelResponseDto,
    readonly plateNumber: string,
    readonly vin: string,
    readonly registrationDate: string,
    readonly managedBy: CarProvider,
    readonly externalFleetId: string,
    readonly externalCarId: string,
    readonly stationId: string,
    readonly locatedAt: string,
    readonly availability: CarAvailability,
    readonly availabilityUpdatedAt: string,
    readonly state: CarStateResponseDto | undefined,
    readonly createdAt: string,
    readonly modifiedAt: string,
    readonly deletedAt: string,
    readonly createdBy: string,
    readonly modifiedBy: string,
    readonly deletedBy: string,
  ) {}

  static from(car: Car): CarResponseDto {
    return new CarResponseDto(
      car.id.value,
      car.carModelId.value,
      car.carModel ? CarModelResponseDto.from(car.carModel) : undefined,
      car.plateNumber.value,
      car.vin.value,
      car.registrationDate.toISO(),
      car.managedBy,
      car.externalFleetId,
      car.externalCarId,
      car.stationId ?? undefined,
      car.locatedAt?.toISO(),
      car.availability,
      car.availabilityUpdatedAt?.toISO(),
      car.state ? CarStateResponseDto.from(car.state) : undefined,
      car.createdAt?.toISO(),
      car.modifiedAt?.toISO(),
      car.deletedAt?.toISO(),
      car.createdBy?.value,
      car.modifiedBy?.value,
      car.deletedBy?.value,
    );
  }
}
