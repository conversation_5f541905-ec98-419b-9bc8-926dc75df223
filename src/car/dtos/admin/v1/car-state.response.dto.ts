import { CarState } from 'src/car-state/car-state.type';
import {
  DoorsDetails,
  StationDetails,
  WindowsDetails,
} from 'src/vulog/vulog.type';

export class CarStateResponseDto {
  readonly carId: string;
  readonly locked: boolean;
  readonly batteryLevel: number;
  readonly allDoorsWindowsClosed: boolean;
  readonly doors: DoorsDetails;
  readonly windows: WindowsDetails;
  readonly engineOn: boolean;
  readonly cablePlugged: boolean;
  readonly charging: boolean;
  readonly stations: string[];
  readonly modificationDate: string;

  private constructor(props: {
    carId: string;
    locked: boolean;
    batteryLevel: number;
    allDoorsWindowsClosed: boolean;
    doors: DoorsDetails;
    windows: WindowsDetails;
    engineOn: boolean;
    cablePlugged: boolean;
    charging: boolean;
    stations: string[];
    modificationDate: string;
  }) {
    this.carId = props.carId;
    this.locked = props.locked;
    this.batteryLevel = props.batteryLevel;
    this.allDoorsWindowsClosed = props.allDoorsWindowsClosed;
    this.doors = props.doors;
    this.windows = props.windows;
    this.engineOn = props.engineOn;
    this.cablePlugged = props.cablePlugged;
    this.charging = props.charging;
    this.stations = props.stations;
    this.modificationDate = props.modificationDate;
  }

  /**
   * Transform CarState domain model to response DTO
   */
  static from(carState: CarState): CarStateResponseDto {
    // Extract zoneIds from stations if available
    const zoneIds: string[] = [];
    const stations = carState.stations as StationDetails | null;

    if (stations?.current) {
      stations.current.forEach((zone) => {
        if (zone.zoneId) {
          zoneIds.push(zone.zoneId);
        }
      });
    }

    return new CarStateResponseDto({
      carId: carState.carId.value,
      locked: carState.locked ?? false,
      batteryLevel: carState.batteryLevel?.value ?? 0,
      allDoorsWindowsClosed: carState.allDoorsWindowsClosed ?? false,
      doors: (carState.doors as DoorsDetails) ?? ({} as DoorsDetails),
      windows: (carState.windows as WindowsDetails) ?? ({} as WindowsDetails),
      engineOn: carState.engineOn ?? false,
      cablePlugged: carState.cablePlugged ?? false,
      charging: carState.charging ?? false,
      stations: zoneIds,
      modificationDate: carState.modifiedAt.toISO(),
    });
  }
}
