import { CarModelId } from 'src/car-model/car-model.type';
import {
  CarAvailability,
  CarPlateNumber,
  CarProvider,
  CarVin,
  StationId,
} from 'src/car/car.type';
import { Query, createQuerySchema } from 'src/shared/query/pagination';
import { z } from 'zod';

export const queryCarSchema = createQuerySchema({
  'car-model': z
    .string()
    .uuid('Please provide correct UUID for carModelId field')
    .optional(),
  'plate-number': z.string().optional(),
  vin: z.string().optional(),
  'managed-by': z.nativeEnum(CarProvider).optional(),
  'external-fleet-id': z.string().optional(),
  'external-car-id': z.string().optional(),
  'include-state': z.string().optional().default('false'),
  availability: z.nativeEnum(CarAvailability).optional(),
  'station-id': z
    .string()
    .uuid('Please provide correct UUID for stationId field')
    .optional(),
});

export type IQueryCar = z.infer<typeof queryCarSchema>;

export class CarQuery extends Query {
  readonly carModelId?: CarModelId;
  readonly plateNumber?: CarPlateNumber;
  readonly vin?: CarVin;
  readonly managedBy?: CarProvider;
  readonly externalFleetId?: string;
  readonly externalCarId?: string;
  readonly includeState?: boolean;
  readonly availability?: CarAvailability;
  readonly stationId?: StationId;

  constructor(props: IQueryCar) {
    super(props.page, props.nbPerPage);

    this.carModelId = props['car-model']
      ? new CarModelId(props['car-model'])
      : null;
    this.plateNumber = props['plate-number']
      ? new CarPlateNumber(props['plate-number'])
      : null;
    this.vin = props.vin ? new CarVin(props.vin) : null;
    this.managedBy = props['managed-by'];
    this.externalFleetId = props['external-fleet-id'];
    this.externalCarId = props['external-car-id'];
    this.includeState = props['include-state'] === 'true';
    this.availability = props.availability;
    this.stationId = props['station-id']
      ? new StationId(props['station-id'])
      : null;
  }

  static fromCarModelId(carModelId: string) {
    return new CarQuery({ 'car-model': carModelId });
  }
}
