import {
  ConflictError,
  InternalServerError,
  NotFoundError,
} from 'src/shared/errors/http.error';

const CAR_NOT_FOUND = 'CAR_NOT_FOUND';

export class CarAlreadyExistsError extends ConflictError {
  static readonly ERROR_CODE = 'CAR_ALREADY_EXISTS';
  constructor(carId: string) {
    super(CarAlreadyExistsError.ERROR_CODE, `Car ${carId} is already existing`);
  }
}

export class CarNotFoundError extends NotFoundError {
  constructor(carId: string) {
    super(CAR_NOT_FOUND, `The car ${carId} could not be found`);
  }
}

export class CarNotFoundByExternalIdError extends NotFoundError {
  constructor(externalCarId: string, externalFleetId: string = undefined) {
    super(
      CAR_NOT_FOUND,
      `The car ${externalFleetId}/${externalCarId} could not be found`,
    );
  }
}

export class CarLocationNotUpdatedError extends InternalServerError {
  static readonly ERROR_CODE = 'CAR_LOCATION_NOT_UPDATED';
  constructor() {
    super(
      CarLocationNotUpdatedError.ERROR_CODE,
      'The car location was not updated.',
    );
  }
}

export class CarLocationUpdateLocationTooOldError extends ConflictError {
  static readonly ERROR_CODE = 'LOCATION_TOO_OLD';
  constructor() {
    super(
      CarLocationUpdateLocationTooOldError.ERROR_CODE,
      'The car location is older than the one currently saved in the system. The car location was not updated.',
    );
  }
}

export class CarAvailabilityNotUpdatedError extends InternalServerError {
  static readonly ERROR_CODE = 'CAR_AVAILABILITY_NOT_UPDATED';
  constructor() {
    super(
      CarAvailabilityNotUpdatedError.ERROR_CODE,
      'The car availability was not updated.',
    );
  }
}

export class CarAvailabilityUpdateAvailabilityTooOldError extends ConflictError {
  static readonly ERROR_CODE = 'AVAILABILITY_TOO_OLD';
  constructor() {
    super(
      CarAvailabilityUpdateAvailabilityTooOldError.ERROR_CODE,
      'The car availability is older than the one currently saved in the system. The car availability was not updated.',
    );
  }
}

export class CarUpdateRequestOutdatedError extends ConflictError {
  static readonly ERROR_CODE = 'CAR_UPDATE_REQUEST_OUTDATED';
  constructor() {
    super(
      CarUpdateRequestOutdatedError.ERROR_CODE,
      'The car information has been updated since they were loaded. Please refresh your page before updating again.',
    );
  }
}

export class CarAvailabilityTransitionForbidden extends ConflictError {
  static readonly ERROR_CODE = 'CAR_AVAILABILITY_TRANSITION_FORBIDDEN';
  constructor() {
    super(
      CarAvailabilityTransitionForbidden.ERROR_CODE,
      'The car’s availability cannot be updated to the new value in its current state. Please refresh the car information and check again.',
    );
  }
}

export class CarNotUpdatedError extends InternalServerError {
  static readonly ERROR_CODE = 'CAR_NOT_UPDATED';
  constructor() {
    super(CarNotUpdatedError.ERROR_CODE, 'The car was not updated.');
  }
}
