import { CarPayload, EventProps } from '@bluesg-2/event-library';
import { CarLocationPayload } from '@bluesg-2/event-library/dist/car/car.event';
import { randomUUID } from 'crypto';
import { DateTime } from 'luxon';
import { CarModel, CarModelId } from 'src/car-model/car-model.type';
import { CarState } from 'src/car-state/car-state.type';
import { CarEntity } from 'src/car/entities/car.entity';
import { InvocationContext } from 'src/shared/invocation-context';
import { entityName } from 'src/shared/logging';
import { AdminUuid } from 'src/shared/shared.type';
import { TinyTypeOf } from 'tiny-types';

export class CarId extends TinyTypeOf<string>() {}

export class CarPlateNumber extends TinyTypeOf<string>() {}

export class CarVin extends TinyTypeOf<string>() {}

export class StationId extends TinyTypeOf<string>() {}

export enum CarProvider {
  VULOG = 'VULOG',
}

export enum CarAvailability {
  AVAILABLE = 'AVAILABLE',
  OUT_OF_SERVICE = 'OUT_OF_SERVICE',
  RESERVED = 'RESERVED',
  ON_RENTAL = 'ON_RENTAL',
}

export class Car {
  constructor(
    public id: CarId,
    public carModelId: CarModelId,
    public carModel: CarModel,
    public plateNumber: CarPlateNumber,
    public vin: CarVin,
    public registrationDate: DateTime,
    public managedBy: CarProvider,
    public externalFleetId: string,
    public externalCarId: string,
    public stationId: string,
    public locatedAt: DateTime,
    public availability: CarAvailability,
    public availabilityUpdatedAt: DateTime,
    public createdAt: DateTime,
    public modifiedAt: DateTime,
    public deletedAt: DateTime,
    public createdBy: AdminUuid,
    public modifiedBy: AdminUuid,
    public deletedBy: AdminUuid,
    public state?: CarState,
  ) {
    this.id = id || new CarId(randomUUID());
    this.state = state;
  }

  static fromEntity(entity: CarEntity): Car {
    return new Car(
      new CarId(entity.id),
      new CarModelId(entity.carModelId),
      entity.carModel ? CarModel.fromEntity(entity.carModel) : null,
      new CarPlateNumber(entity.plateNumber),
      new CarVin(entity.vin),
      DateTime.fromJSDate(entity.registrationDate),
      entity.managedBy,
      entity.externalFleetId,
      entity.externalCarId,
      entity.stationId,
      entity.locatedAt ? DateTime.fromJSDate(entity.locatedAt) : null,
      entity.availability,
      entity.availabilityUpdatedAt
        ? DateTime.fromJSDate(entity.availabilityUpdatedAt)
        : null,
      entity.createdAt ? DateTime.fromJSDate(entity.createdAt) : null,
      entity.modifiedAt ? DateTime.fromJSDate(entity.modifiedAt) : null,
      entity.deletedAt ? DateTime.fromJSDate(entity.deletedAt) : undefined,
      entity.createdBy ? new AdminUuid(entity.createdBy) : undefined,
      entity.modifiedBy ? new AdminUuid(entity.modifiedBy) : undefined,
      entity?.deletedBy ? new AdminUuid(entity.deletedBy) : undefined,
      entity.state ? entity.state.toCarState() : undefined,
    );
  }

  toJson(): object {
    return {
      id: this.id.value,
      carModelId: this.carModel.id.value,
      plateNumber: this.plateNumber.value,
      vin: this.vin.value,
      registrationDate: this.registrationDate?.toJSDate(),
      createdAt: this.createdAt?.toISO(),
      modifiedAt: this.modifiedAt?.toISO(),
      deletedAt: this.deletedAt?.toISO(),
      createdBy: this.createdBy?.value,
      modifiedBy: this.modifiedBy?.value,
      deletedBy: this.deletedBy?.value,
      state: this.state ? this.state.toJson() : null,
    };
  }

  toEventProps(): EventProps<CarPayload> {
    return {
      sender: entityName,
      sentAt: DateTime.now().toMillis(),
      correlationId: InvocationContext.getCorrelationId(),
      payload: {
        id: this.id.value,
        plateNumber: this.plateNumber.value,
        vin: this.vin.value,
        registrationDate: this.registrationDate.toISO(),
      },
    };
  }

  toLocationEventProps(): EventProps<CarLocationPayload> {
    return {
      sender: entityName,
      sentAt: DateTime.now().toMillis(),
      correlationId: InvocationContext.getCorrelationId(),
      payload: {
        id: this.id.value,
        plateNumber: this.plateNumber.value,
        vin: this.vin.value,
        stationId: this.stationId,
        locatedAt: this.locatedAt.toISO(),
        registrationDate: this.registrationDate.toISO(),
      },
    };
  }
}
