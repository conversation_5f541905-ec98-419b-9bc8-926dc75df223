import { CarService } from 'src/car/car.service';
import { CarRepository } from 'src/car/entities/car.repository';
import { getDataSource } from 'src/shared/datasources/datasource';
import { EventModule } from 'src/shared/event.module';
import { FeatureFlagModule } from 'src/shared/feature-flag.module';
import { DataSource } from 'typeorm';

export class CarModule {
  readonly carService: CarService;
  private readonly carRepository: CarRepository;

  constructor(
    dataSource: DataSource,
    eventModule: EventModule,
    featureFlagModule: FeatureFlagModule,
  ) {
    this.carRepository = new CarRepository(dataSource);
    this.carService = new CarService(
      this.carRepository,
      eventModule.eventPublisher,
      featureFlagModule.featureFlag,
    );
  }

  static module: CarModule;

  static async init(): Promise<CarModule> {
    if (!this.module) {
      this.module = new CarModule(
        await getDataSource(),
        await EventModule.init(),
        await FeatureFlagModule.init(),
      );
    }

    return this.module;
  }
}
