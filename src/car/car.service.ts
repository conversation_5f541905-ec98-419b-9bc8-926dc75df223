import { CarCreatedEvent, CarDeletedEvent } from '@bluesg-2/event-library';
import { CarLocationUpdatedEvent } from '@bluesg-2/event-library/dist/car/car.event';
import { DateTime } from 'luxon';
import { Car, CarId } from 'src/car/car.type';
import { CarQuery } from 'src/car/dtos/admin/v1/car.query';
import { CarRepository } from 'src/car/entities/car.repository';
import {
  CarAlreadyExistsError,
  CarNotFoundByExternalIdError,
} from 'src/car/errors/car.error';
import { OneCarByExternalIdQuery } from 'src/car/query/car-by-external-id.query';
import { EventPublisher } from 'src/shared/event.module';
import { FeatureFlag } from 'src/shared/feature-flag.module';
import { getLogger } from 'src/shared/logging';
import { Page } from 'src/shared/query/pagination';
import { VulogCarId } from 'src/shared/shared.type';

const logger = getLogger('car.service');

export class CarService {
  constructor(
    private readonly carRepository: CarRepository,
    private readonly eventPublisher: EventPublisher,
    private readonly featureFlag: FeatureFlag,
  ) {}

  async createCar(car: Car): Promise<Car> {
    const isCarExisting = await this.carRepository.existsByProps({
      plateNumber: car.plateNumber,
      vin: car.vin,
    });

    if (isCarExisting) {
      logger.error('Car is already existing', {
        debugInfo: {
          plateNumber: car.plateNumber.value,
          vin: car.vin.value,
        },
      });

      throw new CarAlreadyExistsError(car.id.value);
    }

    const createdCar = await this.carRepository.insertOne(car);

    await this.eventPublisher.send(
      new CarCreatedEvent(createdCar.toEventProps()),
    );

    return createdCar;
  }

  async getByQuery(query: CarQuery): Promise<Page<Car>> {
    const { result, total } = await this.carRepository.findByProps({
      carModelId: query.carModelId,
      plateNumber: query.plateNumber,
      vin: query.vin,
      managedBy: query.managedBy,
      externalFleetId: query.externalFleetId,
      externalCarId: query.externalCarId,
      includeState: query.includeState,
      availability: query.availability,
      stationId: query.stationId,
      skip: query.skip(),
      take: query.take(),
    });

    if (query.includeState) {
      logger.debug('Cars retrieved with state information');
    } else {
      logger.debug('Cars retrieved without state information');
    }

    return Page.fromQuery(query, result, total);
  }

  async getById(id: string): Promise<Car> {
    return await this.carRepository.getOneByIdOrFail(new CarId(id), true);
  }

  async delete(id: string): Promise<void> {
    const carToDelete = await this.getById(id);

    await this.carRepository.softRemoveById(id);

    await this.eventPublisher.send(
      new CarDeletedEvent(carToDelete.toEventProps()),
    );
  }

  async getOneByProps(query: OneCarByExternalIdQuery): Promise<Car | null> {
    return this.carRepository.getOneByProps(query);
  }

  async getOneByExternalCarId(vulogCarId: VulogCarId): Promise<Car> {
    const car = await this.getOneByProps(
      new OneCarByExternalIdQuery(vulogCarId),
    );

    if (!car) {
      throw new CarNotFoundByExternalIdError(vulogCarId.value);
    }

    return car;
  }

  async updateCarLocation(
    carId: CarId,
    stationId: string,
    locatedAt: DateTime,
  ): Promise<Car> {
    const updatedCar = await this.carRepository.updateLocation(
      carId,
      stationId,
      locatedAt,
    );

    await this.eventPublisher.send(
      new CarLocationUpdatedEvent(updatedCar.toLocationEventProps()),
    );

    return updatedCar;
  }
}
