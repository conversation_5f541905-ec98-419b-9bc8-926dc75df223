import { CarState } from 'src/car-state/car-state.type';
import { CarStateRepository } from 'src/car-state/entities/car-state.repository';
import { CarLockEvent } from 'src/car-state/events/car-lock.event';
import { CarService } from 'src/car/car.service';
import { CarId } from 'src/car/car.type';
import { CarNotFoundByExternalIdError } from 'src/car/errors/car.error';

export class CarStateService {
  constructor(
    private readonly carStateRepository: CarStateRepository,
    private readonly carService: CarService,
  ) {}

  async findByCarId(carId: CarId): Promise<CarState | null> {
    const {
      result: [carState],
    } = await this.carStateRepository.findByProps({
      carId,
    });

    return carState || null;
  }

  async upsertCarState(carState: CarState): Promise<CarState> {
    return this.carStateRepository.upsertNewerOnConflictCarId(carState);
  }

  async handleVehicleLockEvent(carLockEvent: CarLockEvent): Promise<void> {
    const car = await this.carService.getOneByProps({
      externalFleetId: carLockEvent.fleetId,
      externalCarId: carLockEvent.vehicleId,
    });

    if (!car) {
      throw new CarNotFoundByExternalIdError(
        carLockEvent.vehicleId.value,
        carLockEvent.fleetId.value,
      );
    }

    await this.carStateRepository.updateCarStateLockStatus(
      car.id,
      carLockEvent.insertionDate,
      carLockEvent.isLocked,
    );
  }
}
