import { DateTime } from 'luxon';
import { CarStateService } from 'src/car-state/car-state.service';
import { CarState } from 'src/car-state/car-state.type';
import { CarId, CarProvider } from 'src/car/car.type';
import { GetCarStateQuery } from 'src/car/dtos/admin/v1/get-car-state.query';
import { CarRepository } from 'src/car/entities/car.repository';
import { getErrorFormat } from 'src/shared/errors/general.error';
import { getLogger } from 'src/shared/logging';
import { VulogCarId, VulogFleetId } from 'src/shared/shared.type';
import { VulogVehicleInfoResponseDto } from 'src/vulog/dto/vulog-vehicle-info-response.dto';
import { VulogClient } from 'src/vulog/vulog-client';

const logger = getLogger('vulog-car-state.service');

export class VulogCarStateService {
  constructor(
    public readonly carStateService: CarStateService,
    private readonly vulogClient: VulogClient,
    private readonly carRepository: CarRepository,
  ) {}

  async updateAllCarStates(): Promise<void> {
    const fleetIds = await this.carRepository.getExternalFleetIds(
      CarProvider.VULOG,
    );

    for (const fleetId of fleetIds) {
      try {
        logger.info('Updating car state', { fleetId });

        const vehiclesInfo: VulogVehicleInfoResponseDto[] =
          await this.vulogClient.getVehiclesInfo(new VulogFleetId(fleetId));

        for (const vehicleInfo of vehiclesInfo) {
          const car = await this.carRepository.getOneByProps({
            externalCarId: new VulogCarId(vehicleInfo.id),
            externalFleetId: new VulogFleetId(fleetId),
          });

          if (!car) {
            logger.warn(
              'Vulog returned a vehicle id not associated with any car',
              { vulogId: vehicleInfo.id },
            );
            continue;
          }

          logger.debug('Comparing box status vs car availability', {
            boxStatus: vehicleInfo.status.boxStatus,
            availability: car.availability,
          });

          await this.carStateService.upsertCarState(
            vehicleInfo.toCarState(car.id),
          );
        }
      } catch (error: unknown) {
        logger.error('Error updating car state', {
          fleetId,
          error: getErrorFormat(error),
        });
      }
    }
  }

  async getCarState(query: GetCarStateQuery): Promise<CarState> {
    const car = await this.carRepository.getOneByIdOrFail(
      new CarId(query.carId),
    );

    if (!car.externalFleetId || !car.externalCarId) {
      throw new Error(
        `Car ${query.carId} doesn't have external IDs configured`,
      );
    }

    const minModifiedAt = DateTime.now().minus({ seconds: query.maxAge });

    const existingCarState = await this.carStateService.findByCarId(car.id);

    if (existingCarState && existingCarState.modifiedAt > minModifiedAt) {
      return existingCarState;
    }

    const vulogCarState = await this.vulogClient.getCarState(
      new VulogFleetId(car.externalFleetId),
      new VulogCarId(car.externalCarId),
      car.id,
    );

    return await this.carStateService.upsertCarState(vulogCarState);
  }
}
