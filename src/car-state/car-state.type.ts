import { instanceTo<PERSON>lain } from 'class-transformer';
import { DateTime } from 'luxon';
import { CarId } from 'src/car/car.type';
import {
  DoorsDetails,
  StationDetails,
  WindowsDetails,
} from 'src/vulog/vulog.type';
import { TinyTypeOf } from 'tiny-types';

export class CarStateId extends TinyTypeOf<string>() {}

export class CarBatteryLevel extends TinyTypeOf<number>() {}

export class CarState<RawProviderPayload extends object = object> {
  readonly id: CarStateId;
  readonly carId: CarId;
  readonly locked?: boolean;
  readonly batteryLevel?: CarBatteryLevel;
  readonly allDoorsWindowsClosed?: boolean;
  readonly doors?: DoorsDetails;
  readonly windows?: WindowsDetails;
  readonly engineOn?: boolean;
  readonly cablePlugged?: boolean;
  readonly charging?: boolean;
  readonly stations?: StationDetails;
  readonly providerPayload: RawProviderPayload;
  readonly createdAt: DateTime;
  readonly modifiedAt: DateTime;

  constructor(
    id: CarStateId,
    carId: CarId,
    locked: boolean,
    batteryLevel: CarBatteryLevel,
    allDoorsWindowsClosed: boolean,
    doors: DoorsDetails,
    windows: WindowsDetails,
    engineOn: boolean,
    cablePlugged: boolean,
    charging: boolean,
    stations: StationDetails,
    providerPayload: RawProviderPayload,
    createdAt: DateTime,
    modifiedAt: DateTime,
  ) {
    this.id = id;
    this.carId = carId;
    this.locked = locked;
    this.batteryLevel = batteryLevel;
    this.allDoorsWindowsClosed = allDoorsWindowsClosed;
    this.doors = doors;
    this.windows = windows;
    this.engineOn = engineOn;
    this.cablePlugged = cablePlugged;
    this.charging = charging;
    this.stations = stations;
    this.providerPayload = providerPayload;
    this.createdAt = createdAt;
    this.modifiedAt = modifiedAt;
  }

  toJson() {
    return {
      id: this.id.value,
      carId: this.carId.value,
      locked: this.locked,
      batteryLevel: this.batteryLevel?.value,
      allDoorsWindowsClosed: this.allDoorsWindowsClosed,
      doors: this.doors ? instanceToPlain(this.doors) : null,
      windows: this.windows ? instanceToPlain(this.windows) : null,
      engineOn: this.engineOn,
      cablePlugged: this.cablePlugged,
      charging: this.charging,
      stations: this.stations ? instanceToPlain(this.stations) : null,
      providerPayload: this.providerPayload,
      createdAt: this.createdAt.toISO(),
      modifiedAt: this.modifiedAt.toISO(),
    };
  }

  toEventProps() {} // TODO: Define later
}
