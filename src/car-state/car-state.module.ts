import { CarStateService } from 'src/car-state/car-state.service';
import { CarStateRepository } from 'src/car-state/entities/car-state.repository';
import { VulogCarStateService } from 'src/car-state/vulog-car-state.service';
import { CarModule } from 'src/car/car.module';
import { CarRepository } from 'src/car/entities/car.repository';
import { getDataSource } from 'src/shared/datasources/datasource';
import { VulogClient } from 'src/vulog/vulog-client';
import { DataSource } from 'typeorm';

export class CarStateModule {
  readonly carStateRepository: CarStateRepository;
  readonly carStateService: CarStateService;
  readonly vulogCarStateService: VulogCarStateService;

  constructor(dataSource: DataSource, carModule: CarModule) {
    this.carStateRepository = new CarStateRepository(dataSource);
    this.carStateService = new CarStateService(
      this.carStateRepository,
      carModule.carService,
    );
    this.vulogCarStateService = new VulogCarStateService(
      this.carStateService,
      new VulogClient(),
      new CarRepository(dataSource),
    );
  }

  static module: CarStateModule;

  static async init(): Promise<CarStateModule> {
    if (!this.module) {
      this.module = new CarStateModule(
        await getDataSource(),
        await CarModule.init(),
      );
    }

    return this.module;
  }
}
