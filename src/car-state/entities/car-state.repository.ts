import { DateTime } from 'luxon';
import { CarState, CarStateId } from 'src/car-state/car-state.type';
import { CarStateEntity } from 'src/car-state/entities/car-state.entity';
import { CarId } from 'src/car/car.type';
import { DataSource, FindOptionsWhere, Repository } from 'typeorm';

export class CarStateRepository extends Repository<CarStateEntity> {
  constructor(dataSource: DataSource) {
    super(CarStateEntity, dataSource.createEntityManager());
  }

  async findByProps(props: {
    id?: CarStateId;
    carId?: CarId;
    skip?: number;
    take?: number;
  }): Promise<{ result: CarState[]; total: number }> {
    const { id, carId, skip, take } = props;

    const whereCondition: FindOptionsWhere<CarStateEntity> = {};

    if (id) {
      whereCondition.id = id.value;
    }

    if (carId) {
      whereCondition.carId = carId.value;
    }

    const total = await this.countBy(whereCondition);

    const result = await this.find({
      relations: { car: true },
      where: whereCondition,
      skip: skip,
      take: take,
    });

    return {
      result: result.map((carStateEntity) => carStateEntity.toCarState()),
      total,
    };
  }

  async existsByProps(props: { id: CarStateId }): Promise<boolean> {
    const whereOrConditions: FindOptionsWhere<CarStateEntity>[] = [];

    const { id } = props;

    if (!!id) {
      whereOrConditions.push({ id: id.value });
    }

    if (!whereOrConditions.length) {
      console.warn('Where condition is missing');

      return false;
    }

    return this.existsBy(whereOrConditions);
  }

  async getOne(id: CarStateId): Promise<CarState> {
    const entity = await this.findOne({
      where: { id: id.value },
    });

    return entity ? entity.toCarState() : null;
  }

  async upsertNewerOnConflictCarId(carState: CarState): Promise<CarState> {
    const target: CarStateEntity = CarStateEntity.fromCarState(carState);
    delete target.id;

    await this.query(
      `INSERT INTO car_state(
        created_at, created_by, modified_at, modified_by, 
        car_id, locked, ev_battery_level, all_doors_windows_closed,
        doors, windows, engine_on, cable_plugged, charging,
        stations, provider_payload
      ) 
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
      ON CONFLICT (car_id) 
      DO UPDATE SET
        modified_at = EXCLUDED.modified_at,
        modified_by = EXCLUDED.modified_by,
        locked = EXCLUDED.locked,
        ev_battery_level = EXCLUDED.ev_battery_level,
        all_doors_windows_closed = EXCLUDED.all_doors_windows_closed,
        doors = EXCLUDED.doors,
        windows = EXCLUDED.windows,
        engine_on = EXCLUDED.engine_on,
        cable_plugged = EXCLUDED.cable_plugged,
        charging = EXCLUDED.charging,
        stations = EXCLUDED.stations,
        provider_payload = EXCLUDED.provider_payload
      WHERE car_state.modified_at < EXCLUDED.modified_at`,
      [
        target.createdAt,
        target.createdBy,
        target.modifiedAt,
        target.modifiedBy,
        target.carId,
        target.locked,
        target.evBatteryLevel,
        target.allDoorsWindowsClosed,
        target.doors,
        target.windows,
        target.engineOn,
        target.cablePlugged,
        target.charging,
        target.stations,
        target.providerPayload,
      ],
    );

    return (
      await this.findOne({
        where: { carId: carState.carId.value },
      })
    ).toCarState();
  }

  async insertOne(carState: CarState): Promise<CarState> {
    const target: CarStateEntity = CarStateEntity.fromCarState(carState);

    await this.save(this.create(target));

    return this.getOne(new CarStateId(target.id));
  }

  async updateCarStateLockStatus(
    carId: CarId,
    modifiedDate: DateTime,
    locked: boolean,
  ): Promise<void> {
    await this.createQueryBuilder()
      .update(CarStateEntity)
      .set({
        modifiedAt: modifiedDate.toJSDate(),
        locked: locked,
      })
      .where('car_id = :carId', { carId: carId.value })
      .andWhere('(modified_at IS NULL OR modified_at <= :modifiedDate)', {
        modifiedDate: modifiedDate.toJSDate(),
      })
      .execute();
  }
}
