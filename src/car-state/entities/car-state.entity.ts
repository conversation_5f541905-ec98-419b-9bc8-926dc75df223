import { DateTime } from 'luxon';
import {
  CarBatteryLevel,
  CarState,
  CarStateId,
} from 'src/car-state/car-state.type';
import { CarId } from 'src/car/car.type';
import { CarEntity } from 'src/car/entities/car.entity';
import { BaseEntity } from 'src/shared/entities/base.entity';
import { isNumberString, isObject } from 'src/shared/validation';
import { Column, Entity, Index, JoinColumn, OneToOne } from 'typeorm';

@Entity('car_state')
@Index('idx_unique_car_state_car_id', ['carId'], {
  unique: true,
})
export class CarStateEntity extends BaseEntity {
  @Column({
    type: 'uuid',
    nullable: false,
    comment:
      'This column refers to column `id` of table `car` as a foreign key',
  })
  carId: string;

  @Column({
    type: 'boolean',
    nullable: true,
    comment: 'This column describes whether the car is locked or not',
  })
  locked?: boolean;

  @Column({
    type: 'decimal',
    nullable: true,
    comment:
      'This column describes the battery level of the vehicle. Although "battery" in English is actually French "accumulateurs" or Vietnamese "acquy", it does not share the same meaning with the column',
  })
  evBatteryLevel?: string;

  @Column({
    type: 'boolean',
    nullable: true,
    comment:
      'This column describes whether the car has all doors and all windows closed or not',
  })
  allDoorsWindowsClosed?: boolean;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'This column describes the details of doors of the car',
  })
  doors?: object; // The schema of the object depends on the vehicle gateway, so no concrete typing is defined here

  @Column({
    type: 'json',
    nullable: true,
    comment: 'This column describes the details of windows of the car',
  })
  windows?: object; // The schema of the object depends on the vehicle gateway, so no concrete typing is defined here

  @Column({
    type: 'boolean',
    nullable: true,
    comment: 'This column describes whether the engine of the car is on or not',
  })
  engineOn?: boolean;

  @Column({
    type: 'boolean',
    nullable: true,
    comment: 'This column describes whether the car is plugged or not',
  })
  cablePlugged?: boolean;

  @Column({
    type: 'boolean',
    nullable: true,
    comment:
      'This column describes whether the car is charging or not, given that the car is plugged into the EVSE before',
  })
  charging?: boolean;

  @Column({
    type: 'json',
    nullable: true,
    comment:
      'This column describes the details of location info of the car. For Vulog Vehicle Gateway, the term is "zones"',
  })
  stations?: object; // The schema of the object depends on the vehicle gateway, so no concrete typing is defined here

  @Column({
    type: 'json',
    nullable: false,
    comment:
      'This column holds the raw payload coming from the provider (as known as vehicle gateway)',
  })
  providerPayload: object; // The schema of the object depends on the vehicle gateway, so no concrete typing is defined here

  @OneToOne(() => CarEntity, (carEntity) => carEntity.state)
  @JoinColumn({ name: 'car_id' })
  car?: CarEntity;

  toCarState(): CarState {
    return new CarState(
      new CarStateId(this.id),
      new CarId(this.carId),
      this.locked,
      isNumberString(this.evBatteryLevel)
        ? new CarBatteryLevel(+this.evBatteryLevel)
        : null,
      this.allDoorsWindowsClosed,
      isObject(this.doors) ? this.doors : null,
      isObject(this.windows) ? this.windows : null,
      this.engineOn,
      this.cablePlugged,
      this.charging,
      isObject(this.stations) ? this.stations : null,
      this.providerPayload,
      DateTime.fromJSDate(this.createdAt),
      DateTime.fromJSDate(this.modifiedAt),
    );
  }

  static fromCarState(domain: CarState): CarStateEntity {
    const entity = new CarStateEntity();

    const pojo = domain.toJson();

    entity.id = domain.id.value;
    entity.carId = domain.carId.value;
    entity.locked = domain.locked;
    entity.evBatteryLevel = domain.batteryLevel?.value?.toString();
    entity.allDoorsWindowsClosed = domain.allDoorsWindowsClosed;
    entity.doors = !!domain.doors ? pojo.doors : undefined;
    entity.windows = !!domain.windows ? pojo.windows : undefined;
    entity.engineOn = domain.engineOn;
    entity.cablePlugged = domain.cablePlugged;
    entity.charging = domain.charging;
    entity.stations = !!domain.stations ? pojo.stations : undefined;
    entity.providerPayload = pojo.providerPayload;
    entity.createdAt = domain.createdAt.toJSDate();
    entity.modifiedAt = domain.modifiedAt.toJSDate();

    return entity;
  }
}
