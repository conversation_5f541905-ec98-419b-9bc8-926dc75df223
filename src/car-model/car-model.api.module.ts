import { CarModelController } from 'src/car-model/car-model.controller';
import { CarModelModule } from 'src/car-model/car-model.module';
import { CarModule } from 'src/car/car.module';

export class CarModelApiModule {
  readonly carModelController: CarModelController;

  constructor(carModelModule: CarModelModule, carModule: CarModule) {
    this.carModelController = new CarModelController(
      carModelModule.carModelService,
      carModule.carService,
    );
  }

  static module: CarModelApiModule;

  static async init(): Promise<CarModelApiModule> {
    if (!this.module) {
      this.module = new CarModelApiModule(
        await CarModelModule.init(),
        await CarModule.init(),
      );
    }

    return this.module;
  }
}
