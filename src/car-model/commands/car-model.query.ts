import { CarModelHrid, CarModelName } from 'src/car-model/car-model.type';
import { Query, createQuerySchema } from 'src/shared/query/pagination';
import { z } from 'zod';

export const queryCarModelSchema = createQuerySchema({
  hrid: z.string().optional(),
  name: z.string().optional(),
});

export type IQueryCarModel = z.infer<typeof queryCarModelSchema>;

export class CarModelQuery extends Query {
  readonly hrid?: CarModelHrid;
  readonly name?: CarModelName;

  constructor(props: IQueryCarModel) {
    super(props.page, props.nbPerPage);

    this.hrid = props.hrid ? new CarModelHrid(props.hrid) : null;
    this.name = props.name ? new CarModelName(props.name) : null;
  }
}
