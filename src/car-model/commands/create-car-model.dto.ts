import {
  CarModel,
  CarModelDescription,
  CarModelHrid,
  CarModelName,
} from 'src/car-model/car-model.type';
import { z } from 'zod';

export const createCarModelSchema = z.object({
  hrid: z.string().min(1).optional(),
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
});

type ICreateCarModel = z.infer<typeof createCarModelSchema>;

export class CreateCarModelCommand implements ICreateCarModel {
  readonly hrid: string;
  readonly name: string;
  readonly description: string;
  constructor(props: ICreateCarModel) {
    Object.assign(this, props);
  }

  toCarModel(): CarModel {
    return new CarModel(
      null,
      new CarModelName(this.name),
      this.hrid ? new CarModelHrid(this.hrid) : null,
      this.description ? new CarModelDescription(this.description) : undefined,
    );
  }
}
