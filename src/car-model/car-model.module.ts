import { CarModelService } from 'src/car-model/car-model.service';
import { CarModelRepository } from 'src/car-model/entities/car-model.repository';
import { getDataSource } from 'src/shared/datasources/datasource';
import { EventModule } from 'src/shared/event.module';
import { DataSource } from 'typeorm';

export class CarModelModule {
  readonly carModelService: CarModelService;
  private readonly carModelRepository: CarModelRepository;

  constructor(dataSource: DataSource, eventModule: EventModule) {
    this.carModelRepository = new CarModelRepository(dataSource);
    this.carModelService = new CarModelService(
      this.carModelRepository,
      eventModule.eventPublisher,
    );
  }

  static module: CarModelModule;

  static async init(): Promise<CarModelModule> {
    if (!this.module) {
      this.module = new CarModelModule(
        await getDataSource(),
        await EventModule.init(),
      );
    }

    return this.module;
  }
}
