import { NotFoundError } from 'src/shared/errors/http.error';
import { BadRequestError } from 'src/shared/errors/http.error';
import { DbConflictError } from 'src/shared/errors/repository.error';

export class CarModelNotFoundError extends NotFoundError {
  constructor(carModelId: string) {
    super(
      'CAR_MODEL_NOT_FOUND',
      `The car model ${carModelId} could not be found`,
    );
  }
}

export class CarModelHridAlreadyExistsError extends DbConflictError {
  constructor(carModelHrid: string) {
    super(
      'CAR_MODEL_ALREADY_EXISTS',
      `Car model with hrid ${carModelHrid} already exists`,
    );
  }
}

export class InvalidCreatedByError extends BadRequestError {
  constructor() {
    super('INVALID_CREATED_BY', 'The createdBy field is required');
  }
}

export class CarModelCurrentlyInUseError extends BadRequestError {
  constructor(carModelId: string) {
    super(
      'CAR_MODEL_CURRENTLY_IN_USE',
      `The car model ${carModelId} is currently in use`,
    );
  }
}
