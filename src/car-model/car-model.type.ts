import { CarModelPayload, EventProps } from '@bluesg-2/event-library';
import { DateTime } from 'luxon';
import { CarModelEntity } from 'src/car-model/entities/car-model.entity';
import { InvocationContext } from 'src/shared/invocation-context';
import { entityName } from 'src/shared/logging';
import { TinyTypeOf } from 'tiny-types';

export class CarModelId extends TinyTypeOf<string>() {}

export class CarModelHrid extends TinyTypeOf<string>() {}
export class CarModelName extends TinyTypeOf<string>() {}
export class CarModelDescription extends TinyTypeOf<string>() {}

export class CarModel {
  constructor(
    public id: CarModelId,
    public name: CarModelName,
    public hrid?: CarModelHrid,
    public description?: CarModelDescription,
    public createdBy?: string,
    public modifiedBy?: string,
    public deletedBy?: string,
    public createdAt?: DateTime,
    public modifiedAt?: DateTime,
    public deletedAt?: DateTime,
  ) {
    if (!this.hrid) {
      this.hrid = this.generateHrid(this.name);
    }
  }

  private generateHrid(name: CarModelName): CarModelHrid {
    return new CarModelHrid(
      name.value
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, ''),
    );
  }

  static fromEntity(entity: CarModelEntity): CarModel {
    return new CarModel(
      new CarModelId(entity.id),
      new CarModelName(entity.name),
      new CarModelHrid(entity.hrid),
      entity.description
        ? new CarModelDescription(entity.description)
        : undefined,
      entity.createdBy,
      entity.modifiedBy,
      entity.deletedBy,
      entity.createdAt ? DateTime.fromJSDate(entity.createdAt) : undefined,
      entity.modifiedAt ? DateTime.fromJSDate(entity.modifiedAt) : undefined,
      entity.deletedAt ? DateTime.fromJSDate(entity.deletedAt) : undefined,
    );
  }

  toJson(): object {
    return {
      id: this.id.value,
      hrid: this.hrid.value,
      name: this.name.value,
      description: this.description?.value,
      createdBy: this.createdBy,
      modifiedBy: this.modifiedBy,
      deletedBy: this.deletedBy,
      createdAt: this.createdAt,
      modifiedAt: this.modifiedAt,
      deletedAt: this.deletedAt,
    };
  }

  toEventProps(): EventProps<CarModelPayload> {
    return {
      sender: entityName,
      sentAt: DateTime.now().toMillis(),
      correlationId: InvocationContext.getCorrelationId(),
      payload: {
        id: this.id.value,
        hrid: this.hrid.value,
        name: this.name.value,
        description: this.description?.value,
      },
    };
  }
}
