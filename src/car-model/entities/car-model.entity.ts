import { DateTime } from 'luxon';
import {
  CarModel,
  CarModelDescription,
  CarModelHrid,
  CarModelId,
  CarModelName,
} from 'src/car-model/car-model.type';
import { CarEntity } from 'src/car/entities/car.entity';
import { BaseEntity } from 'src/shared/entities/base.entity';
import { Column, Entity, OneToMany } from 'typeorm';

@Entity('car_model')
export class CarModelEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
    unique: true,
    comment: 'User reference of the car model',
  })
  hrid: string;

  @Column({
    type: 'text',
    comment: 'Name of the car model',
  })
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @OneToMany(() => CarEntity, (carEntity) => carEntity.carModel)
  cars?: CarEntity[];

  toCarModel(): CarModel {
    return new CarModel(
      new CarModelId(this.id),
      new CarModelName(this.name),
      new CarModelHrid(this.hrid),
      this.description ? new CarModelDescription(this.description) : undefined,
      this.createdBy,
      this.modifiedBy,
      this.deletedBy,
      this.createdAt ? DateTime.fromJSDate(this.createdAt) : undefined,
      this.modifiedAt ? DateTime.fromJSDate(this.modifiedAt) : undefined,
      this.deletedAt ? DateTime.fromJSDate(this.deletedAt) : undefined,
    );
  }

  static fromCarModel(carModel: CarModel): CarModelEntity {
    const entity = new CarModelEntity();
    if (carModel.id) {
      entity.id = carModel.id.value;
    }
    entity.hrid = carModel.hrid.value;
    entity.name = carModel.name.value;
    entity.description = carModel.description?.value;
    entity.createdBy = carModel.createdBy;
    return entity;
  }
}
