import {
  CarModel,
  CarModelHrid,
  CarModelId,
  CarModelName,
} from 'src/car-model/car-model.type';
import { CarModelEntity } from 'src/car-model/entities/car-model.entity';
import {
  CarModelHridAlreadyExistsError,
  CarModelNotFoundError,
} from 'src/car-model/errors/car-model.error';
import { toILike } from 'src/shared/datasources/operator';
import 'src/shared/errors/repository.error';
import { DbConflictError } from 'src/shared/errors/repository.error';
import {
  DataSource,
  FindOptionsWhere,
  QueryFailedError,
  Repository,
} from 'typeorm';

export class CarModelRepository extends Repository<CarModelEntity> {
  constructor(dataSource: DataSource) {
    super(CarModelEntity, dataSource.createEntityManager());
  }

  async findByProps(props: {
    hrid?: CarModelHrid;
    name?: CarModelName;
    skip?: number;
    take?: number;
  }): Promise<{ result: CarModel[]; total: number }> {
    const { hrid, name, skip, take } = props;

    let whereCondition: FindOptionsWhere<CarModelEntity> = {};

    if (hrid) {
      whereCondition = { ...whereCondition, hrid: toILike(hrid.value) };
    }

    if (name) {
      whereCondition = { ...whereCondition, name: toILike(name.value) };
    }

    const total = await this.countBy(whereCondition);

    const result = await this.find({
      where: whereCondition,
      skip: skip,
      take: take,
    });

    return {
      result: result.map((carModelEntity) =>
        CarModel.fromEntity(carModelEntity),
      ),
      total,
    };
  }

  async findOneByIdOrFail(id: string): Promise<CarModel> {
    const entity = await this.findOneBy({ id });

    if (!entity) {
      throw new CarModelNotFoundError(id);
    }
    return entity.toCarModel();
  }

  async createOrFail(data: CarModel): Promise<CarModel> {
    try {
      const carEntity = CarModelEntity.fromCarModel(data);
      const entity = this.create(carEntity);
      return (await this.save(entity)).toCarModel();
    } catch (error: unknown) {
      if (error instanceof QueryFailedError) {
        if (error.driverError.code === DbConflictError.DB_CONFLICT_ERROR_CODE) {
          throw new CarModelHridAlreadyExistsError(data.hrid.value);
        }
      }
      throw error;
    }
  }

  async existsByPassingProps(props: { id?: CarModelId }): Promise<boolean> {
    const whereOrConditions: FindOptionsWhere<CarModelEntity>[] = [];

    const { id } = props;

    if (!!id) {
      whereOrConditions.push({ id: id.value });
    }

    return this.existsBy(whereOrConditions);
  }

  async softRemoveById(id: string): Promise<void> {
    const carEntity = await this.findOneBy({ id });

    if (!carEntity) {
      throw new CarModelNotFoundError(id);
    }

    await this.softRemove(carEntity);
  }
}
