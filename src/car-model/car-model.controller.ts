import { CarModelService } from 'src/car-model/car-model.service';
import { CarModel } from 'src/car-model/car-model.type';
import { CarModelQuery } from 'src/car-model/commands/car-model.query';
import { CreateCarModelCommand } from 'src/car-model/commands/create-car-model.dto';
import { CarModelCurrentlyInUseError } from 'src/car-model/errors/car-model.error';
import { CarService } from 'src/car/car.service';
import { Car } from 'src/car/car.type';
import { CarQuery } from 'src/car/dtos/admin/v1/car.query';
import {
  CreatedResponse,
  MessageResponseBody,
  OkResponse,
  PaginationResponseBody,
  StandardResponseBody,
} from 'src/shared/http-response';
import { getLogger } from 'src/shared/logging';
import { Page } from 'src/shared/query/pagination';

const logger = getLogger('car-model.controller');

export class CarModelController {
  constructor(
    private readonly carModelService: CarModelService,
    private readonly carService: CarService,
  ) {}

  async getAllCarModels(
    query: CarModelQuery | null,
  ): Promise<OkResponse<PaginationResponseBody<CarModel>>> {
    const page = await this.carModelService.getByQuery(query);

    logger.debug('✅ Car models retrieved successfully', {
      count: page.items.length,
      pagination: page.pagination,
    });

    return OkResponse.fromPage(page);
  }

  async createCarModel(
    createCarModelCommand: CreateCarModelCommand,
  ): Promise<CreatedResponse<StandardResponseBody<CarModel>>> {
    const carModel = createCarModelCommand.toCarModel();

    const result = await this.carModelService.create(carModel);

    logger.info('✅ Car model created successfully', { details: result });

    return CreatedResponse.fromResult(result);
  }

  async getCarModelById(
    carModelId: string,
  ): Promise<OkResponse<StandardResponseBody<CarModel>>> {
    const carModel = await this.carModelService.getById(carModelId);

    logger.debug('✅ Car model retrieved successfully', { id: carModelId });

    return OkResponse.fromResult(carModel);
  }

  async deleteCarModel(
    carModelId: string,
  ): Promise<OkResponse<MessageResponseBody>> {
    const carsOfThisModel: Page<Car> = await this.carService.getByQuery(
      CarQuery.fromCarModelId(carModelId),
    );

    if (carsOfThisModel.pagination.total > 0) {
      throw new CarModelCurrentlyInUseError(carModelId);
    }

    await this.carModelService.delete(carModelId);

    logger.info('✅ Car model deleted successfully', { id: carModelId });

    return OkResponse.fromMessage('The car model has been deleted.');
  }
}
