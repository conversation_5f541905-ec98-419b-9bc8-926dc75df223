import {
  CarModelCreatedEvent,
  CarModelDeletedEvent,
} from '@bluesg-2/event-library';
import { CarModel, CarModelId } from 'src/car-model/car-model.type';
import { CarModelQuery } from 'src/car-model/commands/car-model.query';
import { CarModelRepository } from 'src/car-model/entities/car-model.repository';
import { EventPublisher } from 'src/shared/event.module';
import { Page } from 'src/shared/query/pagination';

export class CarModelService {
  constructor(
    private readonly carModelRepository: CarModelRepository,
    private readonly eventPublisher: EventPublisher,
  ) {}

  async create(carModel: CarModel): Promise<CarModel> {
    const newCarModel = await this.carModelRepository.createOrFail(carModel);

    await this.eventPublisher.send(
      new CarModelCreatedEvent(newCarModel.toEventProps()),
    );

    return newCarModel;
  }

  async getByQuery(query: CarModelQuery): Promise<Page<CarModel>> {
    const { result, total } = await this.carModelRepository.findByProps({
      hrid: query.hrid,
      name: query.name,
      skip: query.skip(),
      take: query.take(),
    });

    return Page.fromQuery(query, result, total);
  }

  async getById(id: string): Promise<CarModel> {
    const entity = await this.carModelRepository.findOneByIdOrFail(id);
    return entity;
  }

  async delete(id: string): Promise<void> {
    const carModelToDelete =
      await this.carModelRepository.findOneByIdOrFail(id);

    await this.carModelRepository.softRemoveById(id);

    await this.eventPublisher.send(
      new CarModelDeletedEvent(carModelToDelete.toEventProps()),
    );
  }

  async existsById(id: CarModelId): Promise<boolean> {
    return this.carModelRepository.existsByPassingProps({ id });
  }
}
