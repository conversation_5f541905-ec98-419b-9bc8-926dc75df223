import axios, { AxiosInstance } from 'axios';
import { DateTime } from 'luxon';
import { CarRentalPaths } from 'src/rental/car-rental-paths';
import {
  CarAvailabilityInfo,
  CarLocation,
  GetCarAvailabilityResponse,
  GetLastLocationsResponse,
} from 'src/rental/car-rental.type';
import { InvocationContext } from 'src/shared/invocation-context';

const CAR_RENTAL_CORRELATION_ID_HEADER_REQUEST = 'x-correlation-id';

export class CarRentalClient {
  private axios: AxiosInstance;

  constructor() {
    this.axios = axios.create();
  }

  getDefaultHeaders() {
    return {
      [CAR_RENTAL_CORRELATION_ID_HEADER_REQUEST]:
        InvocationContext.getCorrelationId(),
    };
  }

  async getCarAvailability(from: DateTime): Promise<CarAvailabilityInfo[]> {
    const url = await CarRentalPaths.getCarAvailability(from);
    const response = await this.axios.get<GetCarAvailabilityResponse>(
      url.toString(),
      {
        headers: this.getDefaultHeaders(),
      },
    );

    if (response.status === 200 && response.data.success) {
      return CarAvailabilityInfo.fromResponse(response.data);
    }

    return [];
  }

  async getLastLocations(from: DateTime): Promise<CarLocation[]> {
    const url = await CarRentalPaths.getLastLocations(from);
    const response = await this.axios.get<GetLastLocationsResponse>(
      url.toString(),
      {
        headers: this.getDefaultHeaders(),
      },
    );

    if (response.status === 200 && response.data.success) {
      return this.filterByLatestLocationAndTransform(response.data.result);
    }

    return [];
  }

  private filterByLatestLocationAndTransform(
    locations: GetLastLocationsResponse['result'],
  ): CarLocation[] {
    const locationsByCarId = new Map<string, CarLocation>();

    for (const rawLocation of locations) {
      const existingLocation = locationsByCarId.get(rawLocation.vulogCarId);

      const location = new CarLocation(
        rawLocation.vulogCarId,
        rawLocation.stationId,
        DateTime.fromISO(rawLocation.locatedAt),
      );

      if (
        !existingLocation ||
        location.locatedAt.toMillis() > existingLocation.locatedAt.toMillis()
      ) {
        locationsByCarId.set(location.vulogCarId, location);
      }
    }

    return Array.from(locationsByCarId.values());
  }
}
