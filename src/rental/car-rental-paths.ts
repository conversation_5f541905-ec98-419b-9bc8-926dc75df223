import { DateTime } from 'luxon';
import { getConfig } from 'src/shared/env';

export class CarRentalPaths {
  static async baseApiUrl(): Promise<string> {
    return getConfig('CRS_BASE_API_URL');
  }

  static async getLastLocations(from: DateTime): Promise<URL> {
    const url = new URL(`${await this.baseApiUrl()}/api/v1/internal/locations`);
    url.searchParams.append('from', from.toISO());
    return url;
  }

  static async getCarAvailability(updatedFrom: DateTime): Promise<URL> {
    const url = new URL(
      `${await this.baseApiUrl()}/api/v1/internal/reservations/recently-updated`,
    );
    url.searchParams.append('updated-from', updatedFrom.toISO());
    return url;
  }
}
