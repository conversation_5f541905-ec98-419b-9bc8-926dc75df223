import { DateTime } from 'luxon';
import { CarAvailability } from 'src/car/car.type';
import { VulogCarId } from 'src/shared/shared.type';

export class CarLocation {
  constructor(
    readonly vulogCarId: string,
    readonly stationId: string,
    readonly locatedAt: DateTime,
  ) {}
}

export interface GetLastLocationsResponse {
  success: boolean;
  correlationId: string;
  result: {
    vulogCarId: string;
    stationId: string;
    locatedAt: string;
  }[];
}

export enum CarRentalReservationStatus {
  Creating = 'CREATING',
  Reserved = 'RESERVED',
  Failed = 'FAILED',
  Cancelled = 'CANCELLED',
  Converted = 'CONVERTED',
  EndTripToReview = 'END_TRIP_TO_REVIEW',
  EndedWaitingForInvoicing = 'ENDED_WAITING_FOR_INVOICING',
  EndTrip = 'END_TRIP',
  Expired = 'EXPIRED',
  Ended = 'ENDED',
}

export interface GetCarAvailabilityResponse {
  success: boolean;
  correlationId: string;
  result: {
    id: string;
    vulogCarId: string;
    status: CarRentalReservationStatus;
    modifiedAt: string;
    createdAt: string;
  }[];
}

export class CarAvailabilityInfo {
  constructor(
    readonly id: string,
    readonly vulogCarId: VulogCarId,
    readonly availability: CarAvailability,
    readonly modifiedAt: DateTime,
    readonly createdAt: DateTime,
  ) {}

  static mapCarRentalReservationStatusToCarAvailability: Record<
    CarRentalReservationStatus,
    CarAvailability
  > = {
    [CarRentalReservationStatus.Creating]: CarAvailability.AVAILABLE,
    [CarRentalReservationStatus.Reserved]: CarAvailability.RESERVED,
    [CarRentalReservationStatus.Failed]: CarAvailability.AVAILABLE,
    [CarRentalReservationStatus.Cancelled]: CarAvailability.AVAILABLE,
    [CarRentalReservationStatus.Converted]: CarAvailability.ON_RENTAL,
    [CarRentalReservationStatus.EndTripToReview]: CarAvailability.AVAILABLE,
    [CarRentalReservationStatus.EndTrip]: CarAvailability.AVAILABLE,
    [CarRentalReservationStatus.Expired]: CarAvailability.AVAILABLE,
    [CarRentalReservationStatus.Ended]: CarAvailability.AVAILABLE,
    [CarRentalReservationStatus.EndedWaitingForInvoicing]:
      CarAvailability.AVAILABLE,
  };

  static fromResponse(
    response: GetCarAvailabilityResponse,
  ): CarAvailabilityInfo[] {
    return response.result.map((result) => {
      const availability =
        CarAvailabilityInfo.mapCarRentalReservationStatusToCarAvailability[
          result.status
        ] ?? CarAvailability.OUT_OF_SERVICE;

      return new CarAvailabilityInfo(
        result.id,
        new VulogCarId(result.vulogCarId),
        availability,
        DateTime.fromISO(result.modifiedAt),
        DateTime.fromISO(result.createdAt),
      );
    });
  }
}
