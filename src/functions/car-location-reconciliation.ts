import middy from '@middy/core';
import { ScheduledEvent } from 'aws-lambda';
import { CarReconciliationModule } from 'src/car-reconciliation/car-reconciliation.module';
import { STANDARD_MIDDLEWARES } from 'src/shared/middlewares/standard.middleware';

export const carLocationReconciliationHandler = async (
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  event: ScheduledEvent,
): Promise<void> => {
  const carReconciliationModule = await CarReconciliationModule.init();

  await carReconciliationModule.carReconciliationController.reconcileCarLocations();
};

export const handler = middy()
  .use(STANDARD_MIDDLEWARES)
  .handler(carLocationReconciliationHandler);
