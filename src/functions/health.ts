import middy from '@middy/core';
import { APIGatewayProxyResult } from 'aws-lambda';
import { getDataSource } from 'src/shared/datasources/datasource';
import { OkResponse, StandardResponseBody } from 'src/shared/http-response';
import { getLogger } from 'src/shared/logging';
import { STANDARD_MIDDLEWARES } from 'src/shared/middlewares/standard.middleware';

export interface HealthResponse {
  status: string;
  migrationResult: string;
}

export const healthHandler = async (): Promise<APIGatewayProxyResult> => {
  const logger = getLogger('healthHandler');

  const dataSource = await getDataSource();

  const pendingMigrations = await dataSource.showMigrations();

  let migrationResult = '';

  if (pendingMigrations) {
    logger.info('Running migrations...');
    migrationResult += 'Running migrations...\n';

    const migrations = await dataSource.runMigrations();
    migrations.forEach(
      (migration) => (migrationResult += `${migration.name}\n`),
    );

    logger.info('Migrations completed successfully.');
  } else {
    logger.info('No pending migrations found.');
    migrationResult = 'No pending migrations found.';
  }

  const response: HealthResponse = {
    status: 'UP',
    migrationResult,
  };

  return new OkResponse(new StandardResponseBody(response)).toGatewayResult();
};

export const handler = middy().use(STANDARD_MIDDLEWARES).handler(healthHandler);
