import middy from '@middy/core';
import httpRouterHandler, { Method, Route } from '@middy/http-router';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { CarAvailabilityApiModule } from 'src/car-availability/car-availability.api.module';
import {
  CarAvailabilityQuery,
  carAvailabilityQuerySchema,
} from 'src/car-availability/dto/car-availability.query';
import { CarApiModule } from 'src/car/car.api.module';
import { CarQuery, queryCarSchema } from 'src/car/dtos/admin/v1/car.query';
import { STANDARD_MIDDLEWARES } from 'src/shared/middlewares/standard.middleware';
import {
  ValidatedApiEvent,
  transformBody,
  transformQuery,
} from 'src/shared/validator';

const getCarAvailabilityHandler = async (
  event: ValidatedApiEvent<CarAvailabilityQuery>,
): Promise<APIGatewayProxyResult> => {
  const carAvailabilityApiModule = await CarAvailabilityApiModule.init();
  const controller = carAvailabilityApiModule.carAvailabilityController;

  return (
    await controller.getCarAvailability(event.validatedRequest)
  ).toGatewayResult();
};

const getCarsHandler = async (
  event: ValidatedApiEvent<CarQuery>,
): Promise<APIGatewayProxyResult> => {
  const carApiModule = await CarApiModule.init();
  const controller = carApiModule.carController;

  return (
    await controller.getAllCars(event.validatedRequest)
  ).toGatewayResult();
};

const routes: Route<APIGatewayProxyEvent, unknown>[] = [
  {
    method: 'POST' as Method,
    path: '/internal/api/v1/availability-map',
    handler: middy()
      .use(transformBody(carAvailabilityQuerySchema, CarAvailabilityQuery))
      .handler(getCarAvailabilityHandler),
  },
  {
    method: 'GET' as Method,
    path: '/internal/api/v1/cars',
    handler: middy()
      .use(transformQuery(queryCarSchema, CarQuery))
      .handler(getCarsHandler),
  },
];

export const handler = middy()
  .use(STANDARD_MIDDLEWARES)
  .handler(httpRouterHandler(routes));
