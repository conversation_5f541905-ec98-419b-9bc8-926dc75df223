import middy from '@middy/core';
import { APIGatewayProxyResult } from 'aws-lambda';
import { STANDARD_MIDDLEWARES } from 'src/shared/middlewares/standard.middleware';
import { ValidatedApiEvent, transformBody } from 'src/shared/validator';
import { vulogEventSchema } from 'src/vg-event/dto/vg-event.dto';
import { VulogEventDto } from 'src/vg-event/dto/vg-event.dto';
import { VgEventApiModule } from 'src/vg-event/vg-event.api.module';

export const vgEventHandler = async (
  event: ValidatedApiEvent<VulogEventDto>,
): Promise<APIGatewayProxyResult> => {
  const vgEventApiModule = await VgEventApiModule.init();

  const vgEventController = vgEventApiModule.vgEventController;

  return (
    await vgEventController.handleEvent(event.validatedRequest)
  ).toGatewayResult();
};

export const handler = middy()
  .use(STANDARD_MIDDLEWARES)
  .use(transformBody(vulogEventSchema, VulogEventDto))
  .handler(vgEventHandler);
