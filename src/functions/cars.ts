import middy from '@middy/core';
import httpRouter<PERSON>and<PERSON>, { Method, Route } from '@middy/http-router';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { PermissionAction } from 'src/auth/authorization/permission';
import { Resource } from 'src/auth/authorization/resource';
import { CarApiModule } from 'src/car/car.api.module';
import { CarQuery, queryCarSchema } from 'src/car/dtos/admin/v1/car.query';
import {
  CreateCarRequestDto,
  createCarRequestSchema,
} from 'src/car/dtos/admin/v1/create-car.request.dto';
import {
  GetCarStateQuery,
  getCarStateQuerySchema,
} from 'src/car/dtos/admin/v1/get-car-state.query';
import {
  UpdateCarRequestDto,
  updateCarRequestSchema,
} from 'src/car/dtos/admin/v1/update-car.request.dto';
import authMiddleware from 'src/shared/middlewares/auth.middleware';
import { STANDARD_MIDDLEWARES } from 'src/shared/middlewares/standard.middleware';
import {
  ValidatedApiEvent,
  transformBody,
  transformPathParams,
  transformQuery,
  transformRequest,
} from 'src/shared/validator';
import { z } from 'zod';

const carIdParamsSchema = z.object({
  carId: z.string().uuid('Please provide correct UUID for Car'),
});

type ICarIdParams = z.infer<typeof carIdParamsSchema>;

const getAllCarsHandler = async (
  event: ValidatedApiEvent<CarQuery>,
): Promise<APIGatewayProxyResult> => {
  const carApiModule = await CarApiModule.init();
  const controller = carApiModule.carController;

  return (
    await controller.getAllCars(event.validatedRequest)
  ).toGatewayResult();
};

const getCarByIdHandler = async (
  event: ValidatedApiEvent<ICarIdParams>,
): Promise<APIGatewayProxyResult> => {
  const carApiModule = await CarApiModule.init();
  const carController = carApiModule.carController;

  return (
    await carController.getCarById(event.validatedRequest.carId)
  ).toGatewayResult();
};

const getCarStateHandler = async (
  event: ValidatedApiEvent<GetCarStateQuery>,
): Promise<APIGatewayProxyResult> => {
  const carApiModule = await CarApiModule.init();
  const controller = carApiModule.carController;

  return (
    await controller.getCarState(event.validatedRequest)
  ).toGatewayResult();
};

export const createCarHandler = async (
  event: ValidatedApiEvent<CreateCarRequestDto>,
): Promise<APIGatewayProxyResult> => {
  const carApiModule = await CarApiModule.init();
  const carController = carApiModule.carController;

  return (
    await carController.createCar(event.validatedRequest)
  ).toGatewayResult();
};

export const deleteCarHandler = async (
  event: ValidatedApiEvent<ICarIdParams>,
): Promise<APIGatewayProxyResult> => {
  const carApiModule = await CarApiModule.init();
  const carController = carApiModule.carController;

  return (
    await carController.deleteCar(event.validatedRequest.carId)
  ).toGatewayResult();
};

export const updateCarHandler = async (
  event: ValidatedApiEvent<UpdateCarRequestDto>,
): Promise<APIGatewayProxyResult> => {
  const carApiModule = await CarApiModule.init();
  const carController = carApiModule.carController;

  return (
    await carController.updateCar(event.validatedRequest)
  ).toGatewayResult();
};

const routes: Route<APIGatewayProxyEvent, unknown>[] = [
  {
    method: 'GET' as Method,
    path: '/admin/api/v1/cars',
    handler: middy()
      .use(authMiddleware(PermissionAction.Read, Resource.Car))
      .use(transformQuery(queryCarSchema, CarQuery))
      .handler(getAllCarsHandler),
  },
  {
    method: 'GET' as Method,
    path: '/admin/api/v1/cars/{carId}',
    handler: middy()
      .use(authMiddleware(PermissionAction.Read, Resource.Car))
      .use(transformPathParams(carIdParamsSchema))
      .handler(getCarByIdHandler),
  },
  {
    method: 'GET' as Method,
    path: '/admin/api/v1/cars/{carId}/state',
    handler: middy()
      .use(authMiddleware(PermissionAction.Read, Resource.Car))
      .use(
        transformRequest(GetCarStateQuery, {
          pathParamsSchema: carIdParamsSchema,
          querySchema: getCarStateQuerySchema,
        }),
      )
      .handler(getCarStateHandler),
  },
  {
    method: 'POST' as Method,
    path: '/admin/api/v1/cars',
    handler: middy()
      .use(authMiddleware(PermissionAction.Create, Resource.Car))
      .use(transformBody(createCarRequestSchema, CreateCarRequestDto))
      .handler(createCarHandler),
  },
  {
    method: 'DELETE' as Method,
    path: '/admin/api/v1/cars/{carId}',
    handler: middy()
      .use(authMiddleware(PermissionAction.Delete, Resource.Car))
      .use(transformPathParams(carIdParamsSchema))
      .handler(deleteCarHandler),
  },
  {
    method: 'PUT' as Method,
    path: '/admin/api/v1/cars/{carId}',
    handler: middy()
      .use(authMiddleware(PermissionAction.Update, Resource.Car))
      .use(
        transformRequest(UpdateCarRequestDto, {
          pathParamsSchema: carIdParamsSchema,
          bodySchema: updateCarRequestSchema,
        }),
      )
      .handler(updateCarHandler),
  },
];

export const handler = middy()
  .use(STANDARD_MIDDLEWARES)
  .handler(httpRouterHandler(routes));
