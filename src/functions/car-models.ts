import middy from '@middy/core';
import httpRouterHandler, { Method, Route } from '@middy/http-router';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { PermissionAction } from 'src/auth/authorization/permission';
import { Resource } from 'src/auth/authorization/resource';
import { CarModelApiModule } from 'src/car-model/car-model.api.module';
import {
  CarModelQuery,
  queryCarModelSchema,
} from 'src/car-model/commands/car-model.query';
import {
  CreateCarModelCommand,
  createCarModelSchema,
} from 'src/car-model/commands/create-car-model.dto';
import authMiddleware from 'src/shared/middlewares/auth.middleware';
import { STANDARD_MIDDLEWARES } from 'src/shared/middlewares/standard.middleware';
import {
  ValidatedApiEvent,
  transformBody,
  transformPathParams,
  transformQuery,
} from 'src/shared/validator';
import { z } from 'zod';

const carModelIdParamsSchema = z.object({
  carModelId: z.string().uuid('Please provide correct UUID for Car Model'),
});

type ICarModelIdParams = z.infer<typeof carModelIdParamsSchema>;

const getAllCarModelsHandler = async (
  event: ValidatedApiEvent<CarModelQuery>,
): Promise<APIGatewayProxyResult> => {
  const carModelApiModule = await CarModelApiModule.init();
  const controller = carModelApiModule.carModelController;

  return (
    await controller.getAllCarModels(event.validatedRequest)
  ).toGatewayResult();
};

const createCarModelHandler = async (
  event: ValidatedApiEvent<CreateCarModelCommand>,
): Promise<APIGatewayProxyResult> => {
  const carModelApiModule = await CarModelApiModule.init();
  const controller = carModelApiModule.carModelController;

  return (
    await controller.createCarModel(event.validatedRequest)
  ).toGatewayResult();
};

const getCarModelByIdHandler = async (
  event: ValidatedApiEvent<ICarModelIdParams>,
): Promise<APIGatewayProxyResult> => {
  const carModelApiModule = await CarModelApiModule.init();
  const controller = carModelApiModule.carModelController;

  return (
    await controller.getCarModelById(event.validatedRequest.carModelId)
  ).toGatewayResult();
};

const deleteCarModelHandler = async (
  event: ValidatedApiEvent<ICarModelIdParams>,
): Promise<APIGatewayProxyResult> => {
  const carModelApiModule = await CarModelApiModule.init();
  const controller = carModelApiModule.carModelController;

  return (
    await controller.deleteCarModel(event.validatedRequest.carModelId)
  ).toGatewayResult();
};

const routes: Route<APIGatewayProxyEvent, unknown>[] = [
  {
    method: 'GET' as Method,
    path: '/admin/api/v1/car-models',
    handler: middy()
      .use(authMiddleware(PermissionAction.Read, Resource.CarModel))
      .use(transformQuery(queryCarModelSchema, CarModelQuery))
      .handler(getAllCarModelsHandler),
  },
  {
    method: 'POST' as Method,
    path: '/admin/api/v1/car-models',
    handler: middy()
      .use(authMiddleware(PermissionAction.Create, Resource.CarModel))
      .use(transformBody(createCarModelSchema, CreateCarModelCommand))
      .handler(createCarModelHandler),
  },
  {
    method: 'GET' as Method,
    path: '/admin/api/v1/car-models/{carModelId}',
    handler: middy()
      .use(authMiddleware(PermissionAction.Read, Resource.CarModel))
      .use(transformPathParams(carModelIdParamsSchema))
      .handler(getCarModelByIdHandler),
  },
  {
    method: 'DELETE' as Method,
    path: '/admin/api/v1/car-models/{carModelId}',
    handler: middy()
      .use(authMiddleware(PermissionAction.Delete, Resource.CarModel))
      .use(transformPathParams(carModelIdParamsSchema))
      .handler(deleteCarModelHandler),
  },
];

export const handler = middy()
  .use(STANDARD_MIDDLEWARES)
  .handler(httpRouterHandler(routes));
