import {
  AimaCarEnabledEvent,
  AimaCarPutOutOfServiceEvent,
  Event,
  EventName,
  RentalEndedEvent,
  RentalReservationCancelledEvent,
  RentalReservationCreatedEvent,
  RentalReservationExpiredEvent,
  RentalReservationSwitchedEvent,
  RentalStartedEvent,
} from '@bluesg-2/event-library';
import middy from '@middy/core';
import { SQSEvent } from 'aws-lambda';
import { BluesgEventApiModule } from 'src/bluesg-event/bluesg-event.api.module';
import { BluesgEvent } from 'src/bluesg-event/bluesg-event.type';
import { getErrorFormat } from 'src/shared/errors/general.error';
import { InvocationContext } from 'src/shared/invocation-context';
import { getLogger } from 'src/shared/logging';
import { BLUESG_EVENT_MIDDLEWARES } from 'src/shared/middlewares/bluesg-event.middleware';

const logger = getLogger('bluesgEventHandler');

export const bluesgEventHandler = async (event: SQSEvent): Promise<void> => {
  const bluesgEventApiModule = await BluesgEventApiModule.init();
  const bluesgEventController = bluesgEventApiModule.bluesgEventController;

  const events = event.Records.map((record) =>
    BluesgEvent.fromSQSEventRecord(record),
  );

  const eventHandlers: Partial<
    Record<EventName, (event: BluesgEvent) => Promise<void>>
  > = {
    [EventName.RentalEndedEvent]: (event: BluesgEvent) =>
      bluesgEventController.handleRentalEndedEvent(
        event.getBodyAsBluesgEvent(RentalEndedEvent),
      ),
    [EventName.RentalStartedEvent]: (event: BluesgEvent) =>
      bluesgEventController.handleRentalStartedEvent(
        event.getBodyAsBluesgEvent(RentalStartedEvent),
      ),
    [EventName.RentalReservationCreatedEvent]: (event: BluesgEvent) =>
      bluesgEventController.handleReservationCreatedEvent(
        event.getBodyAsBluesgEvent(RentalReservationCreatedEvent),
      ),
    [EventName.RentalReservationCancelledEvent]: (event: BluesgEvent) =>
      bluesgEventController.handleReservationCancelledEvent(
        event.getBodyAsBluesgEvent(RentalReservationCancelledEvent),
      ),
    [EventName.RentalReservationSwitchedEvent]: (event: BluesgEvent) =>
      bluesgEventController.handleReservationSwitchedEvent(
        event.getBodyAsBluesgEvent(RentalReservationSwitchedEvent),
      ),
    [EventName.RentalReservationExpiredEvent]: (event: BluesgEvent) =>
      bluesgEventController.handleReservationExpiredEvent(
        event.getBodyAsBluesgEvent(RentalReservationExpiredEvent),
      ),
    [EventName.AimaCarEnabled]: (event: BluesgEvent) =>
      bluesgEventController.handleCarEnabledEvent(
        event.getBodyAsBluesgEvent(AimaCarEnabledEvent),
      ),
    [EventName.AimaCarPutOutOfService]: (event: BluesgEvent) =>
      bluesgEventController.handleCarPutOutOfServiceEvent(
        event.getBodyAsBluesgEvent(AimaCarPutOutOfServiceEvent),
      ),
  };

  for (const event of events) {
    let eventCorrelationId;
    try {
      const bluesgEvent: Event = event.getBodyAsBluesgEvent(Event);

      eventCorrelationId = bluesgEvent.correlationId;
      if (eventCorrelationId) {
        InvocationContext.pushCorrelationId(eventCorrelationId);
      }

      logger.info(`Processing ${bluesgEvent.event} event`, {
        bluesgEvent,
      });

      const handler = eventHandlers[bluesgEvent.event];
      if (handler) {
        await handler(event);
      } else {
        logger.warn('Unknown event', { event: bluesgEvent });
      }
    } catch (error) {
      logger.error('Error processing event', {
        event,
        error: getErrorFormat(error),
      });
    } finally {
      if (eventCorrelationId) {
        InvocationContext.popCorrelationId();
      }
    }
  }
};

export const handler = middy()
  .use(BLUESG_EVENT_MIDDLEWARES)
  .handler(bluesgEventHandler);
