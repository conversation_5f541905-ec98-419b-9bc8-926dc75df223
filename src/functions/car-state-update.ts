import middy from '@middy/core';
import { ScheduledEvent } from 'aws-lambda';
import { STANDARD_MIDDLEWARES } from 'src/shared/middlewares/standard.middleware';
import { VulogCarModule } from 'src/vulog/vulog-car.module';

export const carStateUpdateHandler = async (
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  event: ScheduledEvent,
): Promise<void> => {
  const vulogCarModule = await VulogCarModule.init();

  await vulogCarModule.vulogCarStateController.updateAllCarStates();
};

export const handler = middy()
  .use(STANDARD_MIDDLEWARES)
  .handler(carStateUpdateHandler);
