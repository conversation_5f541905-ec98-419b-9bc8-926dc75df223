import { IConfigCatClient, getClient } from 'configcat-node';

const ACCESS_CONTROL_VARIABLE_NAME = 'rolesToLegacyPermissionsMap';

export class AuthFeatureFlag {
  private readonly client: IConfigCatClient;

  constructor(authenticationConfigKey?: string) {
    if (authenticationConfigKey) {
      this.client = getClient(authenticationConfigKey);
    }
  }

  async getAccessControl(): Promise<Record<string, string[]>> {
    if (!this.client) {
      return {};
    }

    const configVal = await this.client.getValueAsync<string>(
      ACCESS_CONTROL_VARIABLE_NAME,
      JSON.stringify({}),
    );

    return JSON.parse(configVal);
  }
}
