import { AuthFeatureFlag } from 'src/auth/auth.feature-flag';
import { AuthServiceAdapter } from 'src/auth/auth.service.adapter';
import { AuthenticationService } from 'src/auth/authentication/authentication.service';
import { AuthConfigOptions } from 'src/auth/config/auth.config';
import { getConfig } from 'src/shared/env';

export class AuthModule {
  private static module: AuthModule;

  constructor(readonly adapter: AuthServiceAdapter) {}

  static async init(): Promise<AuthModule> {
    if (!this.module) {
      this.module = await this.buildModule(
        new AuthFeatureFlag(await getConfig('CONFIG_CAT_AUTH_SDK_KEY')),
      );
    }

    return this.module;
  }

  protected static async buildModule(
    featureFlag: AuthFeatureFlag,
  ): Promise<AuthModule> {
    const secret = await getConfig('JWT_SECRET');
    const auth0Server = await getConfig('AUTH0_SERVER_URL');

    const options = new AuthConfigOptions(secret, auth0Server);

    return new AuthModule(
      new AuthServiceAdapter(
        new AuthenticationService(options),
        featureFlag,
        options,
      ),
    );
  }

  protected static setModule(module: AuthModule): void {
    this.module = module;
  }
}
