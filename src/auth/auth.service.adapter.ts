import { APIGatewayEvent } from 'aws-lambda';
import { AuthFeatureFlag } from 'src/auth/auth.feature-flag';
import { AuthenticationService } from 'src/auth/authentication/authentication.service';
import { TokenPayload } from 'src/auth/authentication/token';
import { Auth0TokenParser } from 'src/auth/authorization/auth0.token-parser';
import { Permission } from 'src/auth/authorization/permission';
import { ITokenParser } from 'src/auth/authorization/token-payload-parser';
import { TokenV1Parser } from 'src/auth/authorization/token-v1-parser.util';
import { AuthConfigOptions } from 'src/auth/config/auth.config';
import {
  ForbiddenError,
  UNAUTHORIZED_CODE,
  UNAUTHORIZED_MESSAGE,
  UnauthorizedError,
} from 'src/shared/errors/http.error';
import { InvocationContext } from 'src/shared/invocation-context';

export const AUTHORIZATION_HEADER = 'authorization';

export class AuthServiceAdapter {
  private parsers: ITokenParser<TokenPayload>[];

  constructor(
    private authService: AuthenticationService,
    flags: AuthFeatureFlag,
    options: AuthConfigOptions,
  ) {
    this.parsers = [new Auth0TokenParser(options, flags), new TokenV1Parser()];
  }

  private getParser(token: TokenPayload): ITokenParser<TokenPayload> {
    return this.parsers.find((parser) => parser.isValid(token));
  }

  async authorizeRequest(
    event: APIGatewayEvent,
    permission: Permission,
  ): Promise<void> {
    if (!event.headers[AUTHORIZATION_HEADER]) {
      throw new UnauthorizedError(
        UNAUTHORIZED_CODE,
        UNAUTHORIZED_MESSAGE,
        'Authorization header is missing',
      );
    }

    const token = this.extractToken(event);
    const verifiedToken = await this.authService.verifyToken(token);

    if (!verifiedToken.payload) {
      throw new UnauthorizedError(
        UNAUTHORIZED_CODE,
        UNAUTHORIZED_MESSAGE,
        'Token payload is missing',
      );
    }

    const tokenParser = this.getParser(verifiedToken.payload);

    if (!tokenParser) {
      throw new UnauthorizedError(
        UNAUTHORIZED_CODE,
        UNAUTHORIZED_MESSAGE,
        'Token does not match any parser',
      );
    }

    const hasPermission = await tokenParser.hasAccess(
      verifiedToken.payload,
      permission,
    );
    if (!hasPermission) {
      throw new ForbiddenError();
    }

    InvocationContext.setToken(verifiedToken);
  }

  private extractToken(event: APIGatewayEvent): string {
    return event.headers[AUTHORIZATION_HEADER].replaceAll(/Bearer /g, '');
  }
}
