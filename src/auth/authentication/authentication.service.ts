import jwt, { Jwt<PERSON>ead<PERSON>, SigningKeyCallback } from 'jsonwebtoken';
import {
  CertSigningKey,
  JwksClient,
  RsaSigningKey,
  SigningKey,
} from 'jwks-rsa';
import jwks from 'jwks-rsa';
import { Token, TokenPayload } from 'src/auth/authentication/token';
import { AuthConfigOptions } from 'src/auth/config/auth.config';
import {
  UNAUTHORIZED_CODE,
  UNAUTHORIZED_MESSAGE,
  UnauthorizedError,
} from 'src/shared/errors/http.error';
import { getLogger } from 'src/shared/logging';

const logger = getLogger('authentication.service');

export class AuthenticationService {
  private readonly jwksClient: JwksClient;

  constructor(private options: AuthConfigOptions) {
    if (options.isJwksEnabled()) {
      this.jwksClient = jwks({
        jwksUri: options.jwksJsonUri(),
      });
    }
  }

  verifyToken(token: string): Promise<Token> {
    return new Promise((resolve, reject) => {
      jwt.verify(
        token,
        (header, callback) => this.getSecretKey(header, callback),
        { complete: true },
        (err, decoded) => {
          if (err) {
            logger.error(err);
            return reject(
              new UnauthorizedError(
                UNAUTHORIZED_CODE,
                UNAUTHORIZED_MESSAGE,
                err.message,
              ),
            );
          }
          const { payload, signature } = decoded;
          resolve({ payload: payload as TokenPayload, signature });
        },
      );
    });
  }

  private getSecretKey(header: JwtHeader, callback: SigningKeyCallback): void {
    if (header.alg === 'HS256') {
      return callback(null, this.options.secret);
    }

    if (!this.jwksClient || !header.kid) {
      return callback(null, this.options.secret);
    }

    this.jwksClient.getSigningKey(header.kid, (err, key: SigningKey) => {
      if (err) {
        return callback(err);
      }

      callback(
        null,
        (key as CertSigningKey).publicKey ||
          (key as RsaSigningKey).rsaPublicKey,
      );
    });
  }
}
