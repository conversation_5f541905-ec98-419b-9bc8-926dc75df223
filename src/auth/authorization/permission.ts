import { Resource } from 'src/auth/authorization/resource';

export class Permission {
  constructor(
    readonly resource: Resource,
    readonly action: PermissionAction,
  ) {}

  toTokenFormat(): string {
    return `${this.action}:${this.resource}`;
  }
}

export enum PermissionAction {
  Create = 'C',
  Read = 'R',
  Delete = 'D',
  Update = 'U',
  All = 'all',
}

export enum DenotedPermissionAction {
  Create = 'C',
  Read = 'R',
  Delete = 'D',
  Update = 'U',
  All = '*',
}

export const GRAND_ADMIN_ACCESS_RESOURCE = 'all';
