import { TokenPayload } from 'src/auth/authentication/token';
import {
  DenotedPermissionAction,
  GRAND_ADMIN_ACCESS_RESOURCE,
  Permission,
  PermissionAction,
} from 'src/auth/authorization/permission';
import { ITokenParser } from 'src/auth/authorization/token-payload-parser';

export interface TokenV1 extends TokenPayload {
  v: number;
  permissions: Record<string, string>;
}

export class TokenV1Parser implements ITokenParser<TokenV1> {
  isValid(payload: TokenV1): boolean {
    return payload['v'] === 1;
  }

  async hasAccess(payload: TokenV1, permission: Permission): Promise<boolean> {
    const denotedAction = getDenotedPermission(permission.action);
    return Object.keys(payload.permissions).some((resourceAsKey) => {
      const fullStringGrantedPermissions = payload.permissions[resourceAsKey];
      const grantedPermissions = fullStringGrantedPermissions.split('');
      if (
        resourceAsKey !== permission.resource &&
        resourceAsKey !== GRAND_ADMIN_ACCESS_RESOURCE
      ) {
        return false;
      }

      if (
        !grantedPermissions.includes(denotedAction) &&
        !grantedPermissions.includes(DenotedPermissionAction.All) &&
        !fullStringGrantedPermissions.includes(PermissionAction.All)
      ) {
        return false;
      }
      return true;
    });
  }
}

export function getDenotedPermission(permission: string) {
  return permission.split('')[0].toUpperCase();
}
