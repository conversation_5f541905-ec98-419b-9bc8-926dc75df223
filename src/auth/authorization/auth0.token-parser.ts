import { AuthFeatureFlag } from 'src/auth/auth.feature-flag';
import { TokenPayload } from 'src/auth/authentication/token';
import {
  Permission,
  PermissionAction,
} from 'src/auth/authorization/permission';
import { ITokenParser } from 'src/auth/authorization/token-payload-parser';
import { AuthConfigOptions } from 'src/auth/config/auth.config';

const AUTH0_ROLE_CUSTOM_CLAIM = 'https://bo-api.blusg.com/roles';

export interface Auth0Token extends TokenPayload {
  iss: string;
  sub: string;
  aud: string[];
  scope?: string;
  iat: number;
  exp: number;
  org_id: string;
  azp: string;
  permissions: string[];
  [key: `${string}/roles`]: string[];
}

export class Auth0TokenParser implements ITokenParser<Auth0Token> {
  constructor(
    private readonly options: AuthConfigOptions,
    private readonly flags: AuthFeatureFlag,
  ) {}

  isValid(payload: Auth0Token): boolean {
    return payload.iss === `${this.options.auth0Server}/`;
  }

  async hasAccess(
    payload: Auth0Token,
    permission: Permission,
  ): Promise<boolean> {
    const requiredPermission = permission.toTokenFormat();
    const rolesToLegacyPermissions = await this.flags.getAccessControl();
    const permissions: string[] = payload[AUTH0_ROLE_CUSTOM_CLAIM]?.flatMap(
      (role) => rolesToLegacyPermissions[role] || [],
    );
    return (
      (permissions &&
        permissions.findIndex((perm) =>
          [
            `${PermissionAction.All}:all`,
            `${permission.action}:all`,
            `${PermissionAction.All}:${permission.resource}`,
            requiredPermission,
          ].includes(perm),
        ) > -1) ||
      payload.permissions.includes(requiredPermission)
    );
  }
}
