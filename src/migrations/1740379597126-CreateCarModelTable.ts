import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateCarModelTable1740379597126 implements MigrationInterface {
  name = 'CreateCarModelTable1740379597126';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";');

    await queryRunner.query(
      `CREATE TABLE "car_model" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "hrid" character varying(256) NOT NULL, "name" text NOT NULL, "description" text NOT NULL, CONSTRAINT "UQ_cd6752ca853f002b65b1d8dbd5e" UNIQUE ("hrid"), CONSTRAINT "PK_525071eea12c671d67e35a5cbc8" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "car_model"`);
  }
}
