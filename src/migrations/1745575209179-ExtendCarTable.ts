import { MigrationInterface, QueryRunner } from 'typeorm';

export class ExtendCarTable1745575209179 implements MigrationInterface {
  name = 'ExtendCarTable1745575209179';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "car" ADD "availability_updated_at" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "car"."availability_updated_at" IS 'When the availability status of the car was last updated'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `COMMENT ON COLUMN "car"."availability_updated_at" IS 'When the car availability was updated'`,
    );
    await queryRunner.query(
      `ALTER TABLE "car" DROP COLUMN "availability_updated_at"`,
    );
  }
}
