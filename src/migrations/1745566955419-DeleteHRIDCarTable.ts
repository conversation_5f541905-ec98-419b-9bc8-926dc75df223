import { MigrationInterface, QueryRunner } from 'typeorm';

export class DeleteHRIDCarTable1745566955419 implements MigrationInterface {
  name = 'DeleteHRIDCarTable1745566955419';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "car" DROP CONSTRAINT "UQ_c09870c11d19d64bfacb1e06b46"`,
    );
    await queryRunner.query(`ALTER TABLE "car" DROP COLUMN "hrid"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "car" ADD "hrid" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "car" ADD CONSTRAINT "UQ_c09870c11d19d64bfacb1e06b46" UNIQUE ("hrid")`,
    );
  }
}
