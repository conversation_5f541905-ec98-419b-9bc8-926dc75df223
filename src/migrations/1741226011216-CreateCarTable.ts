import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateCarTable1741226011216 implements MigrationInterface {
  name = 'CreateCarTable1741226011216';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "car" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "created_by" text, "modified_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "modified_by" text, "deleted_at" TIMESTAMP WITH TIME ZONE, "deleted_by" text, "hrid" character varying NOT NULL, "car_model_id" uuid NOT NULL, "plate_number" character varying NOT NULL, "vin" character varying NOT NULL, "registration_date" TIMESTAMP WITH TIME ZONE, CONSTRAINT "UQ_c09870c11d19d64bfacb1e06b46" UNIQUE ("hrid"), CONSTRAINT "PK_55bbdeb14e0b1d7ab417d11ee6d" PRIMARY KEY ("id")); COMMENT ON COLUMN "car"."created_at" IS 'Auto-generated by ORM'; COMMENT ON COLUMN "car"."modified_at" IS 'Auto-modified by ORM'; COMMENT ON COLUMN "car"."deleted_at" IS 'Auto-generated by ORM'`,
    );
    await queryRunner.query(
      `ALTER TABLE "car_model" DROP CONSTRAINT "UQ_cd6752ca853f002b65b1d8dbd5e"`,
    );
    await queryRunner.query(`ALTER TABLE "car_model" DROP COLUMN "hrid"`);
    await queryRunner.query(
      `ALTER TABLE "car_model" ADD "hrid" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "car_model" ADD CONSTRAINT "UQ_cd6752ca853f002b65b1d8dbd5e" UNIQUE ("hrid")`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "car_model"."hrid" IS 'User reference of the car model'`,
    );
    await queryRunner.query(
      `ALTER TABLE "car" ADD CONSTRAINT "FK_09acf549ea240bed91b24fc120e" FOREIGN KEY ("car_model_id") REFERENCES "car_model"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "car" DROP CONSTRAINT "FK_09acf549ea240bed91b24fc120e"`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "car_model"."hrid" IS 'User reference of the car model'`,
    );
    await queryRunner.query(
      `ALTER TABLE "car_model" DROP CONSTRAINT "UQ_cd6752ca853f002b65b1d8dbd5e"`,
    );
    await queryRunner.query(`ALTER TABLE "car_model" DROP COLUMN "hrid"`);
    await queryRunner.query(
      `ALTER TABLE "car_model" ADD "hrid" character varying(256) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "car_model" ADD CONSTRAINT "UQ_cd6752ca853f002b65b1d8dbd5e" UNIQUE ("hrid")`,
    );
    await queryRunner.query(`DROP TABLE "car"`);
  }
}
