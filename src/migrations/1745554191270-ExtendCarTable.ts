import { MigrationInterface, QueryRunner } from 'typeorm';

export class ExtendCarTable1745554191270 implements MigrationInterface {
  name = 'ExtendCarTable1745554191270';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "car" ADD "availability" character varying`,
    );
    await queryRunner.query(`UPDATE "car" SET "availability" = 'AVAILABLE'`);
    await queryRunner.query(
      `ALTER TABLE "car" ALTER COLUMN "availability" SET NOT NULL`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "car"."availability" IS 'The availability of the car'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "car" DROP COLUMN "availability"`);
  }
}
