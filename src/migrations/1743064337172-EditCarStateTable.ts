import { MigrationInterface, QueryRunner } from 'typeorm';

export class EditCarStateTable1743064337172 implements MigrationInterface {
  name = 'EditCarStateTable1743064337172';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE UNIQUE INDEX IF NOT EXISTS "idx_unique_car_state_car_id" ON "car_state" ("car_id")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX IF EXISTS "public"."idx_unique_car_state_car_id"`,
    );
  }
}
