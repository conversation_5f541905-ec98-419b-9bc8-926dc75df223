import { MigrationInterface, QueryRunner } from 'typeorm';

export class MakeDescriptionNullableCarModelTable1740562621855
  implements MigrationInterface
{
  name = 'MakeDescriptionNullableCarModelTable1740562621855';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "car_model" ALTER COLUMN "description" DROP NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "car_model" ALTER COLUMN "description" SET NOT NULL`,
    );
  }
}
