import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAuditableColumns1741157655903 implements MigrationInterface {
  name = 'AddAuditableColumns1741157655903';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "car_model" ADD "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "car_model"."created_at" IS 'Auto-generated by ORM'`,
    );
    await queryRunner.query(`ALTER TABLE "car_model" ADD "created_by" text`);
    await queryRunner.query(
      `ALTER TABLE "car_model" ADD "modified_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "car_model"."modified_at" IS 'Auto-modified by ORM'`,
    );
    await queryRunner.query(`ALTER TABLE "car_model" ADD "modified_by" text`);
    await queryRunner.query(
      `ALTER TABLE "car_model" ADD "deleted_at" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "car_model"."deleted_at" IS 'Auto-generated by ORM'`,
    );
    await queryRunner.query(`ALTER TABLE "car_model" ADD "deleted_by" text`);
    await queryRunner.query(
      `COMMENT ON COLUMN "car_model"."hrid" IS 'User reference of the car model'`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "car_model"."name" IS 'Name of the car model'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`COMMENT ON COLUMN "car_model"."name" IS NULL`);
    await queryRunner.query(`COMMENT ON COLUMN "car_model"."hrid" IS NULL`);
    await queryRunner.query(`ALTER TABLE "car_model" DROP COLUMN "deleted_by"`);
    await queryRunner.query(
      `COMMENT ON COLUMN "car_model"."deleted_at" IS 'Auto-generated by ORM'`,
    );
    await queryRunner.query(`ALTER TABLE "car_model" DROP COLUMN "deleted_at"`);
    await queryRunner.query(
      `ALTER TABLE "car_model" DROP COLUMN "modified_by"`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "car_model"."modified_at" IS 'Auto-modified by ORM'`,
    );
    await queryRunner.query(
      `ALTER TABLE "car_model" DROP COLUMN "modified_at"`,
    );
    await queryRunner.query(`ALTER TABLE "car_model" DROP COLUMN "created_by"`);
    await queryRunner.query(
      `COMMENT ON COLUMN "car_model"."created_at" IS 'Auto-generated by ORM'`,
    );
    await queryRunner.query(`ALTER TABLE "car_model" DROP COLUMN "created_at"`);
  }
}
