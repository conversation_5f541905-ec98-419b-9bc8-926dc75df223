import { MigrationInterface, QueryRunner } from 'typeorm';

export class ExtendCarTable1744701308579 implements MigrationInterface {
  name = 'ExtendCarTable1744701308579';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "car" ADD "station_id" uuid`);
    await queryRunner.query(
      `COMMENT ON COLUMN "car"."station_id" IS 'The station where the car is located'`,
    );
    await queryRunner.query(
      `ALTER TABLE "car" ADD "located_at" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "car"."located_at" IS 'When the car was located at the station'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "car" DROP COLUMN "located_at"`);

    await queryRunner.query(`ALTER TABLE "car" DROP COLUMN "station_id"`);
  }
}
