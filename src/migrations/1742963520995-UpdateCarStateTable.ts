import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCarStateTable1742963520995 implements MigrationInterface {
  name = 'UpdateCarStateTable1742963520995';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "car_state" DROP CONSTRAINT "FK_dbe5603cf06fb7ed6b983cc552d"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "car_state" ADD CONSTRAINT "FK_dbe5603cf06fb7ed6b983cc552d" FOREIGN KEY ("car_id") REFERENCES "car"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
