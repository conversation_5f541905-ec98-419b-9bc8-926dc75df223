import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateCarStateTable1742874307435 implements MigrationInterface {
  name = 'CreateCarStateTable1742874307435';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "car_state" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "created_by" text, "modified_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "modified_by" text, "deleted_at" TIMESTAMP WITH TIME ZONE, "deleted_by" text, "car_id" uuid NOT NULL, "locked" boolean, "ev_battery_level" numeric, "all_doors_windows_closed" boolean, "doors" json, "windows" json, "engine_on" boolean, "cable_plugged" boolean, "charging" boolean, "stations" json, "provider_payload" json NOT NULL, CONSTRAINT "PK_e9fe8e3a9423dca04e8f0e88b60" PRIMARY KEY ("id")); COMMENT ON COLUMN "car_state"."created_at" IS 'Auto-generated by ORM'; COMMENT ON COLUMN "car_state"."modified_at" IS 'Auto-modified by ORM'; COMMENT ON COLUMN "car_state"."deleted_at" IS 'Auto-generated by ORM'; COMMENT ON COLUMN "car_state"."car_id" IS 'This column refers to column \`id\` of table \`car\` as a foreign key'; COMMENT ON COLUMN "car_state"."locked" IS 'This column describes whether the car is locked or not'; COMMENT ON COLUMN "car_state"."ev_battery_level" IS 'This column describes the battery level of the vehicle. Although "battery" in English is actually French "accumulateurs" or Vietnamese "acquy", it does not share the same meaning with the column'; COMMENT ON COLUMN "car_state"."all_doors_windows_closed" IS 'This column describes whether the car has all doors and all windows closed or not'; COMMENT ON COLUMN "car_state"."doors" IS 'This column describes the details of doors of the car'; COMMENT ON COLUMN "car_state"."windows" IS 'This column describes the details of windows of the car'; COMMENT ON COLUMN "car_state"."engine_on" IS 'This column describes whether the engine of the car is on or not'; COMMENT ON COLUMN "car_state"."cable_plugged" IS 'This column describes whether the car is plugged or not'; COMMENT ON COLUMN "car_state"."charging" IS 'This column describes whether the car is charging or not, given that the car is plugged into the EVSE before'; COMMENT ON COLUMN "car_state"."stations" IS 'This column describes the details of location info of the car. For Vulog Vehicle Gateway, the term is "zones"'; COMMENT ON COLUMN "car_state"."provider_payload" IS 'This column holds the raw payload coming from the provider (as known as vehicle gateway)'`,
    );
    await queryRunner.query(
      `ALTER TABLE "car_state" ADD CONSTRAINT "FK_dbe5603cf06fb7ed6b983cc552d" FOREIGN KEY ("car_id") REFERENCES "car"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "car_state" DROP CONSTRAINT "FK_dbe5603cf06fb7ed6b983cc552d"`,
    );
    await queryRunner.query(`DROP TABLE "car_state"`);
  }
}
