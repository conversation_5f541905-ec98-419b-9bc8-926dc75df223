import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddManagedByColumns1742524879145 implements MigrationInterface {
  name = 'AddManagedByColumns1742524879145';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "car" 
      ADD COLUMN IF NOT EXISTS "managed_by" character varying NOT NULL DEFAULT 'VULOG',
      ADD COLUMN IF NOT EXISTS "external_fleet_id" character varying,
      ADD COLUMN IF NOT EXISTS "external_car_id" character varying;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "car" 
      DROP COLUMN IF EXISTS "managed_by",
      DROP COLUMN IF EXISTS "external_fleet_id",
      DROP COLUMN IF EXISTS "external_car_id";
    `);
  }
}
