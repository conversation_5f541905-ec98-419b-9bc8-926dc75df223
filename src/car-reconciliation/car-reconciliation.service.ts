import { DateTime } from 'luxon';
import { CarAvailabilityService } from 'src/car-availability/car-availability.service';
import { CarReconciliationConfig } from 'src/car-reconciliation/car-reconciliation.config';
import { CarService } from 'src/car/car.service';
import {
  CarAvailabilityUpdateAvailabilityTooOldError,
  CarLocationUpdateLocationTooOldError,
} from 'src/car/errors/car.error';
import { OneCarByExternalIdQuery } from 'src/car/query/car-by-external-id.query';
import { CarRentalClient } from 'src/rental/car-rental-client';
import { getErrorFormat } from 'src/shared/errors/general.error';
import { getLogger } from 'src/shared/logging';
import { VulogCarId } from 'src/shared/shared.type';

const logger = getLogger('car-reconciliation.service');

export class CarReconciliationService {
  constructor(
    private readonly carService: CarService,
    private readonly carRentalClient: CarRentalClient,
    private readonly carAvailabilityService: CarAvailabilityService,
  ) {}

  async reconcileCarAvailability(): Promise<void> {
    const carAvailabilities = await this.carRentalClient.getCarAvailability(
      DateTime.now().minus({
        minutes:
          await CarReconciliationConfig.availabilityReconciliationWindowMinutesConfig(),
      }),
    );

    for (const carAvailability of carAvailabilities) {
      try {
        const car = await this.carService.getOneByProps(
          new OneCarByExternalIdQuery(carAvailability.vulogCarId),
        );

        if (!car) {
          logger.warn(
            `Car not found by vulogCarId: ${carAvailability.vulogCarId}`,
          );
          continue;
        }

        if (car.availability === carAvailability.availability) {
          logger.debug('Skipping car availability update', {
            vulogCarId: carAvailability.vulogCarId.value,
            carAvailability: carAvailability.availability,
            modifiedAt: carAvailability.modifiedAt,
            previousAvailability: car.availability,
            previousAvailabilityUpdatedAt: car.availabilityUpdatedAt,
          });
          continue;
        }

        await this.carAvailabilityService.updateCarAvailability(
          car.id,
          carAvailability.availability,
          carAvailability.modifiedAt,
        );

        logger.info('Updated car availability', {
          vulogCarId: carAvailability.vulogCarId.value,
          carAvailability: carAvailability.availability,
          modifiedAt: carAvailability.modifiedAt,
          previousAvailability: car.availability,
          previousAvailabilityUpdatedAt: car.availabilityUpdatedAt,
        });
      } catch (error) {
        if (error instanceof CarAvailabilityUpdateAvailabilityTooOldError) {
          continue;
        }
        logger.error('Error updating car availability', {
          vulogCarId: carAvailability.vulogCarId.value,
          carAvailability: carAvailability.availability,
          modifiedAt: carAvailability.modifiedAt,
          error: getErrorFormat(error),
        });
      }
    }
  }

  async reconcileCarLocations(): Promise<void> {
    const lastLocations = await this.carRentalClient.getLastLocations(
      DateTime.now().minus({
        minutes:
          await CarReconciliationConfig.locationReconciliationWindowMinutesConfig(),
      }),
    );

    for (const location of lastLocations) {
      try {
        const car = await this.carService.getOneByProps(
          new OneCarByExternalIdQuery(new VulogCarId(location.vulogCarId)),
        );

        if (!car) {
          logger.warn(`Car not found by vulogCarId: ${location.vulogCarId}`);
          continue;
        }

        if (car.stationId === location.stationId) {
          logger.debug('Skipping car location update', {
            vulogCarId: location.vulogCarId,
            stationId: location.stationId,
            locatedAt: location.locatedAt,
            previousStationId: car.stationId,
            previousLocatedAt: car.locatedAt,
          });
          continue;
        }

        await this.carService.updateCarLocation(
          car.id,
          location.stationId,
          location.locatedAt,
        );

        logger.info('Updated car location', {
          vulogCarId: location.vulogCarId,
          stationId: location.stationId,
          locatedAt: location.locatedAt,
          previousStationId: car.stationId,
          previousLocatedAt: car.locatedAt,
        });
      } catch (error) {
        if (error instanceof CarLocationUpdateLocationTooOldError) {
          continue;
        }
        logger.error('Error updating car location', {
          vulogCarId: location.vulogCarId,
          stationId: location.stationId,
          locatedAt: location.locatedAt,
          error: getErrorFormat(error),
        });
      }
    }
  }
}
