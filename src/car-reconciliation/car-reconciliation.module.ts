import { CarAvailabilityModule } from 'src/car-availability/car-availability.module';
import { CarReconciliationController } from 'src/car-reconciliation/car-reconciliation.controller';
import { CarReconciliationService } from 'src/car-reconciliation/car-reconciliation.service';
import { CarModule } from 'src/car/car.module';
import { CarRentalClient } from 'src/rental/car-rental-client';

export class CarReconciliationModule {
  readonly carReconciliationController: CarReconciliationController;
  readonly carReconciliationService: CarReconciliationService;

  constructor(
    carModule: CarModule,
    carRentalClient: CarRentalClient,
    carAvailabilityModule: CarAvailabilityModule,
  ) {
    this.carReconciliationService = new CarReconciliationService(
      carModule.carService,
      carRentalClient,
      carAvailabilityModule.carAvailabilityService,
    );
    this.carReconciliationController = new CarReconciliationController(
      this.carReconciliationService,
    );
  }

  static module: CarReconciliationModule;

  static async init(): Promise<CarReconciliationModule> {
    if (!this.module) {
      const carModule = await CarModule.init();
      const carRentalClient = new CarRentalClient();
      const carAvailabilityModule = await CarAvailabilityModule.init();
      this.module = new CarReconciliationModule(
        carModule,
        carRentalClient,
        carAvailabilityModule,
      );
    }

    return this.module;
  }
}
