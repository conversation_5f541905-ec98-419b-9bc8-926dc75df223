import { CarReconciliationService } from 'src/car-reconciliation/car-reconciliation.service';
import { getLogger } from 'src/shared/logging';

const logger = getLogger('car-reconciliation.controller');

export class CarReconciliationController {
  constructor(
    private readonly carReconciliationService: CarReconciliationService,
  ) {}

  async reconcileCarLocations(): Promise<void> {
    logger.info('Car reconciliation location update started');

    await this.carReconciliationService.reconcileCarLocations();

    logger.info('Car reconciliation location update completed');
  }

  async reconcileCarAvailability(): Promise<void> {
    logger.info('Car reconciliation availability update started');

    await this.carReconciliationService.reconcileCarAvailability();

    logger.info('Car reconciliation availability update completed');
  }
}
