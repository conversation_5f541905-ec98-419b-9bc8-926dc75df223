import { getConfigNumber } from 'src/shared/env';

export class CarReconciliationConfig {
  static async locationReconciliationWindowMinutesConfig(): Promise<number> {
    return getConfigNumber('LOCATION_RECONCILIATION_WINDOW_MINUTES', {
      default: 20,
    });
  }

  static async availabilityReconciliationWindowMinutesConfig(): Promise<number> {
    return getConfigNumber('AVAILABILITY_RECONCILIATION_WINDOW_MINUTES', {
      default: 20,
    });
  }
}
