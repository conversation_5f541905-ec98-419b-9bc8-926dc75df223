import { VulogFleetId } from 'src/shared/shared.type';
import { getVulogConfig } from 'src/vulog/vulog-config';

export class VulogPaths {
  static apiVersionPrefix(): string {
    return 'v2';
  }

  static async auth(fleetId: VulogFleetId): Promise<URL> {
    const baseUrl = await getVulogConfig().getBaseApiUrl();
    return new URL(
      `${baseUrl}/auth/realms/${fleetId.value}/protocol/openid-connect/token`,
    );
  }

  static async vvgVehicles(fleetId: string): Promise<URL> {
    const baseUrl = await getVulogConfig().getBaseApiUrl();
    return new URL(
      `${baseUrl}/${this.apiVersionPrefix()}/fleets/${fleetId}/vehicles`,
    );
  }

  static async vvgVehicleById(
    fleetId: string,
    vehicleId: string,
  ): Promise<URL> {
    const baseUrl = await getVulogConfig().getBaseApiUrl();
    return new URL(
      `${baseUrl}/${this.apiVersionPrefix()}/fleets/${fleetId}/vehicles/${vehicleId}`,
    );
  }
}
