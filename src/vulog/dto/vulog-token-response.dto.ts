import { VulogToken } from 'src/vulog/vulog.type';

export class VulogTokenResponseDto {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  refresh_expires_in: number;

  static toVulogToken(response: VulogTokenResponseDto): VulogToken {
    return new VulogToken(
      response.access_token,
      response.refresh_token,
      response.token_type,
      new Date(Date.now() + response.expires_in * 1000),
      new Date(Date.now() + response.refresh_expires_in * 1000),
    );
  }
}
