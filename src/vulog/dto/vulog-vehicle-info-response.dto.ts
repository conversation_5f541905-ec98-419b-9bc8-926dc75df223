import { DateTime } from 'luxon';
import {
  CarBatteryLevel,
  CarState,
  CarStateId,
} from 'src/car-state/car-state.type';
import { CarId } from 'src/car/car.type';
import { v4 as uuidv4 } from 'uuid';
import { z } from 'zod';

const vulogDoorsSchema = z.object({
  frontLeftClosed: z.boolean().optional().nullable(),
  frontRightClosed: z.boolean().optional().nullable(),
  rearLeftClosed: z.boolean().optional().nullable(),
  rearRightClosed: z.boolean().optional().nullable(),
  trunkClosed: z.boolean().optional().nullable(),
});

const vulogWindowsSchema = z.object({
  frontLeftClosed: z.boolean().optional().nullable(),
  frontRightClosed: z.boolean().optional().nullable(),
  rearLeftClosed: z.boolean().optional().nullable(),
  rearRightClosed: z.boolean().optional().nullable(),
  trunkClosed: z.boolean().optional().nullable(),
});

const vulogStatusSchema = z.object({
  boxStatus: z.string().optional().nullable(),
  energyLevel: z.number().optional().nullable(),
  locked: z.boolean().optional().nullable(),
  doorsAndWindowsClosed: z.boolean().optional().nullable(),
  doors: vulogDoorsSchema.optional().nullable(),
  windows: vulogWindowsSchema.optional().nullable(),
  cablePlugged: z.boolean().optional().nullable(),
  charging: z.boolean().optional().nullable(),
  engineOn: z.boolean().optional().nullable(),
  updateDate: z.coerce.date().optional().nullable(),
});

const vulogZoneSchema = z.object({
  type: z.string().optional().nullable(),
  zoneId: z.string().optional().nullable(),
  sticky: z.boolean().optional().nullable(),
  labels: z.array(z.string()).optional().nullable(),
});

const vulogZonesSchema = z
  .object({
    current: z.array(vulogZoneSchema).optional().nullable(),
  })
  .optional();

// Simplified Vulog response schema with only fields needed for CarStateEntity
export const vulogVehicleInfoResponseSchema = z.object({
  id: z.string(),
  status: vulogStatusSchema.optional().nullable(),
  zones: vulogZonesSchema.optional().nullable(),
});

export type IVulogVehicleInfoResponse = z.infer<
  typeof vulogVehicleInfoResponseSchema
>;

export class VulogVehicleInfoResponseDto implements IVulogVehicleInfoResponse {
  readonly id: string;
  readonly status: z.infer<typeof vulogStatusSchema>;
  readonly zones: z.infer<typeof vulogZonesSchema>;
  readonly rawData: unknown;

  constructor(data: unknown) {
    const validatedData = vulogVehicleInfoResponseSchema.parse(data);
    Object.assign(this, validatedData);
    this.rawData = data;
  }

  toCarState(carId: CarId): CarState {
    return new CarState(
      new CarStateId(uuidv4()),
      carId,
      this.status?.locked,
      this.status?.energyLevel !== undefined
        ? new CarBatteryLevel(this.status.energyLevel)
        : null,
      this.status?.doorsAndWindowsClosed,
      this.status?.doors || null,
      this.status?.windows || null,
      this.status?.engineOn,
      this.status?.cablePlugged,
      this.status?.charging,
      this.zones ? this.zones : null,
      typeof this.rawData === 'object' && this.rawData !== null
        ? this.rawData
        : {},
      DateTime.now(),
      this.status?.updateDate
        ? DateTime.fromJSDate(this.status.updateDate)
        : DateTime.now(),
    );
  }
}
