import { getConfig } from 'src/shared/env';
import { VulogGrantType } from 'src/vulog/dto/vulog-token-request.dto';
import { VulogConfigError } from 'src/vulog/error/vulog-config.error';
import { z } from 'zod';

let config: VulogConfig | null = null;

export function getVulogConfig(): VulogConfig {
  if (!config) {
    config = new VulogConfig();
  }
  return config;
}

class VulogConfig {
  private apiKey: string | null = null;
  private baseApiUrl: string | null = null;
  private clientId: string | null = null;
  private clientSecret: string | null = null;
  private username: string | null = null;
  private password: string | null = null;
  private grantType: VulogGrantType | null = null;

  async getApiKey(): Promise<string> {
    if (!this.apiKey) {
      this.apiKey = await getConfig('VULOG_API_KEY');
    }
    return this.apiKey;
  }

  async getBaseApiUrl(): Promise<string> {
    if (!this.baseApiUrl) {
      this.baseApiUrl = await getConfig('VULOG_BASE_API_URL');
    }
    return this.baseApiUrl;
  }

  async getClientId(): Promise<string> {
    if (!this.clientId) {
      this.clientId = await getConfig('VULOG_CLIENT_ID');
    }
    return this.clientId;
  }

  async getClientSecret(): Promise<string> {
    if (!this.clientSecret) {
      this.clientSecret = await getConfig('VULOG_CLIENT_SECRET');
    }
    return this.clientSecret;
  }

  async getUsername(): Promise<string> {
    if (!this.username) {
      this.username = await getConfig('VULOG_USERNAME');
    }
    return this.username;
  }

  async getPassword(): Promise<string> {
    if (!this.password) {
      this.password = await getConfig('VULOG_PASSWORD');
    }
    return this.password;
  }

  async getGrantType(): Promise<VulogGrantType> {
    if (!this.grantType) {
      const grantTypeConfig = await getConfig('VULOG_GRANT_TYPE');
      const grantType = z.nativeEnum(VulogGrantType).safeParse(grantTypeConfig);
      if (!grantType.success) {
        throw new VulogConfigError(`Invalid grant type: ${grantTypeConfig}`);
      }
      this.grantType = grantType.data;
    }
    return this.grantType;
  }
}
