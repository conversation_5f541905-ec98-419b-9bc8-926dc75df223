export interface DoorsDetails {
  frontLeftClosed?: boolean;
  frontRightClosed?: boolean;
  rearLeftClosed?: boolean;
  rearRightClosed?: boolean;
  trunkClosed?: boolean;
}

export interface WindowsDetails {
  frontLeftClosed?: boolean;
  frontRightClosed?: boolean;
  rearLeftClosed?: boolean;
  rearRightClosed?: boolean;
  trunkClosed?: boolean;
}

export interface ZoneInfo {
  type?: string;
  zoneId?: string;
  sticky?: boolean;
  labels?: string[];
}

export interface StationDetails {
  current?: ZoneInfo[];
}

const TOKEN_EXPIRY_SAFETY_MS = 5000;

export class VulogToken {
  constructor(
    readonly access_token: string,
    readonly refresh_token: string,
    readonly token_type: string,
    readonly expires_at: Date,
    readonly refresh_expires_at: Date,
  ) {}

  isExpired(): boolean {
    return this.expires_at < new Date(Date.now() + TOKEN_EXPIRY_SAFETY_MS);
  }

  isRefreshExpired(): boolean {
    return (
      this.refresh_expires_at < new Date(Date.now() + TOKEN_EXPIRY_SAFETY_MS)
    );
  }
}
