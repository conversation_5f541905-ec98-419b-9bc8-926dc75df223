import axios, { AxiosError, AxiosResponse } from 'axios';
import { CarState } from 'src/car-state/car-state.type';
import { CarId } from 'src/car/car.type';
import { getErrorFormat } from 'src/shared/errors/general.error';
import {
  INTERNAL_SERVER_ERROR_CODE,
  InternalServerError,
} from 'src/shared/errors/http.error';
import { defaultZodErrorMessage } from 'src/shared/errors/zod.error';
import { getLogger } from 'src/shared/logging';
import { VulogCarId } from 'src/shared/shared.type';
import { VulogFleetId } from 'src/shared/shared.type';
import { VulogTokenRequestDto } from 'src/vulog/dto/vulog-token-request.dto';
import { VulogTokenResponseDto } from 'src/vulog/dto/vulog-token-response.dto';
import { VulogVehicleInfoResponseDto } from 'src/vulog/dto/vulog-vehicle-info-response.dto';
import { getVulogConfig } from 'src/vulog/vulog-config';
import { VulogPaths } from 'src/vulog/vulog-paths';
import { VulogToken } from 'src/vulog/vulog.type';
import { z } from 'zod';

const logger = getLogger('vulog.client');

export class VulogClient {
  private tokens: Record<string, VulogToken> = {};

  constructor() {}

  async get(
    fleetId: VulogFleetId,
    url: string,
    headers: Record<string, string> = {},
  ): Promise<AxiosResponse<unknown>> {
    const token = await this.getToken(fleetId);
    try {
      return await axios.get(url, {
        headers: {
          Authorization: `Bearer ${token.access_token}`,
          'X-API-KEY': await getVulogConfig().getApiKey(),
          ...headers,
        },
      });
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        if (error.response?.status === 401 || error.response?.status === 403) {
          throw new InternalServerError(
            INTERNAL_SERVER_ERROR_CODE,
            'Vulog authentication failed!',
          );
        }
      }
      throw error;
    }
  }

  async getVehiclesInfo(
    fleetId: VulogFleetId,
  ): Promise<VulogVehicleInfoResponseDto[]> {
    const url = await VulogPaths.vvgVehicles(fleetId.value);

    const res: VulogVehicleInfoResponseDto[] = [];

    let page = 1;
    let continuationToken = undefined;
    while (page === 1 || continuationToken) {
      logger.debug('Getting VVG Vehicles Info', {
        fleetId: fleetId.value,
        page,
      });

      const pageUrl =
        page === 1 ? url : `${url}?continuationToken=${continuationToken}`;

      try {
        const response = await this.get(fleetId, pageUrl.toString());

        const vehiclesInfoResponse = response.data as {
          content: unknown[];
          continuationToken: string;
        };

        logger.debug('Retrieved VVG Vehicles Info', {
          fleetId: fleetId.value,
          page,
          count: vehiclesInfoResponse.content.length,
        });

        vehiclesInfoResponse.content.forEach((data) =>
          res.push(new VulogVehicleInfoResponseDto(data)),
        );

        continuationToken = vehiclesInfoResponse.continuationToken;
        page++;
      } catch (error: unknown) {
        logger.error('Error getting car info', {
          error: getErrorFormat(error),
        });
        if (error instanceof z.ZodError) {
          throw new InternalServerError(
            INTERNAL_SERVER_ERROR_CODE,
            'Invalid car state response from Vulog: ' +
              defaultZodErrorMessage(error),
          );
        }
        throw error;
      }
    }

    logger.info('Finished retrieving VVG Vehicles Info', {
      fleetId: fleetId.value,
      count: res.length,
    });

    return res;
  }

  async getCarState(
    fleetId: VulogFleetId,
    vehicleId: VulogCarId,
    carId: CarId,
  ): Promise<CarState> {
    const url = await VulogPaths.vvgVehicleById(fleetId.value, vehicleId.value);
    try {
      const response = await this.get(fleetId, url.toString());
      const carState = new VulogVehicleInfoResponseDto(
        response.data,
      ).toCarState(carId);
      return carState;
    } catch (error: unknown) {
      logger.error('Error getting car info', {
        error: getErrorFormat(error),
      });
      if (error instanceof z.ZodError) {
        throw new InternalServerError(
          INTERNAL_SERVER_ERROR_CODE,
          'Invalid car state response from Vulog: ' +
            defaultZodErrorMessage(error),
        );
      }
      throw error;
    }
  }

  async getToken(fleetId: VulogFleetId): Promise<VulogToken> {
    if (this.tokens[fleetId.value] && !this.tokens[fleetId.value].isExpired()) {
      return this.tokens[fleetId.value];
    }

    const url = await VulogPaths.auth(fleetId);

    const params = new URLSearchParams({
      client_id: await getVulogConfig().getClientId(),
      client_secret: await getVulogConfig().getClientSecret(),
      grant_type: await getVulogConfig().getGrantType(),
      username: await getVulogConfig().getUsername(),
      password: await getVulogConfig().getPassword(),
    } as VulogTokenRequestDto);

    const response = await axios.post<VulogTokenResponseDto>(
      url.toString(),
      params.toString(),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
    );

    return VulogTokenResponseDto.toVulogToken(response.data);
  }
}
