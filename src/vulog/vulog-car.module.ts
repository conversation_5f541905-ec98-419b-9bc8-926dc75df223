import { CarStateModule } from 'src/car-state/car-state.module';
import { VulogCarStateService } from 'src/car-state/vulog-car-state.service';
import { VulogCarStateController } from 'src/vulog/vulog-car-state.controller';

export class VulogCarModule {
  readonly vulogCarStateController: VulogCarStateController;

  constructor(public readonly vulogCarStateService: VulogCarStateService) {
    this.vulogCarStateController = new VulogCarStateController(
      this.vulogCarStateService,
    );
  }

  static module: VulogCarModule;

  static async init(): Promise<VulogCarModule> {
    if (!this.module) {
      const carStateModule = await CarStateModule.init();
      this.module = new VulogCarModule(carStateModule.vulogCarStateService);
    }

    return this.module;
  }
}
