import { VulogCarStateService } from 'src/car-state/vulog-car-state.service';
import { getLogger } from 'src/shared/logging';

const logger = getLogger('vulog-car-state.controller');

export class VulogCarStateController {
  constructor(private readonly vulogCarStateService: VulogCarStateService) {}

  async updateAllCarStates(): Promise<void> {
    logger.info('Update all car states started');

    await this.vulogCarStateService.updateAllCarStates();

    logger.info('Update all car states completed');
  }
}
