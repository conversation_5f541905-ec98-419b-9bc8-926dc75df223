import { CarAvailabilityService } from 'src/car-availability/car-availability.service';
import { CarAvailabilityRepository } from 'src/car-availability/entities/car-availability.repository';
import { CarRepository } from 'src/car/entities/car.repository';
import { getDataSource } from 'src/shared/datasources/datasource';
import { EventModule } from 'src/shared/event.module';
import { FeatureFlagModule } from 'src/shared/feature-flag.module';
import { DataSource } from 'typeorm';

export class CarAvailabilityModule {
  readonly carAvailabilityService: CarAvailabilityService;
  private readonly carAvailabilityRepository: CarAvailabilityRepository;
  private readonly carRepository: CarRepository;

  constructor(
    dataSource: DataSource,
    eventModule: EventModule,
    featureFlagModule: FeatureFlagModule,
  ) {
    this.carAvailabilityRepository = new CarAvailabilityRepository(dataSource);
    this.carRepository = new CarRepository(dataSource);
    this.carAvailabilityService = new CarAvailabilityService(
      this.carAvailabilityRepository,
      this.carRepository,
      eventModule.eventPublisher,
      featureFlagModule.featureFlag,
    );
  }

  static module: CarAvailabilityModule;

  static async init(): Promise<CarAvailabilityModule> {
    if (!this.module) {
      this.module = new CarAvailabilityModule(
        await getDataSource(),
        await EventModule.init(),
        await FeatureFlagModule.init(),
      );
    }

    return this.module;
  }
}
