import { CarAvailabilityController } from 'src/car-availability/car-availability.controller';
import { CarAvailabilityModule } from 'src/car-availability/car-availability.module';

export class CarAvailabilityApiModule {
  readonly carAvailabilityController: CarAvailabilityController;

  constructor(carAvailabilityModule: CarAvailabilityModule) {
    this.carAvailabilityController = new CarAvailabilityController(
      carAvailabilityModule.carAvailabilityService,
    );
  }

  static module: CarAvailabilityApiModule;

  static async init(): Promise<CarAvailabilityApiModule> {
    if (!this.module) {
      const carAvailabilityModule = await CarAvailabilityModule.init();
      this.module = new CarAvailabilityApiModule(carAvailabilityModule);
    }

    return this.module;
  }
}
