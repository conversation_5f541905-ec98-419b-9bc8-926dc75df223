import {
  CarAvailabilityUpdatedEventPayload,
  EventProps,
  StationAvailability,
} from '@bluesg-2/event-library';
import { DateTime } from 'luxon';
import { InvocationContext } from 'src/shared/invocation-context';
import { entityName } from 'src/shared/logging';

export type CarAvailabilityRecord = {
  carModelName: string;
  stationId: string;
  availableCount: string;
  reservedCount: string;
};

export type CarAvailabilityMap = {
  [stationId: string]: {
    id: string;
    cars: {
      [carModelName: string]: {
        available: number;
        reserved: number;
      };
    };
  };
};

export class CarAvailaibilityMap {
  private map: CarAvailabilityMap;
  constructor(carAvailabilityMap: CarAvailabilityMap) {
    this.map = carAvailabilityMap;
  }
  toCarAvailabilityUpdatedEvent(): EventProps<CarAvailabilityUpdatedEventPayload> {
    const stationAvailabilityMap: Record<
      string,
      {
        carAvailability: {
          byModel: {
            [key: string]: number;
          };
          total: number;
        };
      }
    > = {};

    for (const [stationId, data] of Object.entries(this.map)) {
      stationAvailabilityMap[stationId] = {
        carAvailability: {
          byModel: {},
          total: 0,
        },
      };
      let total = 0;
      for (const [carModelName, { available }] of Object.entries(data.cars)) {
        total += available;
        stationAvailabilityMap[stationId].carAvailability.byModel[
          carModelName
        ] = available;
      }
      stationAvailabilityMap[stationId].carAvailability.total = total;
    }

    const stationAvailability: StationAvailability[] = [];

    for (const [stationId, data] of Object.entries(stationAvailabilityMap)) {
      stationAvailability.push({
        stationId,
        carAvailability: data.carAvailability,
      });
    }
    const payload = new CarAvailabilityUpdatedEventPayload({
      stations: stationAvailability,
      timestamp: new Date().toISOString(),
    });
    const result: EventProps<CarAvailabilityUpdatedEventPayload> = {
      sender: entityName,
      sentAt: DateTime.now().toMillis(),
      correlationId: InvocationContext.getCorrelationId(),
      payload,
    };

    return result;
  }
}
