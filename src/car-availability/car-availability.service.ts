import { CarAvailabilityUpdatedEvent } from '@bluesg-2/event-library';
import { CarLocationUpdatedEvent } from '@bluesg-2/event-library/dist/car/car.event';
import { DateTime } from 'luxon';
import {
  CarAvailabilityMap,
  CarAvailabilityRecord,
  CarAvailaibilityMap,
} from 'src/car-availability/car-availability.type';
import { CarAvailabilityQuery } from 'src/car-availability/dto/car-availability.query';
import { CarAvailabilityRepository } from 'src/car-availability/entities/car-availability.repository';
import { Car, CarAvailability, CarId } from 'src/car/car.type';
import { CarRepository } from 'src/car/entities/car.repository';
import { CarNotFoundError } from 'src/car/errors/car.error';
import { EventPublisher } from 'src/shared/event.module';
import { FeatureFlag } from 'src/shared/feature-flag.module';
import { getLogger } from 'src/shared/logging';

const logger = getLogger('car-availability.service');

export class CarAvailabilityService {
  constructor(
    private readonly carAvailabilityRepository: CarAvailabilityRepository,
    private readonly carRepository: CarRepository,
    private readonly eventPublisher: EventPublisher,
    private readonly featureFlag: FeatureFlag,
  ) {}

  async getCarAvailability(
    query: CarAvailabilityQuery,
  ): Promise<CarAvailabilityMap> {
    const aggregation: CarAvailabilityRecord[] =
      await this.carAvailabilityRepository.getCarAvailability({
        defaultMinBatteryLevel: query?.defaultMinBatteryLevel?.value,
        carModelMinBatteryLevels: query.carModelMinBatteryLevels,
        stationIds: query.stationIds,
      });

    const result: CarAvailabilityMap = {};
    aggregation.forEach((item) => {
      if (!(item.stationId in result)) {
        result[item.stationId] = {
          id: item.stationId,
          cars: {},
        };
      }
      result[item.stationId]['cars'][item.carModelName] = {
        available: parseInt(item.availableCount, 10),
        reserved: parseInt(item.reservedCount, 10),
      };
    });
    return result;
  }

  async updateCarAvailability(
    carId: CarId,
    availability: CarAvailability,
    availabilityAt: DateTime,
  ): Promise<void> {
    const carExists = await this.carRepository.existsBy({ id: carId.value });
    if (!carExists) {
      throw new CarNotFoundError(carId.value);
    }
    await this.carRepository.updateAvailability(
      carId,
      availability,
      availabilityAt,
    );

    const featureFlag =
      await this.featureFlag.getEnableCsCarAvailabilityUpdatedEvent();
    if (!featureFlag) {
      logger.info(
        'CarAvailabilityUpdatedEvent feature flag is disabled - skipping event publication',
        {
          carId: carId.value,
          availability,
          availabilityAt: availabilityAt.toISO(),
        },
      );
      return;
    }
    const carAvailabilityMapData: CarAvailabilityMap =
      await this.getCarAvailability({});

    const carAvailabilityMap = new CarAvailaibilityMap(carAvailabilityMapData);
    const payload = carAvailabilityMap.toCarAvailabilityUpdatedEvent();

    await this.eventPublisher.send(new CarAvailabilityUpdatedEvent(payload));
  }

  async updateCarAvailabilityAndLocation(
    carId: CarId,
    availability: CarAvailability,
    stationId: string,
    asOf: DateTime,
  ): Promise<Car> {
    const updatedCar =
      await this.carRepository.updateAvailabilityAndLocationAsOf(
        carId,
        availability,
        stationId,
        asOf,
      );

    await this.eventPublisher.send(
      new CarLocationUpdatedEvent(updatedCar.toLocationEventProps()),
    );

    return updatedCar;
  }

  async switchReservation(
    carId: CarId,
    previousCarId: CarId,
    switchedAt: DateTime,
  ): Promise<void> {
    await this.carRepository.switchCarAvailability(
      carId,
      CarAvailability.RESERVED,
      previousCarId,
      CarAvailability.AVAILABLE,
      switchedAt,
    );
  }
}
