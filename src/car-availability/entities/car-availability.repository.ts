import { CarAvailabilityRecord } from 'src/car-availability/car-availability.type';
import { CarBatteryLevel } from 'src/car-state/car-state.type';
import { CarEntity } from 'src/car/entities/car.entity';
import { Brackets, DataSource, EntityManager, Repository } from 'typeorm';

export class CarAvailabilityRepository extends Repository<CarEntity> {
  constructor(dataSource: DataSource, entityManager?: EntityManager) {
    super(CarEntity, entityManager ?? dataSource.createEntityManager());
  }
  async getCarAvailability(query: {
    stationIds: string[];
    defaultMinBatteryLevel: number;
    carModelMinBatteryLevels?: {
      carModelName: string;
      minBatteryLevel?: CarBatteryLevel;
    }[];
  }): Promise<CarAvailabilityRecord[]> {
    const { stationIds, defaultMinBatteryLevel, carModelMinBatteryLevels } =
      query;
    const queryBuilder = this.createQueryBuilder('car')
      .leftJoinAndSelect('car.state', 'car_state')
      .leftJoinAndSelect('car.carModel', 'car_model')
      .select('car.station_id', 'stationId')
      .addSelect('car_model.name', 'carModelName')
      .addSelect(
        `SUM(CASE WHEN car.availability = 'AVAILABLE' THEN 1 ELSE 0 END)`,
        'availableCount',
      )
      .addSelect(
        `SUM(CASE WHEN car.availability = 'RESERVED' THEN 1 ELSE 0 END)`,
        'reservedCount',
      )
      .where('car.station_id is not null');

    if (stationIds) {
      queryBuilder.andWhere('car.stationId IN (:...stationIds)', {
        stationIds,
      });
    }
    const carModelWithBatteryLevelQueries: string[] = [];

    carModelMinBatteryLevels?.forEach((carModelMinBatteryLevel) => {
      if (!carModelMinBatteryLevel?.carModelName) {
        return;
      }
      if (
        carModelMinBatteryLevel?.minBatteryLevel?.value ||
        defaultMinBatteryLevel
      ) {
        carModelWithBatteryLevelQueries.push(
          `car_model.name = '${carModelMinBatteryLevel.carModelName}' and car_state.ev_battery_level >= ${carModelMinBatteryLevel?.minBatteryLevel?.value || defaultMinBatteryLevel}`,
        );
        return;
      }
      carModelWithBatteryLevelQueries.push(
        `car_model.name = '${carModelMinBatteryLevel.carModelName}'`,
      );
    });

    if (carModelWithBatteryLevelQueries?.length) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          carModelWithBatteryLevelQueries.forEach((query) => {
            qb.orWhere(query);
          });
        }),
      );
    } else if (defaultMinBatteryLevel) {
      queryBuilder.andWhere('car_state.ev_battery_level >= :minBatteryLevel', {
        minBatteryLevel: defaultMinBatteryLevel,
      });
    }

    queryBuilder.groupBy('car_model.name, car.stationId');

    return await queryBuilder.getRawMany();
  }
}
