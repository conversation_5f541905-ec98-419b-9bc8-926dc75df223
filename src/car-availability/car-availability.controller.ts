import { CarAvailabilityService } from 'src/car-availability/car-availability.service';
import { CarAvailabilityMap } from 'src/car-availability/car-availability.type';
import { CarAvailabilityQuery } from 'src/car-availability/dto/car-availability.query';
import { OkResponse, StandardResponseBody } from 'src/shared/http-response';

export class CarAvailabilityController {
  constructor(
    private readonly carAvailabilityService: CarAvailabilityService,
  ) {}
  async getCarAvailability(
    query: CarAvailabilityQuery,
  ): Promise<OkResponse<StandardResponseBody<CarAvailabilityMap>>> {
    const response =
      await this.carAvailabilityService.getCarAvailability(query);
    return OkResponse.fromResult(response);
  }
}
