import { CarBatteryLevel } from 'src/car-state/car-state.type';
import { z } from 'zod';

export const carAvailabilityQuerySchema = z.object({
  stationIds: z
    .array(z.string().uuid('Please provide correct UUID for stationId field'))
    .optional(),
  default: z
    .object({
      minBatteryLevel: z.number().optional(),
    })
    .optional(),
  carModels: z
    .record(
      z.string(),
      z
        .object({
          minBatteryLevel: z.number().optional(),
        })
        .optional(),
    )
    .optional(),
});

export type ICarAvailabilityQuery = z.infer<typeof carAvailabilityQuerySchema>;

export class CarAvailabilityQuery {
  readonly stationIds?: string[];
  readonly defaultMinBatteryLevel?: CarBatteryLevel;
  readonly carModelMinBatteryLevels?: {
    carModelName: string;
    minBatteryLevel?: CarBatteryLevel;
  }[];

  constructor(props: ICarAvailabilityQuery) {
    if (props['stationIds']?.length) {
      this.stationIds = props['stationIds'];
    }

    this.defaultMinBatteryLevel = props?.['default']?.['minBatteryLevel']
      ? new CarBatteryLevel(props?.['default']?.['minBatteryLevel'])
      : null;

    if (props['carModels'] && Object.keys(props['carModels'])?.length) {
      this.carModelMinBatteryLevels = [];
      for (const [carModelName, batteryLevel] of Object.entries(
        props['carModels'],
      )) {
        this.carModelMinBatteryLevels.push({
          carModelName: carModelName,
          minBatteryLevel: batteryLevel?.minBatteryLevel
            ? new CarBatteryLevel(batteryLevel.minBatteryLevel)
            : null,
        });
      }
    }
  }
}
