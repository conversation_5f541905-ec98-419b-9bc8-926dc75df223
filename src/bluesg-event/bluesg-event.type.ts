import { Event } from '@bluesg-2/event-library';
import { SQSRecord } from 'aws-lambda';
import { ClassConstructor, plainToInstance } from 'class-transformer';
import { BluesgEventParsingError } from 'src/bluesg-event/errors/bluesg-event.error';
import { getErrorFormat } from 'src/shared/errors/general.error';

export class BluesgEvent {
  constructor(
    public readonly messageId: string,
    public readonly receiptHandle: string,
    public readonly body: string,
    public readonly eventSource: string,
    public readonly eventSourceARN: string,
    public readonly awsRegion: string,
  ) {}

  static fromSQSEventRecord(record: SQSRecord): BluesgEvent {
    return new BluesgEvent(
      record.messageId,
      record.receiptHandle,
      record.body,
      record.eventSource,
      record.eventSourceARN,
      record.awsRegion,
    );
  }

  getBodyAsBluesgEvent<T extends Event>(
    eventConstructor: ClassConstructor<T>,
  ): T {
    try {
      return plainToInstance(
        eventConstructor,
        JSON.parse(JSON.parse(this.body).Message) as Record<string, unknown>,
      );
    } catch (error) {
      throw new BluesgEventParsingError(this.messageId, getErrorFormat(error));
    }
  }
}
