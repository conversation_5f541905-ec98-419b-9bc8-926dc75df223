import {
  AimaCarEnabledEvent,
  AimaCarPutOutOfServiceEvent,
  RentalEndedEvent,
  RentalReservationCancelledEvent,
  RentalReservationCreatedEvent,
  RentalReservationExpiredEvent,
  RentalReservationSwitchedEvent,
  RentalStartedEvent,
} from '@bluesg-2/event-library';
import { DateTime } from 'luxon';
import {
  EndStationIdMissingError,
  StartStationIdMissingError,
} from 'src/bluesg-event/errors/bluesg-event.error';
import { CarAvailabilityService } from 'src/car-availability/car-availability.service';
import { CarService } from 'src/car/car.service';
import { CarAvailability } from 'src/car/car.type';
import { VulogCarId } from 'src/shared/shared.type';

export class BluesgEventController {
  constructor(
    private readonly carService: CarService,
    private readonly carAvailabilityService: CarAvailabilityService,
  ) {}

  async handleRentalEndedEvent(
    rentalEndedEvent: RentalEndedEvent,
  ): Promise<void> {
    const vulogCarId = new VulogCarId(rentalEndedEvent.payload.carId);
    const stationId = rentalEndedEvent.payload.endStationId;
    const locatedAt = DateTime.fromISO(rentalEndedEvent.payload.endedAt);

    const car = await this.carService.getOneByExternalCarId(vulogCarId);

    if (!stationId) {
      // If no station ID, only update availability
      await this.carAvailabilityService.updateCarAvailability(
        car.id,
        CarAvailability.AVAILABLE,
        locatedAt,
      );
      throw new EndStationIdMissingError(
        vulogCarId.value,
        rentalEndedEvent.payload.tripInfo.id,
      );
    }

    // Update both availability and location in one operation
    await this.carAvailabilityService.updateCarAvailabilityAndLocation(
      car.id,
      CarAvailability.AVAILABLE,
      stationId,
      locatedAt,
    );
  }

  async handleRentalStartedEvent(
    rentalStartedEvent: RentalStartedEvent,
  ): Promise<void> {
    const vulogCarId = new VulogCarId(rentalStartedEvent.payload.carId);
    const stationId = rentalStartedEvent.payload.startStationId;
    const locatedAt = DateTime.fromISO(rentalStartedEvent.payload.startedAt);

    const car = await this.carService.getOneByExternalCarId(vulogCarId);

    if (!stationId) {
      // If no station ID, only update availability
      await this.carAvailabilityService.updateCarAvailability(
        car.id,
        CarAvailability.ON_RENTAL,
        locatedAt,
      );
      throw new StartStationIdMissingError(vulogCarId.value);
    }

    // Update both availability and location in one operation
    await this.carAvailabilityService.updateCarAvailabilityAndLocation(
      car.id,
      CarAvailability.ON_RENTAL,
      stationId,
      locatedAt,
    );
  }

  async handleReservationCreatedEvent(
    reservationCreatedEvent: RentalReservationCreatedEvent,
  ): Promise<void> {
    const vulogCarId = new VulogCarId(reservationCreatedEvent.payload.carId);
    const car = await this.carService.getOneByExternalCarId(vulogCarId);

    await this.carAvailabilityService.updateCarAvailability(
      car.id,
      CarAvailability.RESERVED,
      DateTime.fromISO(reservationCreatedEvent.payload.createdAt),
    );
  }

  async handleReservationCancelledEvent(
    reservationCancelledEvent: RentalReservationCancelledEvent,
  ): Promise<void> {
    const vulogCarId = new VulogCarId(reservationCancelledEvent.payload.carId);
    const car = await this.carService.getOneByExternalCarId(vulogCarId);

    await this.carAvailabilityService.updateCarAvailability(
      car.id,
      CarAvailability.AVAILABLE,
      DateTime.fromISO(reservationCancelledEvent.payload.cancelledAt),
    );
  }

  async handleReservationSwitchedEvent(
    reservationSwitchedEvent: RentalReservationSwitchedEvent,
  ): Promise<void> {
    const currentCarId = new VulogCarId(reservationSwitchedEvent.payload.carId);
    const currentCar =
      await this.carService.getOneByExternalCarId(currentCarId);
    const previousCarId = new VulogCarId(
      reservationSwitchedEvent.payload.previousCarId,
    );
    const previousCar =
      await this.carService.getOneByExternalCarId(previousCarId);

    await this.carAvailabilityService.switchReservation(
      currentCar.id,
      previousCar.id,
      DateTime.fromISO(reservationSwitchedEvent.payload.switchedAt),
    );
  }

  async handleReservationExpiredEvent(
    reservationExpiredEvent: RentalReservationExpiredEvent,
  ): Promise<void> {
    const vulogCarId = new VulogCarId(reservationExpiredEvent.payload.carId);
    const car = await this.carService.getOneByExternalCarId(vulogCarId);

    await this.carAvailabilityService.updateCarAvailability(
      car.id,
      CarAvailability.AVAILABLE,
      DateTime.fromISO(reservationExpiredEvent.payload.expiredAt),
    );
  }

  async handleCarEnabledEvent(
    carEnabledEvent: AimaCarEnabledEvent,
  ): Promise<void> {
    const vulogCarId = new VulogCarId(carEnabledEvent.payload.vulogCarId);
    const car = await this.carService.getOneByExternalCarId(vulogCarId);

    await this.carAvailabilityService.updateCarAvailability(
      car.id,
      CarAvailability.AVAILABLE,
      DateTime.fromISO(carEnabledEvent.payload.timestamp),
    );
  }

  async handleCarPutOutOfServiceEvent(
    carPutOutOfServiceEvent: AimaCarPutOutOfServiceEvent,
  ): Promise<void> {
    const vulogCarId = new VulogCarId(
      carPutOutOfServiceEvent.payload.vulogCarId,
    );
    const car = await this.carService.getOneByExternalCarId(vulogCarId);

    await this.carAvailabilityService.updateCarAvailability(
      car.id,
      CarAvailability.OUT_OF_SERVICE,
      DateTime.fromISO(carPutOutOfServiceEvent.payload.timestamp),
    );
  }
}
