import { BluesgEventController } from 'src/bluesg-event/bluesg-event.controller';
import { CarAvailabilityModule } from 'src/car-availability/car-availability.module';
import { CarModule } from 'src/car/car.module';

export class BluesgEventApiModule {
  readonly bluesgEventController: BluesgEventController;

  constructor(
    carModule: CarModule,
    carAvailabilityModule: CarAvailabilityModule,
  ) {
    this.bluesgEventController = new BluesgEventController(
      carModule.carService,
      carAvailabilityModule.carAvailabilityService,
    );
  }

  static module: BluesgEventApiModule;

  static async init(): Promise<BluesgEventApiModule> {
    if (!this.module) {
      this.module = new BluesgEventApiModule(
        await CarModule.init(),
        await CarAvailabilityModule.init(),
      );
    }

    return this.module;
  }
}
