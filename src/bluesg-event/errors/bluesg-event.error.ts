import { BadRequestError } from 'src/shared/errors/http.error';

export class BluesgEventParsingError extends Error {
  errorCode = 'BLUESG_EVENT_PARSING_ERROR';

  constructor(
    public readonly messageId: string,
    public readonly error: unknown,
  ) {
    super();
  }
}

export class EndStationIdMissingError extends BadRequestError {
  static readonly ERROR_CODE = 'END_STATION_ID_MISSING';
  constructor(carId: string, rentalId: string) {
    super(
      EndStationIdMissingError.ERROR_CODE,
      `Rental ended event for rental ${rentalId} of car ${carId} is missing end stations id`,
    );
  }
}

export class StartStationIdMissingError extends BadRequestError {
  static readonly ERROR_CODE = 'START_STATION_ID_MISSING';
  constructor(carId: string) {
    super(
      StartStationIdMissingError.ERROR_CODE,
      `Rental started event for car ${carId} is missing start station id`,
    );
  }
}
