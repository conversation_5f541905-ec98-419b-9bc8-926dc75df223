import axios, { AxiosError, AxiosInstance } from 'axios';
import { InvocationContext } from 'src/shared/invocation-context';
import { StationPaths } from 'src/station/station-paths';

const STATION_CORRELATION_ID_HEADER_REQUEST = 'x-correlation-id';

export class StationClient {
  private axios: AxiosInstance;

  constructor() {
    this.axios = axios.create();
  }

  getDefaultHeaders() {
    return {
      [STATION_CORRELATION_ID_HEADER_REQUEST]:
        InvocationContext.getCorrelationId(),
    };
  }

  async checkStationExists(stationId: string): Promise<boolean> {
    try {
      const url = await StationPaths.getStationById(stationId);
      const response = await this.axios.get(url.toString(), {
        headers: this.getDefaultHeaders(),
      });
      return response.status === 200 && response.data.success;
    } catch (error) {
      if (
        error instanceof AxiosError &&
        (error.response?.status === 404 || error.response?.status === 400)
      ) {
        return false;
      }
      throw error;
    }
  }
}
