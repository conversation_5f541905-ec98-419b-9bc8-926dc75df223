import { APIGatewayProxyResult } from 'aws-lambda';
import { status as HttpStatus } from 'http-status';
import { Page } from 'src/shared/query/pagination';

class PaginationData {
  constructor(
    readonly page: number,
    readonly nbPerPage: number,
    readonly total: number,
  ) {}
}

export class StandardResponseBody<T> {
  readonly success: boolean = true;
  constructor(readonly result: T) {}
}

export class PaginationResponseBody<T> extends StandardResponseBody<T[]> {
  constructor(
    result: T[],
    readonly pagination: PaginationData,
  ) {
    super(result);
  }
}

export class MessageResponseBody {
  readonly success: boolean = true;
  constructor(readonly message: string) {}
}

class HttpResponse<B> {
  constructor(
    readonly statusCode: number,
    readonly body?: B,
    readonly extraHeaders?: Record<string, string>,
  ) {}

  toGatewayResult(): APIGatewayProxyResult {
    return {
      statusCode: this.statusCode,
      headers: {
        'Content-Type': 'application/json',
        ...(this.extraHeaders || {}),
      },
      body: this.body ? JSON.stringify(this.body) : undefined,
    };
  }
}

export class OkResponse<B> extends HttpResponse<B> {
  constructor(body: B, extraHeaders?: Record<string, string>) {
    super(HttpStatus.OK, body, extraHeaders);
  }

  static fromResult<T>(result: T): OkResponse<StandardResponseBody<T>> {
    return new OkResponse(new StandardResponseBody(result));
  }

  static fromPage<T>(page: Page<T>): OkResponse<PaginationResponseBody<T>> {
    return new OkResponse(
      new PaginationResponseBody(page.items, page.pagination),
    );
  }

  static fromMessage(message: string): OkResponse<MessageResponseBody> {
    return new OkResponse(new MessageResponseBody(message));
  }
}

export class CreatedResponse<B> extends HttpResponse<B> {
  constructor(body: B, extraHeaders?: Record<string, string>) {
    super(HttpStatus.CREATED, body, extraHeaders);
  }

  static fromResult<T>(result: T): CreatedResponse<StandardResponseBody<T>> {
    return new CreatedResponse(new StandardResponseBody(result));
  }
}

export class NoContentResponse<B> extends HttpResponse<B> {
  // 204 response must not contain a body
  constructor(extraHeaders?: Record<string, string>) {
    super(HttpStatus.NO_CONTENT, undefined, extraHeaders);
  }
}
