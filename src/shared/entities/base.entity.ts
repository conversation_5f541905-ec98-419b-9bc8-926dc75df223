import {
  INTERNAL_SERVER_ERROR_CODE,
  InternalServerError,
} from 'src/shared/errors/http.error';
import { InvocationContext } from 'src/shared/invocation-context';
import {
  BaseEntity as Base,
  BeforeInsert,
  BeforeRemove,
  BeforeSoftRemove,
  BeforeUpdate,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export class BaseEntity extends Base {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
    comment: 'Auto-generated by ORM',
  })
  createdAt?: Date;

  @Column({
    type: 'text',
    nullable: true,
    select: true,
  })
  createdBy?: string;

  @UpdateDateColumn({
    type: 'timestamptz',
    select: true,
    comment: 'Auto-modified by ORM',
  })
  modifiedAt?: Date;

  @Column({
    type: 'text',
    nullable: true,
    select: true,
  })
  modifiedBy?: string;

  @DeleteDateColumn({
    type: 'timestamptz',
    comment: 'Auto-generated by ORM',
  })
  deletedAt?: Date;

  @Column({
    type: 'text',
    nullable: true,
    select: true,
  })
  deletedBy?: string;

  @BeforeInsert()
  setCreatedBy() {
    this.createdBy = InvocationContext.getUserId();
    this.setModifiedBy();
  }

  @BeforeUpdate()
  setModifiedBy() {
    this.modifiedBy = InvocationContext.getUserId();
  }

  @BeforeSoftRemove()
  async setDeletedBy() {
    this.deletedBy = InvocationContext.getUserId();
    this.setModifiedBy();
    // work around as typeorm ONLY update deletedAt field with soft remove query
    // ref: https://github.com/typeorm/typeorm/issues/9155
    await this.save();
  }

  @BeforeRemove()
  blockRemove() {
    throw new InternalServerError(
      INTERNAL_SERVER_ERROR_CODE,
      'Hard remove is not supported!',
    );
  }
}
