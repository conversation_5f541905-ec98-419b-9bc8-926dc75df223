import middy from '@middy/core';
import { APIGatewayEvent } from 'aws-lambda';
import { AuthModule } from 'src/auth/auth.module';
import {
  Permission,
  PermissionAction,
} from 'src/auth/authorization/permission';
import { Resource } from 'src/auth/authorization/resource';

export default (
  action: PermissionAction,
  resource: Resource,
): middy.MiddlewareObj => {
  const before: middy.MiddlewareFn = async ({
    event,
  }: {
    event: APIGatewayEvent;
  }) => {
    const authModule = await AuthModule.init();

    await authModule.adapter.authorizeRequest(
      event,
      new Permission(resource, action),
    );
  };

  return {
    before: before,
  };
};
