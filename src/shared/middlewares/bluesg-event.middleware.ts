import { errorHandler } from 'src/shared/middlewares/error-handler.middleware';
import { inputOutputLoggerMiddleware } from 'src/shared/middlewares/input-ouput.logger.middleware';
import { invocationContextMiddleware } from 'src/shared/middlewares/invocation-context.middleware';

export const BLUESG_EVENT_MIDDLEWARES = [
  invocationContextMiddleware(),
  inputOutputLoggerMiddleware(),
  errorHandler(),
];
