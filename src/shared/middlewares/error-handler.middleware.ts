import middy from '@middy/core';
import { getErrorFormat } from 'src/shared/errors/general.error';
import {
  HttpError,
  INTERNAL_SERVER_ERROR_CODE,
  INTERNAL_SERVER_ERROR_MESSAGE,
} from 'src/shared/errors/http.error';
import { defaultZodErrorResponse } from 'src/shared/errors/zod.error';
import { getLogger } from 'src/shared/logging';
import { z } from 'zod';

export const errorHandler = (): middy.MiddlewareObj => {
  const onError: middy.MiddlewareFn = async (request): Promise<void> => {
    const { error } = request;

    if (request.response !== undefined) return;

    const logger = getLogger('errorHandler');
    logger.error('Error:', {
      error: getErrorFormat(error),
    });

    const defaultResponse = {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        code: INTERNAL_SERVER_ERROR_CODE,
        message: INTERNAL_SERVER_ERROR_MESSAGE,
      }),
    };

    if (error instanceof HttpError) {
      request.response = error.toResponse();
    } else if (error instanceof z.ZodError) {
      request.response = defaultZodErrorResponse(error);
    } else {
      request.response = defaultResponse;
    }
  };

  return {
    onError,
  };
};
