import middy from '@middy/core';
import { randomUUID } from 'crypto';
import { InvocationContext } from 'src/shared/invocation-context';

export const CORRELATION_ID_HEADER = 'x-correlation-id';
export const CORRELATION_ID_RESPONSE_KEY = 'correlationId';

function extractCorrelationId(headers?: { [key: string]: string }): string {
  if (headers?.[CORRELATION_ID_HEADER]) {
    return headers[CORRELATION_ID_HEADER];
  }

  return randomUUID();
}

export const invocationContextMiddleware = (): middy.MiddlewareObj => {
  const setupInvocationContext: middy.MiddlewareFn = async (
    request,
  ): Promise<void> => {
    const { event, context } = request;

    const requestId = context.awsRequestId;

    const correlationId = extractCorrelationId(event.headers);

    InvocationContext.init(requestId, correlationId);
  };

  const teardownInvocationContext: middy.MiddlewareFn = async (
    request,
  ): Promise<void> => {
    try {
      const { response } = request;

      if (!response) {
        return;
      }

      response.headers ??= {};
      response.headers[CORRELATION_ID_HEADER] =
        InvocationContext.getCorrelationId();

      const responseBody = response.body;
      const parsedBody = responseBody ? JSON.parse(responseBody) : null;

      if (!parsedBody || typeof parsedBody !== 'object') {
        return;
      }

      parsedBody[CORRELATION_ID_RESPONSE_KEY] =
        InvocationContext.getCorrelationId();

      response.body = JSON.stringify(parsedBody);
    } finally {
      // whatever happens, clear the invocation context here
      InvocationContext.clear();
    }
  };

  return {
    before: setupInvocationContext,
    after: teardownInvocationContext,
    onError: teardownInvocationContext,
  };
};
