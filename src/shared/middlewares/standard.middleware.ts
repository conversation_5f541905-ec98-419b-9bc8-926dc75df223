import httpHeaderNormalizer from '@middy/http-header-normalizer';
import json<PERSON>odyParser from '@middy/http-json-body-parser';
import { corsMiddleware } from 'src/shared/middlewares/cors.middleware';
import { errorHandler } from 'src/shared/middlewares/error-handler.middleware';
import { inputOutputLoggerMiddleware } from 'src/shared/middlewares/input-ouput.logger.middleware';
import { invocationContextMiddleware } from 'src/shared/middlewares/invocation-context.middleware';

/*
  when there are multiple middlewares setup [middleware1, middleware2, middleware3],
  the execution order is:
    middleware1.before
    middleware2.before
    middleware3.before
    handler
    middleware3.after
    middleware2.after
    middleware1.after

  if there's error, then the onError line will be invoked:
    handler
    middleware3.onError
    middleware2.onError
    middleware1.onError
*/
export const STANDARD_MIDDLEWARES = [
  corsMiddleware(),
  httpHeaderNormalizer(),
  invocationContextMiddleware(),
  inputOutputLoggerMiddleware(),
  jsonBody<PERSON>ars<PERSON>({ disableContentTypeError: true }),
  errorHand<PERSON>(),
];
