import middy from '@middy/core';
import { APIGatewayProxyResult } from 'aws-lambda';

const DEFAULT_CORS_HEADERS = {
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*',
  'Access-Control-Allow-Origin': '*',
};

export const corsMiddleware = (): middy.MiddlewareObj => {
  const before: middy.MiddlewareFn = async (
    request,
  ): Promise<APIGatewayProxyResult | void> => {
    const method = request.event.httpMethod;

    if (method === 'OPTIONS') {
      const response = {
        headers: DEFAULT_CORS_HEADERS,
        statusCode: 204,
        body: '',
      };

      return response;
    }
  };

  const after: middy.MiddlewareFn = async (request): Promise<void> => {
    const { response } = request;

    if (!response) {
      return;
    }

    response.headers = {
      ...response.headers,
      ...DEFAULT_CORS_HEADERS,
    };
  };

  return {
    before,
    after: after,
    onError: after,
  };
};
