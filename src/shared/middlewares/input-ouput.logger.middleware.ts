import middy from '@middy/core';
import inputOutputLogger from '@middy/input-output-logger';
import { getLogger } from 'src/shared/logging';

export const inputOutputLoggerMiddleware = (): middy.MiddlewareObj => {
  return inputOutputLogger({
    logger: (request) => {
      const childLogger = getLogger('inputOutputLogger');
      childLogger.debug(`[CS] invocation`, {
        event: request.event,
        response: request.response,
      });
    },
    awsContext: true,
    omitPaths: [
      'event.headers.authorization',
      'event.multiValueHeaders.authorization',
    ],
    mask: '***redacted***',
  });
};
