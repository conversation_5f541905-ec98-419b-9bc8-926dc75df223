import { IConfigCatClient, getClient } from 'configcat-node';
import { getConfig } from 'src/shared/env';

export class FeatureFlagModule {
  constructor(readonly featureFlag: FeatureFlag) {}

  static module: FeatureFlagModule;

  static async init(): Promise<FeatureFlagModule> {
    if (!this.module) {
      const configCatClient = getClient(await getConfig('CONFIG_CAT_SDK_KEY'));

      this.module = new FeatureFlagModule(new FeatureFlag(configCatClient));
    }

    return this.module;
  }
}

export class FeatureFlag {
  constructor(private readonly configCatClient: IConfigCatClient) {}

  async getEnableConsumingVgEvents(): Promise<boolean> {
    return await this.configCatClient.getValueAsync(
      'enableConsumingVgEvents',
      false,
    );
  }

  async getEnableCsCarAvailabilityUpdatedEvent(): Promise<boolean> {
    return await this.configCatClient.getValueAsync(
      'enableCsCarAvailabilityUpdatedEvent',
      false,
    );
  }
}
