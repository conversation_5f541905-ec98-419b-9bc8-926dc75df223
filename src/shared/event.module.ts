import { PublishCommand, SNSClient } from '@aws-sdk/client-sns';
import { Event } from '@bluesg-2/event-library';
import { getAwsClientConfig, getConfig } from 'src/shared/env';
import { getErrorFormat } from 'src/shared/errors/general.error';
import { getLogger } from 'src/shared/logging';

const logger = getLogger('event.module.ts');
export class EventModule {
  constructor(readonly eventPublisher: EventPublisher) {}

  static module: EventModule;

  static async init(): Promise<EventModule> {
    if (!this.module) {
      const clientConfig = await getAwsClientConfig();
      const snsClient = new SNSClient(clientConfig);
      const eventPublisher = new EventPublisher(snsClient);

      this.module = new EventModule(eventPublisher);
    }

    return this.module;
  }
}

export class EventPublisher {
  constructor(readonly snsClient: SNSClient) {}

  async send<T extends Event>(event: T): Promise<void> {
    try {
      const command = new PublishCommand({
        TopicArn: await getConfig('MESSAGE_BUS_TOPIC_ARN'),
        Message: JSON.stringify(event),
        MessageAttributes: {
          eventType: {
            DataType: 'String',
            StringValue: event.event,
          },
        },
      });

      const response = await this.snsClient.send(command);

      logger.info(`Sent ${event.event} event successfully`, {
        event,
        messageId: response.MessageId,
      });
    } catch (error) {
      logger.error('Error sending event', {
        event,
        error: getErrorFormat(error),
      });
    }

    return null;
  }
}
