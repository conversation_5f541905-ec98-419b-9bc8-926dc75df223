import { APIGatewayProxyResult } from 'aws-lambda';

export class HttpError extends Error {
  constructor(
    public statusCode: number,
    public code: string = 'GENERIC_ERROR',
    message: string,
    public cause?: string,
  ) {
    super(message);
  }

  toResponse(): APIGatewayProxyResult {
    return {
      statusCode: this.statusCode,
      body: JSON.stringify({
        code: this.code,
        message: this.message,
        cause: this.cause,
      }),
      headers: { 'Content-Type': 'application/json' },
    };
  }
}

export const BAD_REQUEST_CODE = 'BAD_REQUEST';
export const BAD_REQUEST_MESSAGE = 'Bad Request';
export class BadRequestError extends HttpError {
  constructor(
    code: string = BAD_REQUEST_CODE,
    message: string = BAD_REQUEST_MESSAGE,
    cause?: string,
  ) {
    super(400, code, message, cause);
  }
}

export const UNAUTHORIZED_CODE = 'UNAUTHORIZED';
export const UNAUTHORIZED_MESSAGE = 'Unauthorized';
export class UnauthorizedError extends HttpError {
  constructor(
    code: string = UNAUTHORIZED_CODE,
    message: string = UNAUTHORIZED_MESSAGE,
    cause?: string,
  ) {
    super(401, code, message, cause);
  }
}

export const FORBIDDEN_CODE = 'FORBIDDEN';
export const FORBIDDEN_MESSAGE = 'Forbidden';
export class ForbiddenError extends HttpError {
  constructor(
    code: string = FORBIDDEN_CODE,
    message: string = FORBIDDEN_MESSAGE,
    cause?: string,
  ) {
    super(403, code, message, cause);
  }
}

export const NOT_FOUND_CODE = 'NOT_FOUND';
export const NOT_FOUND_MESSAGE = 'Not Found';
export class NotFoundError extends HttpError {
  constructor(
    code: string = NOT_FOUND_CODE,
    message: string = NOT_FOUND_MESSAGE,
    cause?: string,
  ) {
    super(404, code, message, cause);
  }
}

export const CONFLICT_CODE = 'CONFLICT';
export const CONFLICT_MESSAGE = 'Conflict';
export class ConflictError extends HttpError {
  constructor(
    code: string = CONFLICT_CODE,
    message: string = CONFLICT_MESSAGE,
    cause?: string,
  ) {
    super(409, code, message, cause);
  }
}

export const INTERNAL_SERVER_ERROR_CODE = 'INTERNAL_SERVER_ERROR';
export const INTERNAL_SERVER_ERROR_MESSAGE = 'Internal Server Error';
export class InternalServerError extends HttpError {
  constructor(
    code: string = INTERNAL_SERVER_ERROR_CODE,
    message: string = INTERNAL_SERVER_ERROR_MESSAGE,
    cause?: string,
  ) {
    super(500, code, message, cause);
  }
}
