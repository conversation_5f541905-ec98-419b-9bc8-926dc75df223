import {
  ZodError,
  ZodInvalidTypeIssue,
  ZodInvalidUnionIssue,
  ZodIssue,
} from 'zod';

const formatPath = (issue: ZodIssue) => {
  return issue.path.join(', ');
};

const formatInvalidType = (issues: ZodIssue[]) => {
  const expected = issues
    .map((i) => {
      // Unforutnately the zod library doesn't distinguish between a wrong type and a required field, which we want to handle differently
      if (i.code === 'invalid_type' && i.message !== 'Required') {
        return i.expected;
      }
      return undefined;
    })
    .filter(Boolean);

  if (!expected.length) {
    return undefined;
  }

  const received = (issues?.[0] as ZodInvalidTypeIssue)?.received;

  return `Expected type: '${expected.join(', ')}' for field '${formatPath(
    issues[0],
  )}', got: '${received}'`;
};

const formatRequiredField = (issues: ZodIssue[]) => {
  const expected = issues
    .map((i) => {
      if (i.code === 'invalid_type' && i.message === 'Required') {
        return i.expected;
      }
      return undefined;
    })
    .filter(<PERSON>olean);

  if (!expected.length) {
    return undefined;
  }

  return `Field '${formatPath(issues[0])}' is required`;
};

const formatUnionError = (issue: ZodInvalidUnionIssue) => {
  const issues = issue.unionErrors.flatMap((e) => e.issues);
  return (
    formatInvalidType(issues) || formatRequiredField(issues) || issue.message
  );
};

export const defaultZodErrorMessage = (err: ZodError) => {
  const issueMessages = err.issues.slice(0, 3).map((issue) => {
    switch (issue.code) {
      case 'invalid_type':
        return (
          formatInvalidType([issue]) ||
          formatRequiredField([issue]) ||
          issue.message
        );
      case 'invalid_literal':
        return `Expected literal: '${issue.expected}' for field '${formatPath(
          issue,
        )}', but got: '${issue.received}'`;
      case 'invalid_union':
        return formatUnionError(issue);
      case 'invalid_enum_value':
        return `Expected: '${issue.options.join(', ')}' for field '${formatPath(
          issue,
        )}', but got: '${issue.received}'`;
      case 'unrecognized_keys':
        return `Unrecognized fields: '${issue.keys.join(', ')}'`;
      case 'invalid_arguments':
        return `Invalid arguments for '${issue.path.join(', ')}'`;
      case 'too_small':
        return `Value for field '${formatPath(
          issue,
        )}' cannot be less than ${issue.minimum} (min value)`;
      case 'too_big':
        return `Value for field '${formatPath(
          issue,
        )}' cannot exceed ${issue.maximum} (max value)`;
      case 'not_multiple_of':
        return `Value for field '${formatPath(issue)}' not multiple of: '${
          issue.multipleOf
        }'`;
      case 'not_finite':
        return `Value for field '${formatPath(issue)}' not finite: '${
          issue.message
        }'`;
      case 'invalid_union_discriminator':
      case 'invalid_return_type':
      case 'invalid_date':
      case 'invalid_string':
      case 'invalid_intersection_types':
      default:
        return issue.message;
    }
  });

  return issueMessages.join('; ');
};

const extractDefaultZodErrorCode = (err: ZodError): string | undefined => {
  const errorCodes = err.issues
    .map((i) => {
      if (i.code === 'custom') {
        return i.params?.code;
      }
      return null;
    })
    .filter(Boolean);
  if (errorCodes.length) {
    return errorCodes.join('; ');
  }
  return undefined;
};

export function defaultZodErrorResponse(err: ZodError) {
  return {
    statusCode: 400,
    body: JSON.stringify({
      message: defaultZodErrorMessage(err),
      code: extractDefaultZodErrorCode(err),
    }),
    headers: {
      'Content-Type': 'application/json',
    },
  };
}
