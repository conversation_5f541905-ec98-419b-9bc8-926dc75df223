import { DateTime } from 'luxon';
import { z } from 'zod';

export function isNumberString(value: unknown): boolean {
  return z
    .string()
    .refine((val) => !isNaN(Number(val)) && val.trim() !== '')
    .safeParse(value).success;
}

export function isNumber(value: unknown): boolean {
  return z.number().safeParse(value).success;
}

export function isObject(value: unknown): boolean {
  return z.object({}).safeParse(value).success;
}

export function isDefined(value: unknown) {
  return !z.null().or(z.undefined()).safeParse(value).success;
}

export function isoDateTime(fieldName: string) {
  return z.string().refine(
    (data) => {
      if (!data) return true;
      const date = DateTime.fromISO(data);
      return date.isValid;
    },
    {
      message: `Please provide correct ${fieldName} in ISO 8601 format (e.g. YYYY-MM-DDTHH:mm:ssZ / YYYY-MM-DDTHH:mm:ss / YYYY-MM-DD)`,
    },
  );
}

export function isoDateTimeNotInFuture(fieldName: string, errorCode: string) {
  return isoDateTime(fieldName).refine(
    (data) => {
      if (!data) return true;
      const date = DateTime.fromISO(data);
      return date.isValid && date <= DateTime.now();
    },
    {
      message: `${fieldName} cannot be in the future. Please check the value.`,
      params: { code: errorCode },
    },
  );
}
