import middy from '@middy/core';
import { APIGatewayProxyEvent } from 'aws-lambda';
import { z } from 'zod';

export const parseUuid = (input: string, message?: string) => {
  const parser = z.string().uuid(message ?? 'Please provide correct UUID');

  return parser.parse(input);
};

export interface ValidatedApiEvent<T> extends APIGatewayProxyEvent {
  validatedRequest?: T;
}

export const transformBody = <S extends z.ZodSchema, T extends z.infer<S>>(
  schema: S,
  dtoConstructor: new (props: unknown) => T,
) => {
  return transformRequest(dtoConstructor, { bodySchema: schema });
};

export const transformQuery = <S extends z.ZodSchema, T>(
  schema: S,
  dtoConstructor: new (props: unknown) => T,
) => {
  return transformRequest(dtoConstructor, { querySchema: schema });
};

export const transformPathParams = <S extends z.ZodSchema>(schema: S) => {
  const before: middy.MiddlewareFn = async ({ event }) => {
    const result = event as ValidatedApiEvent<z.infer<typeof schema>>;
    result.validatedRequest = schema.parse(event.pathParameters);
  };
  return {
    before,
  };
};

export const transformRequest = <
  PathParamsSchema extends z.ZodSchema,
  QuerySchema extends z.ZodSchema,
  BodySchema extends z.ZodSchema,
  T,
>(
  dtoConstructor: new (props: T) => T,
  schemas: {
    pathParamsSchema?: PathParamsSchema;
    querySchema?: QuerySchema;
    bodySchema?: BodySchema;
  },
) => {
  const before: middy.MiddlewareFn = async ({ event }) => {
    const result = event as ValidatedApiEvent<T>;
    const props: T = {
      ...(schemas.pathParamsSchema
        ? schemas.pathParamsSchema.parse(event.pathParameters)
        : {}),
      ...(schemas.querySchema
        ? schemas.querySchema.parse(event.queryStringParameters || {})
        : {}),
      ...(schemas.bodySchema ? schemas.bodySchema.parse(event.body) : {}),
    };

    result.validatedRequest = new dtoConstructor(props);
  };
  return {
    before,
  };
};
