import { Token } from 'src/auth/authentication/token';
import {
  INTERNAL_SERVER_ERROR_CODE,
  InternalServerError,
} from 'src/shared/errors/http.error';

export class InvocationContext {
  static context: InvocationContext = null;

  private readonly requestId: string;
  private readonly correlationIds: string[] = [];
  token: Token;

  private constructor(requestId: string, correlationId: string) {
    this.requestId = requestId;
    this.correlationIds.push(correlationId);
  }

  static pushCorrelationId(correlationId: string) {
    this.getContext().correlationIds.push(correlationId);
  }

  static popCorrelationId() {
    this.getContext().correlationIds.pop();
  }

  static isInitialized(): boolean {
    return !!this.context;
  }

  static getRequestId(): string {
    return this.getContext().requestId;
  }

  static getCorrelationId(): string {
    return this.getContext().correlationIds[
      this.getContext().correlationIds.length - 1
    ];
  }

  static setToken(token: Token): void {
    this.getContext().token = token;
  }

  static getTokenSignature(): string {
    return this.getContext().token?.signature;
  }

  static getUserId(): string {
    return this.getContext().token?.payload.userId;
  }

  static init(requestId: string, correlationId: string): void {
    if (this.context) {
      throw new InternalServerError(
        INTERNAL_SERVER_ERROR_CODE,
        'InvocationContext already initialized!',
      );
    }
    this.context = new InvocationContext(requestId, correlationId);
  }

  private static getContext(): InvocationContext {
    if (!this.context) {
      throw new InternalServerError(
        INTERNAL_SERVER_ERROR_CODE,
        'InvocationContext not initialized!',
      );
    }
    return this.context;
  }

  static clear(): void {
    if (!this.context) {
      throw new InternalServerError(
        INTERNAL_SERVER_ERROR_CODE,
        'InvocationContext not initialized!',
      );
    }
    this.context = null;
  }
}
