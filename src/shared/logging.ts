import { InvocationContext } from 'src/shared/invocation-context';
import winston from 'winston';

const envName = process.env.ENV_NAME || 'unknown';
const logLevel = process.env.LOG_LEVEL || 'info';
const logFormat = process.env.LOG_FORMAT || 'json';

export const entityName = `car-service-${envName}`;

const withCustomIds = winston.format((info) => {
  if (InvocationContext.isInitialized()) {
    info.requestId = InvocationContext.getRequestId();
    info.correlationId = InvocationContext.getCorrelationId();
    info.authSignature = InvocationContext.getTokenSignature();
  }

  return info;
});

const jsonFormat = winston.format.combine(
  withCustomIds(),
  winston.format.timestamp(),
  winston.format.json(),
);

const consoleFormat = winston.format.combine(
  withCustomIds(),
  winston.format.timestamp(),
  winston.format.colorize(),
  winston.format.simple(),
);

const baseLogger = winston.createLogger({
  level: logLevel,
  defaultMeta: {
    service: 'car-service',
    entityName: entityName,
    env: envName,
  },
  format: logFormat === 'console' ? consoleFormat : jsonFormat,
  transports: [new winston.transports.Console()],
});

// get a named child logger to use
export function getLogger(name: string): winston.Logger {
  return baseLogger.child({ name: name });
}
