import { buildDataSourceOptions } from 'src/shared/datasources/datasource.options';
import 'src/shared/env';
import { getLogger } from 'src/shared/logging';
import { DataSource } from 'typeorm';

const logger = getLogger('datasource');

let dataSource: DataSource;

const buildDataSource = async () => {
  return new DataSource(await buildDataSourceOptions());
};

export async function getDataSource(): Promise<DataSource> {
  if (dataSource) {
    return dataSource;
  }
  logger.debug('Initializing datasource...');
  dataSource = await buildDataSource();
  await dataSource.initialize();
  logger.debug('Datasource initialized.');
  return dataSource;
}

// default export for typeorm cli
export default buildDataSource();
