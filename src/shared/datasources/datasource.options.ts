import { Signer } from '@aws-sdk/rds-signer';
import 'reflect-metadata';
import { CarModelEntity } from 'src/car-model/entities/car-model.entity';
import { CarStateEntity } from 'src/car-state/entities/car-state.entity';
import { CarEntity } from 'src/car/entities/car.entity';
import { getConfig, getConfigBoolean, getConfigNumber } from 'src/shared/env';
import { DataSourceOptions } from 'typeorm';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';

export const buildDataSourceOptions = async () => {
  // DB_SSL_CERT is encode in base64 type
  const ssl = (await getConfigBoolean('DB_SSL', {
    default: false,
    isOptional: true,
  }))
    ? {
        ca: Buffer.from(await getConfig('DB_SSL_CERT'), 'base64').toString(),
        rejectUnauthorized: false,
      }
    : null;

  const username = await getConfig('DB_USERNAME');
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let password: any;
  if ((await getConfig('DB_AUTH_MODE', { isOptional: true })) === 'rds_iam') {
    password = async (): Promise<string> => {
      const signer = new Signer({
        region: await getConfig('AWS_DEFAULT_REGION'),
        hostname: await getConfig('DB_HOST'),
        port: await getConfigNumber('DB_PORT'),
        username: await getConfig('DB_USERNAME'),
      });
      return await signer.getAuthToken();
    };
  } else {
    password = await getConfig('DB_PASSWORD');
  }

  return {
    type: await getConfig('DB_TYPE'),
    host: await getConfig('DB_HOST'),
    port: await getConfig('DB_PORT'),
    username: username,
    password: password,
    database: await getConfig('DB_NAME'),
    entities: [CarModelEntity, CarEntity, CarStateEntity],
    migrations: ['**/migrations/*.mjs'],
    synchronize: false,
    namingStrategy: new SnakeNamingStrategy(),
    ssl: ssl,
    autoLoadEntities: true,
    logging: false,
  } as DataSourceOptions;
};
