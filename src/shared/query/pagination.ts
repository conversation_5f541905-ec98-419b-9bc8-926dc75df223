import { z } from 'zod';

export class Query {
  constructor(
    readonly page: number = DEFAULT_PAGE_NUMBER,
    readonly nbPerPage: number = DEFAULT_NB_PER_PAGE,
  ) {}

  skip(): number {
    return this.page ? (this.page - 1) * this.nbPerPage : 0;
  }

  take(): number {
    return this.nbPerPage;
  }
}

export class Page<T> {
  constructor(
    readonly items: T[],
    readonly pagination: {
      page: number;
      nbPerPage: number;
      total: number;
    },
  ) {}

  static fromQuery<T>(query: Query, items: T[], total: number): Page<T> {
    return new Page<T>(items, {
      page: query.page,
      nbPerPage: query.nbPerPage,
      total,
    });
  }

  static transform<T, R>(page: Page<T>, transform: (item: T) => R): Page<R> {
    return new Page<R>(page.items.map(transform), { ...page.pagination });
  }
}

export const DEFAULT_NB_PER_PAGE = 50;
export const DEFAULT_PAGE_NUMBER = 1;

export const paginationSchema = z.object({
  page: z.coerce
    .number()
    .int()
    .positive()
    .optional()
    .default(DEFAULT_PAGE_NUMBER),
  nbPerPage: z.coerce
    .number()
    .int()
    .positive()
    .optional()
    .default(DEFAULT_NB_PER_PAGE),
});

export type IPaginationParams = z.infer<typeof paginationSchema>;

/**
 * Creates a query schema with pagination and additional fields
 * @param additionalFields - Additional Zod schema fields specific to the entity
 * @returns A complete Zod schema with pagination and entity-specific fields
 */
export function createQuerySchema<T extends z.ZodRawShape>(
  additionalFields: T,
) {
  return paginationSchema
    .extend(additionalFields)
    .strict()
    .optional()
    .nullable();
}
