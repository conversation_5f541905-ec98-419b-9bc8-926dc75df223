import {
  GetSecretValueCommand,
  SecretsManagerClient,
} from '@aws-sdk/client-secrets-manager';
import { EnvironmentConfigNotFoundError } from 'src/shared/errors/environment-not-found.error';
import { getLogger } from 'src/shared/logging';

const logger = getLogger('env');

interface GetEnvOptions<T> {
  default?: T;
  isOptional?: boolean;
}

const secretManagerClient = process.env.SECRET_NAME
  ? new SecretsManagerClient()
  : undefined;

let secretsPromise: Promise<{ [key: string]: string }> | undefined = undefined;

async function getSecrets(): Promise<
  | {
      [key: string]: string;
    }
  | undefined
> {
  if (process.env.SECRET_NAME && !secretsPromise) {
    secretsPromise = (async () => {
      logger.debug('Retrieving secrets from secrets manager...');
      const response = await secretManagerClient!.send(
        new GetSecretValueCommand({
          SecretId: process.env.SECRET_NAME,
        }),
      );
      const secrets = JSON.parse(response.SecretString!);
      logger.debug('Successfully retrieved secrets from secrets manager.');
      return secrets;
    })();
  }

  return secretsPromise;
}

async function getSecret(name: string): Promise<string | undefined> {
  const secrets = await getSecrets();
  if (secrets === undefined) {
    return undefined;
  }
  return secrets[name];
}

export async function getConfigBoolean(
  name: string,
  options: GetEnvOptions<boolean> = {},
): Promise<boolean> {
  const strValue = await getConfig(name, {
    default: options.default?.toString(),
  });

  return strValue.toLowerCase() === 'true';
}

export async function getConfigNumber(
  name: string,
  options: GetEnvOptions<number> = {},
): Promise<number> {
  const strValue = await getConfig(name, {
    default: options.default?.toString(),
  });

  return parseFloat(strValue);
}

export async function getConfig(
  name: string,
  options: GetEnvOptions<string> = {},
): Promise<string> {
  let value = process.env.SECRET_NAME ? await getSecret(name) : undefined;

  if (value === undefined) {
    value = process.env[name];
  }

  if (value === undefined) {
    if (options.default === undefined && options.isOptional !== true) {
      throw new EnvironmentConfigNotFoundError(name);
    }
    return options.default!;
  }

  return value;
}

export async function getAwsClientConfig(): Promise<{ endpoint?: string }> {
  const clientConfig: { endpoint?: string } = {};
  const localstackEndpoint = await getConfig('LOCALSTACK_ENDPOINT', {
    isOptional: true,
    default: null,
  });

  if (localstackEndpoint) {
    logger.warn(
      'Running with localstack! This should not be the case on AWS, please verify your configuration.',
    );
    clientConfig.endpoint = localstackEndpoint;
  }

  return clientConfig;
}
