import { DateTime } from 'luxon';
import { VulogCarId, VulogFleetId } from 'src/shared/shared.type';
import { VulogCarLockedEventDto } from 'src/vg-event/vg-event.type';
import { z } from 'zod';

export enum VulogEventType {
  VEHICLE_LOCK = 'VEHICLE_LOCK',
  VEHICLE_UNLOCK = 'VEHICLE_UNLOCK',
  VEHICLE_CONNECTIVITY_ONLINE = 'VEHICLE_CONNECTIVITY_ONLINE',
  VEHICLE_CONNECTIVITY_OFFLINE = 'VEHICLE_CONNECTIVITY_OFFLINE',
  VEHICLE_CONNECTIVITY_INTERRUPTED = 'VEHICLE_CONNECTIVITY_INTERRUPTED',
  VEHICLE_WAKE_UP = 'VEHICLE_WAKE_UP',
}

export enum VulogEventOrigin {
  BOX = 'BOX',
  API = 'API',
  BLE = 'BLE',
  INTERNAL = 'INTERNAL',
}

export enum VulogGpsFix {
  NO = 'NO',
  GNSS_DR = 'GNSS_DR',
  DEAD_RECON = 'DEAD_RECON',
  FIX_2D = '2D',
  FIX_3D = '3D',
  TIME_ONLY = 'TIME_ONLY',
}

export const vulogEventSchema = z.object({
  type: z.string(),
  vehicleId: z.string(),
  fleetId: z.string(),
  insertionDate: z.coerce.date(),
  boxId: z.string().nullable().optional(),
  hasAlerts: z.boolean().nullable().optional(),
  date: z.coerce.date().nullable().optional(),
  failed: z.boolean().nullable().optional(),
  errorMessage: z.string().nullable().optional(),
  errorCode: z.number().nullable().optional(),
  sessionId: z.string().nullable().optional(),
  latitude: z.number().nullable().optional(),
  longitude: z.number().nullable().optional(),
  speed: z.number().nullable().optional(),
  altitude: z.number().nullable().optional(),
  nbSat: z.number().nullable().optional(),
  gpsFix: z.nativeEnum(VulogGpsFix).nullable().optional(),
  hdop: z.number().nullable().optional(),
  head: z.number().nullable().optional(),
  primaryFuelTank: z.object({}).nullable().optional(),
  secondaryFuelTank: z.object({}).nullable().optional(),
  tractionBattery: z.object({}).nullable().optional(),
  secondaryTractionBattery: z.object({}).nullable().optional(),
  mileage: z.number().nullable().optional(),
  auxBatteryVoltage: z.number().nullable().optional(),
  locked: z.boolean().nullable().optional(),
  doorsAndWindowsClosed: z.boolean().nullable().optional(),
  engineOn: z.boolean().nullable().optional(),
  card1Present: z.boolean().nullable().optional(),
  card2Present: z.boolean().nullable().optional(),
  keyPresent: z.boolean().nullable().optional(),
  ckhOk: z.boolean().nullable().optional(),
  charging: z.boolean().nullable().optional(),
  immobilized: z.boolean().nullable().optional(),
  boxStatus: z.string().nullable().optional(),
  transactionId: z.string().nullable().optional(),
  origin: z.nativeEnum(VulogEventOrigin).nullable().optional(),
});

type IVulogEvent = z.infer<typeof vulogEventSchema>;

export class VulogEventDto implements IVulogEvent {
  readonly type?: VulogEventType;
  readonly vehicleId?: string;
  readonly fleetId?: string;
  readonly insertionDate?: Date;

  constructor(props: IVulogEvent) {
    Object.assign(this, props);
  }

  toVulogCarLockedEventDto(): VulogCarLockedEventDto {
    const isLocked = this.type === VulogEventType.VEHICLE_LOCK;
    return new VulogCarLockedEventDto(
      new VulogCarId(this.vehicleId),
      new VulogFleetId(this.fleetId),
      DateTime.fromJSDate(this.insertionDate),
      isLocked,
    );
  }
}
