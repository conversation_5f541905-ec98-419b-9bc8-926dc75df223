import { CarStateService } from 'src/car-state/car-state.service';
import { FeatureFlag } from 'src/shared/feature-flag.module';
import { MessageResponseBody, OkResponse } from 'src/shared/http-response';
import { getLogger } from 'src/shared/logging';
import { VulogEventDto, VulogEventType } from 'src/vg-event/dto/vg-event.dto';

const logger = getLogger('vg-event.controller');

const EVENT_PROCESSED_MESSAGE = 'Event processed successfully';
const FEATURE_NOT_ENABLED = 'This feature has not been enabled.';
const EVENT_IGNORED_MESSAGE =
  'This event did not match a type used by the platform. This event has been ignored.';

export class VgEventController {
  constructor(
    private readonly featureFlag: FeatureFlag,
    private readonly carStateService: CarStateService,
  ) {}

  async handleEvent(
    vulogEvent: VulogEventDto,
  ): Promise<OkResponse<MessageResponseBody>> {
    const featureEnabled = await this.featureFlag.getEnableConsumingVgEvents();

    if (!featureEnabled) {
      logger.info('Feature not enabled');
      return OkResponse.fromMessage(FEATURE_NOT_ENABLED);
    }

    return this.handleWebhook(vulogEvent);
  }

  private async handleWebhook(
    vulogEvent: VulogEventDto,
  ): Promise<OkResponse<MessageResponseBody>> {
    switch (vulogEvent.type) {
      case VulogEventType.VEHICLE_LOCK: {
        const carLockEvent = vulogEvent
          .toVulogCarLockedEventDto()
          .toCarLockEvent();
        await this.carStateService.handleVehicleLockEvent(carLockEvent);
        break;
      }

      case VulogEventType.VEHICLE_UNLOCK: {
        const carLockEvent = vulogEvent
          .toVulogCarLockedEventDto()
          .toCarLockEvent();
        await this.carStateService.handleVehicleLockEvent(carLockEvent);
        break;
      }

      default:
        logger.info(`Unsupported event type: ${vulogEvent.type}`);
        return OkResponse.fromMessage(EVENT_IGNORED_MESSAGE);
    }

    return OkResponse.fromMessage(EVENT_PROCESSED_MESSAGE);
  }
}
