import { DateTime } from 'luxon';
import { CarLockEvent } from 'src/car-state/events/car-lock.event';
import { VulogCarId, VulogFleetId } from 'src/shared/shared.type';

export class VulogCarLockedEventDto {
  constructor(
    readonly vehicleId: VulogCarId,
    readonly fleetId: VulogFleetId,
    readonly insertionDate: DateTime,
    readonly isLocked: boolean,
  ) {}

  toCarLockEvent(): CarLockEvent {
    return new CarLockEvent(
      this.fleetId,
      this.vehicleId,
      this.isLocked,
      this.insertionDate,
    );
  }
}
