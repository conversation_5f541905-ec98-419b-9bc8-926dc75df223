import { CarStateModule } from 'src/car-state/car-state.module';
import { FeatureFlagModule } from 'src/shared/feature-flag.module';
import { VgEventController } from 'src/vg-event/vg-event.controller';

export class VgEventApiModule {
  readonly vgEventController: VgEventController;

  constructor(
    featureFlagModule: FeatureFlagModule,
    carStateModule: CarStateModule,
  ) {
    this.vgEventController = new VgEventController(
      featureFlagModule.featureFlag,
      carStateModule.carStateService,
    );
  }

  static module: VgEventApiModule;

  static async init(): Promise<VgEventApiModule> {
    if (!this.module) {
      this.module = new VgEventApiModule(
        await FeatureFlagModule.init(),
        await CarStateModule.init(),
      );
    }

    return this.module;
  }
}
