# How to set your local environment ?

Simply run the command from the project root:

```
npm run local:setup
```

Remember to set the environment values:

```
export NODE_ENV=local
```

## Complete your .env file

Once the command run, you should have a new `.env.local` file in your project.
Complete it with your credentials.

> Don't worry this file will be ignored by GIT.

### Vulog config

You can find all vulog config in postman Local or Stating environment

- `VULOG_API_KEY`: vulog_api_key
- `VULOG_CLIENT_ID`: vulog_client_id
- `VULOG_CLIENT_SECRET`: vulog_client_secret
- `VULOG_USERNAME`: vulog_username
- `VULOG_PASSWORD`: vulog_password

### Other services urls

- `CRS_BASE_API_URL`: Car Rental Service URL
- `STS_BASE_API_URL`: Station Service URL

### Reconciliation window config

- `LOCATION_RECONCILIATION_WINDOW_MINUTES`: Reconciliation window config in minutes, used to get the last locations from CRS with a delay up to this value
- `AVAILABILITY_RECONCILIATION_WINDOW_MINUTES`: Reconciliation window config in minutes, used to get the last reservation from CRS with a delay up to this value

### ConfigCat

You will need to retrieve the ConfigCat SDK key for Test Environment & set it to the env var `CONFIG_CAT_SDK_KEY`

# Keep this script up to date

If new value are needed in the `.env.local`, please add them to the `.env.template` file.

**Do not save any local credential** into the template as it will be saved into the repository.
