AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31

Globals:
  Function:
    Runtime: nodejs22.x
    Architectures: # pick the correct arch for your laptop :)
      - arm64
      # - x86_64
    MemorySize: 512
    Timeout: 60
    Environment:
      Variables:
        LOG_LEVEL: debug
        AWS_DEFAULT_REGION: ap-southeast-1
        DB_TYPE: postgres
        DB_HOST: host.docker.internal
        DB_PORT: 5432
        DB_USERNAME: postgres
        DB_PASSWORD: supermagicalpassword
        DB_NAME: car_service
        ENV_NAME: local
        JWT_SECRET: jwt-secret
        MESSAGE_BUS_TOPIC_ARN: arn:aws:sns:ap-southeast-1:000000000000:topic56789
        LOCALSTACK_ENDPOINT: http://host.docker.internal:4566
        VULOG_BASE_API_URL: https://vg-sta.vulog.net
        STS_BASE_API_URL: http://sts.int-dev:3006
        CRS_BASE_API_URL: http://crs.int-dev:3002
        VULOG_GRANT_TYPE: password
        VULOG_API_KEY:
        VULOG_CLIENT_ID:
        VULOG_CLIENT_SECRET:
        VULOG_USERNAME:
        VULOG_PASSWORD:
        CONFIG_CAT_SDK_KEY:
        LOCATION_RECONCILIATION_WINDOW_MINUTES: 20
        AVAILABILITY_RECONCILIATION_WINDOW_MINUTES: 20

Resources:
  HealthFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dist/functions/health.handler
      Events:
        Api:
          Type: Api
          Properties:
            Path: /health
            Method: get
  InternalsFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dist/functions/internal-api.handler
      Events:
        Api:
          Type: Api
          Properties:
            Path: /internal/api/v1/availability-map
            Method: post
  CarModelsFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dist/functions/car-models.handler
      Events:
        Direct:
          Type: Api
          Properties:
            Path: /admin/api/v1/car-models
            Method: any
        CatchAll:
          Type: Api
          Properties:
            Path: /admin/api/v1/car-models/{proxy+}
            Method: any
  CarsFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dist/functions/cars.handler
      Events:
        Direct:
          Type: Api
          Properties:
            Path: /admin/api/v1/cars
            Method: any
        CatchAll:
          Type: Api
          Properties:
            Path: /admin/api/v1/cars/{proxy+}
            Method: any
  VgEventsFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dist/functions/vg-events.handler
      Events:
        Api:
          Type: Api
          Properties:
            Path: /external/api/v1/vulog/vg-events
            Method: post
  CarLocationReconciliationFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dist/functions/car-location-reconciliation.handler

  BluesgEventsFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: dist/functions/bluesg-events.handler
