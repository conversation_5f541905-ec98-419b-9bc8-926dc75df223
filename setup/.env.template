

# AWS configuration
AWS_DEFAULT_REGION=ap-southeast-1

# Database configuration
DB_TYPE=postgres
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=supermagicalpassword
DB_NAME=car_service

# Vulog configuration
VULOG_BASE_API_URL=https://vg-sta.vulog.net
VULOG_API_KEY=
VULOG_CLIENT_ID=
VULOG_CLIENT_SECRET=
VULOG_USERNAME=
VULOG_PASSWORD=
VULOG_GRANT_TYPE=password

DB_SSL= # true or false
DB_SSL_CERT= # Base64 encoded SSL certificate

# AWS RDS IAM authentication
DB_AUTH_MODE= # rds_iam to use RDS IAM, empty otherwise

# Admin Auth
AUTH0_SERVER_URL=auth0.local
CONFIG_CAT_AUTH_SDK_KEY=config-cat-auth-sdk-key
