name: 'car-service'

services:
  car-service-db:
    image: postgres:16
    restart: always
    ports:
      - '${DB_PORT}:5432'
    environment:
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_NAME}
    volumes:
      - postgres-data:/var/lib/postgresql/data
  car-service-localstack:
    container_name: 'car-service-localstack-main'
    image: localstack/localstack:stable
    restart: always
    ports:
      - '127.0.0.1:4566:4566' # LocalStack Gateway
      - '127.0.0.1:4510-4559:4510-4559' # external services port range
    environment:
      # LocalStack configuration: https://docs.localstack.cloud/references/configuration/
      MAIN_CONTAINER_NAME: 'car-service-localstack-main'
      SERVICES: 'sns,sqs'
    volumes:
      - localstack-data:/var/lib/localstack
      - /var/run/docker.sock:/var/run/docker.sock
      - ./localstack_setup:/etc/localstack/init/ready.d

volumes:
  postgres-data:
  localstack-data:
