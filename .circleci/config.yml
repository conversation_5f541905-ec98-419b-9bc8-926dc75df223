version: 2.1
orbs:
  aws-cli: circleci/aws-cli@5.2.0
  bluesg: bluesg/bluesg-orb@0.7.65

parameters:
  node-version:
    type: string
    default: 22.14.0

  release:
    type: string
    default: << pipeline.git.revision >>

  slack-channel:
    type: string
    description: 'Slack channel used to send deployment notifications'
    default: C08FSRSA5BJ # car-service

executors:
  node-executor:
    docker:
      - image: cimg/node:<< pipeline.parameters.node-version >>
  cdk-executor:
    docker:
      - image: 886797425683.dkr.ecr.ap-southeast-1.amazonaws.com/platform-cdk:latest
        aws_auth:
          oidc_role_arn: arn:aws:iam::886797425683:role/CircleCi_deployment

jobs:
  test:
    executor: node-executor
    resource_class: medium
    steps:
      - checkout
      - setup_remote_docker:
          version: docker23
          docker_layer_caching: true
      - restore_cache:
          name: Restore NPM Package Cache
          keys:
            - v1-npm-deps-{{ checksum "package-lock.json" }}
      - run:
          name: Install Dependencies
          command: npm install
      - run:
          name: Build
          command: npm run build
      - run:
          name: Test
          command: npm run test:cov
          environment:
            JEST_JUNIT_OUTPUT_DIR: ./reports/junit/test-result.xml
      - store_test_results:
          path: ./reports/
      - persist_to_workspace:
          root: coverage
          paths:
            - lcov.info
      - save_cache:
          name: Save NPM Package Cache
          key: v1-npm-deps-{{ checksum "package-lock.json" }}
          paths:
            - node_modules
  lint:
    executor: node-executor
    resource_class: medium
    steps:
      - checkout
      - restore_cache:
          name: Restore NPM Package Cache
          keys:
            - v1-npm-deps-{{ checksum "package-lock.json" }}
      - run:
          name: Install Dependencies
          command: |
            npm install
      - run:
          name: Lint
          command: |
            npm run check:ci:format
            npm run check:ci:lint
      - save_cache:
          name: Save NPM Package Cache
          key: v1-npm-deps-{{ checksum "package-lock.json" }}
          paths:
            - node_modules
  build:
    executor: node-executor
    resource_class: medium
    steps:
      - checkout
      - restore_cache:
          name: Restore NPM Package Cache
          keys:
            - v1-npm-deps-{{ checksum "package-lock.json" }}
      - run:
          name: Install Dependencies
          command: |
            npm install
      - run:
          name: Build
          command: |
            npm run build
            npm run package
      - save_cache:
          name: Save NPM Package Cache
          key: v1-npm-deps-{{ checksum "package-lock.json" }}
          paths:
            - node_modules
  deploy-infra:
    executor: cdk-executor
    parameters:
      aws-region:
        type: string
        default: 'ap-southeast-1'
      cdk-stack-name:
        type: string
      cdk-role-arn:
        type: string
    steps:
      - aws-cli/setup:
          role_arn: << parameters.cdk-role-arn >>
          region: << parameters.aws-region >>
      - run:
          name: Run a CDK command
          command: |
            aws sts get-caller-identity
            npm run cdk diff --ci true --require-approval never << parameters.cdk-stack-name >>
  deploy:
    executor: node-executor
    resource_class: medium
    parameters:
      aws-region:
        type: string
        default: 'ap-southeast-1'
      role-arn:
        type: string
      deployment-env:
        type: string
      bucket-name:
        type: string
      health-function-name:
        type: string
      operation:
        type: enum
        enum: ['deploy-staging', 'deploy-uat', 'deploy-prod']
    environment:
      DEPLOYMENT_OPERATION: << parameters.operation >>
    steps:
      - checkout
      - add_ssh_keys:
          fingerprints:
            - 'SHA256:od2Wy2R4gxO58ud1Kk4G+cEbMJGoCYTiVUJUO+Y+2TE'
      - aws-cli/setup:
          role_arn: << parameters.role-arn >>
          region: << parameters.aws-region >>
      - run:
          name: Verify AWS access
          command: |
            aws sts get-caller-identity
      - restore_cache:
          name: Restore NPM Package Cache
          keys:
            - v1-npm-deps-{{ checksum "package-lock.json" }}
      - run:
          name: Install Dependencies
          command: |
            npm install
      - run:
          name: Build
          command: |
            npm run build
            npm run package
      - run:
          name: Upload zip packages
          command: |
            bash scripts/upload.sh << parameters.bucket-name >> deployments/deployment.<< parameters.deployment-env >>.txt
      - run:
          name: Deploy CDK
          command: |
            echo "Deploy CDK here :)"
      - run:
          name: Update Lambda Functions code
          command: |
            bash scripts/update_code.sh << parameters.bucket-name >> deployments/deployment.<< parameters.deployment-env >>.txt
      - run:
          name: Migrate DB Schema
          command: |
            bash scripts/run_migrations.sh << parameters.health-function-name >>
      - bluesg/tag-deployment-history:
          operation: DEPLOYMENT_OPERATION
      - bluesg/send-deployment-to-linearb
      - bluesg/slack-deployment-success:
          channel-id: << pipeline.parameters.slack-channel >>
          service-name: ${CIRCLE_PROJECT_REPONAME}
          environment: << parameters.deployment-env >>
      - bluesg/slack-deployment-failure:
          channel-id: << pipeline.parameters.slack-channel >>
          service-name: ${CIRCLE_PROJECT_REPONAME}
          environment: << parameters.deployment-env >>

workflows:
  test-build-deploy:
    jobs:
      - test:
          context:
            - github-context
      - lint:
          context:
            - github-context
      - bluesg/code-scan:
          requires:
            - test
            - lint
          name: code-scan
          fail-with-quality-gate: 'true'
          revision: << pipeline.parameters.release >>
          project-version: << pipeline.parameters.release >>
          context:
            - sonar-context
      - build:
          context:
            - github-context
          requires:
            - code-scan
      - bluesg/tag-version:
          name: semantic-version
          image-tag: << pipeline.parameters.release >>
          tag-ecr: false
          context:
            - github-context
            - slack-context
          requires:
            - build
          filters:
            branches:
              only:
                - main
      - deploy-infra:
          name: deploy-infra-dev
          context: aws-cdk-context
          cdk-role-arn: 'arn:aws:iam::116431628221:role/circleci_cdk_deploy'
          cdk-stack-name: 'CarServiceLambdaDEV'
          requires:
            - build
            - semantic-version
          filters:
            branches:
              only:
                - main
                - /ci\/.*/
      - deploy:
          name: deploy-dev
          context:
            - aws-cdk-context
            - github-context
            - linearb-context
            - staging-context
          role-arn: arn:aws:iam::116431628221:role/CarServiceLambdaDEV-deployment-role-dev
          deployment-env: dev
          bucket-name: bluesg-car-service-dev
          health-function-name: car-service-health-function-dev
          operation: deploy-staging
          requires:
            - deploy-infra-dev
          filters:
            branches:
              only:
                - main
                - /ci\/.*/
      - deploy-uat-approval:
          type: approval
          requires:
            - deploy-dev
          filters:
            branches:
              only:
                - main
                - /ci\/.*/
      - deploy:
          name: deploy-uat
          context:
            - aws-cdk-context
            - github-context
            - linearb-context
            - uat-context
          role-arn: arn:aws:iam::331142702324:role/CarServiceLambdaUAT-deployment-role-uat
          deployment-env: uat
          bucket-name: bluesg-car-service-uat
          health-function-name: car-service-health-function-uat
          operation: deploy-uat
          requires:
            - deploy-uat-approval
          filters:
            branches:
              only:
                - main
                - /ci\/.*/
      - deploy-prod-approval:
          type: approval
          requires:
            - deploy-uat
          filters:
            branches:
              only:
                - main
                - /ci\/.*/
      - deploy:
          name: deploy-prod
          context:
            - aws-cdk-context
            - github-context
            - linearb-context
            - prod-context
          role-arn: arn:aws:iam::295317914546:role/CarServiceLambdaPROD-deployment-role-prod
          deployment-env: prod
          bucket-name: bluesg-car-service-prod
          health-function-name: car-service-health-function-prod
          operation: deploy-prod
          requires:
            - deploy-prod-approval
          filters:
            branches:
              only:
                - main
                - /ci\/.*/
