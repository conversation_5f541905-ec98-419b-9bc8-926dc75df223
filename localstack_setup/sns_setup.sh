#!/usr/bin/env bash

set -euo pipefail

# enable debug
# set -x

# source: https://stackoverflow.com/a/70063706
echo "configuring sns linked with sqs"
echo "==================="
LOCALSTACK_HOST=localhost
AWS_REGION=ap-southeast-1
LOCALSTACK_DUMMY_ID=000000000000

get_all_queues() {
    awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 sqs list-queues --region ${AWS_REGION}
}


create_queue() {
    local QUEUE_NAME_TO_CREATE=$1
    awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 sqs create-queue --queue-name ${QUEUE_NAME_TO_CREATE} --region ${AWS_REGION}
}


get_all_topics() {
    awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 sns list-topics --region ${AWS_REGION}
}

create_topic() {
    local TOPIC_NAME_TO_CREATE=$1
    awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 sns create-topic --name ${TOPIC_NAME_TO_CREATE} --query 'TopicArn' --output text --region ${AWS_REGION}
}

link_queue_and_topic() {
    local TOPIC_ARN_TO_LINK=$1
    local QUEUE_ARN_TO_LINK=$2
    awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 sns subscribe --topic-arn ${TOPIC_ARN_TO_LINK} --protocol sqs --notification-endpoint ${QUEUE_ARN_TO_LINK} --region ${AWS_REGION}
}

guess_queue_arn_from_name() {
    local QUEUE_NAME=$1
    echo "arn:aws:sqs:${AWS_REGION}:${LOCALSTACK_DUMMY_ID}:$QUEUE_NAME"
}

QUEUE_NAME="queue123"
TOPIC_NAME="topic56789"

echo "creating topic $TOPIC_NAME"
TOPIC_ARN=$(create_topic ${TOPIC_NAME})
echo "created topic: $TOPIC_ARN"

echo "creating queue $QUEUE_NAME"
QUEUE_URL=$(create_queue ${QUEUE_NAME})
echo "created queue: $QUEUE_URL"
QUEUE_ARN=$(guess_queue_arn_from_name $QUEUE_NAME)

echo "linking topic $TOPIC_ARN to queue $QUEUE_ARN"
LINKING_RESULT=$(link_queue_and_topic $TOPIC_ARN $QUEUE_ARN)
echo "linking done:"
echo "$LINKING_RESULT"

echo "all topics are:"
echo "$(get_all_topics)"

echo "all queues are:"
echo "$(get_all_queues)"
