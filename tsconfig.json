{"compilerOptions": {"rootDir": ".", "baseUrl": "./", "target": "ES2022", "module": "ES2022", "moduleResolution": "node", "lib": ["ES2022", "DOM"], "strict": true, "preserveConstEnums": true, "noEmit": true, "sourceMap": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "declaration": true, "noImplicitAny": true, "strictNullChecks": false, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": false, "inlineSources": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "typeRoots": ["./node_modules/@types"], "types": ["jest"]}, "ts-node": {"require": ["tsconfig-paths/register"]}, "exclude": ["node_modules", "dist"]}