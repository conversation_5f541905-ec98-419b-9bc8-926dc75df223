import { fail } from 'assert';
import { TokenPayload } from 'src/auth/authentication/token';
import {
  CarModel,
  CarModelHrid,
  CarModelId,
  CarModelName,
} from 'src/car-model/car-model.type';
import { CarModelRepository } from 'src/car-model/entities/car-model.repository';
import { CarModelHridAlreadyExistsError } from 'src/car-model/errors/car-model.error';
import { getDataSource } from 'src/shared/datasources/datasource';
import { InternalServerError } from 'src/shared/errors/http.error';
import { InvocationContext } from 'src/shared/invocation-context';

describe('Car Model Repository', () => {
  let carModelRepository: CarModelRepository;
  let carModelId: CarModelId;

  beforeAll(async () => {
    InvocationContext.init('fake-request-id', 'fake-correlation-id');
    InvocationContext.setToken({
      payload: {
        userId: 'test-user-id',
      } as TokenPayload,
      signature: 'foobar',
    });

    carModelRepository = new CarModelRepository(await getDataSource());
  });

  it('should insert car model', async () => {
    const carModel = new CarModel(
      null,
      new CarModelName('test car model'),
      new CarModelHrid('test hrid'),
    );

    const saved = await carModelRepository.createOrFail(carModel);

    expect(saved.id).toBeTruthy();
    expect(saved.createdBy).toBe('test-user-id');
    expect(saved.modifiedBy).toBe('test-user-id');

    carModelId = saved.id;
  });

  it('should not insert same hrid', async () => {
    const carModel = new CarModel(
      null,
      new CarModelName('test car model'),
      new CarModelHrid('test hrid'),
    );

    try {
      await carModelRepository.createOrFail(carModel);
    } catch (error) {
      if (error instanceof CarModelHridAlreadyExistsError) {
        expect(error.code).toBe('CAR_MODEL_ALREADY_EXISTS');
        expect(error.message).toBe(
          'Car model with hrid test hrid already exists',
        );
        return;
      }
    }

    fail('should not reach here');
  });

  it('should not allow hard remove', async () => {
    try {
      await carModelRepository.remove(
        await carModelRepository.findOneBy({ id: carModelId.value }),
      );
    } catch (error) {
      if (error instanceof InternalServerError) {
        expect(error.message).toBe('Hard remove is not supported!');
        return;
      }
    }
    fail('should not reach here');
  });

  it('should soft remove', async () => {
    await carModelRepository.softRemoveById(carModelId.value);

    const deleted = await carModelRepository.findOne({
      where: { id: carModelId.value },
      withDeleted: true,
    });

    expect(deleted.deletedBy).toBe('test-user-id');
  });

  afterAll(() => {
    InvocationContext.clear();
  });
});
