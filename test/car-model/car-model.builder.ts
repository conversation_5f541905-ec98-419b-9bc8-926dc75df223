import * as falso from '@ngneat/falso';
import { DateTime } from 'luxon';
import {
  CarModel,
  CarModelDescription,
  CarModelHrid,
  CarModelId,
  CarModelName,
} from 'src/car-model/car-model.type';
import { MockBuilder } from 'test/shared/mock.builder';

export class CarModelBuilder extends MockBuilder<CarModel> {
  protected result: CarModel = new CarModel(
    new CarModelId(falso.randUuid()),
    new CarModelName(falso.randText()),
  );

  withId(id: CarModelId): CarModelBuilder {
    this.result.id = id;
    return this;
  }

  withName(name: CarModelName): CarModelBuilder {
    this.result.name = name;
    return this;
  }

  withHrid(hrid: string): CarModelBuilder {
    this.result.hrid = new CarModelHrid(hrid);
    return this;
  }

  withDescription(description: string): CarModelBuilder {
    this.result.description = new CarModelDescription(description);
    return this;
  }

  withCreatedBy(createdBy: string): CarModelBuilder {
    this.result.createdBy = createdBy;
    return this;
  }

  withModifiedBy(modifiedBy: string): CarModelBuilder {
    this.result.modifiedBy = modifiedBy;
    return this;
  }

  withDeletedBy(deletedBy: string): CarModelBuilder {
    this.result.deletedBy = deletedBy;
    return this;
  }

  withCreatedAt(createdAt: DateTime): CarModelBuilder {
    this.result.createdAt = createdAt;
    return this;
  }

  withModifiedAt(modifiedAt: DateTime): CarModelBuilder {
    this.result.modifiedAt = modifiedAt;
    return this;
  }

  withDeletedAt(deletedAt: DateTime): CarModelBuilder {
    this.result.deletedAt = deletedAt;
    return this;
  }

  build: () => CarModel = () => {
    return this.result;
  };
}
