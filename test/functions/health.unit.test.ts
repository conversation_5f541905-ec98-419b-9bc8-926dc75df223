import { jest } from '@jest/globals';
import { HealthResponse } from 'src/functions/health';
import { StandardResponseBody } from 'src/shared/http-response';
import { DataSource } from 'typeorm';

jest.unstable_mockModule('src/shared/datasources/datasource', () => ({
  getDataSource: jest.fn(),
}));

const { getDataSource } = await import('src/shared/datasources/datasource');
const { healthHandler } = await import('src/functions/health');

const mockDataSource = {
  isInitialized: true,
  showMigrations: jest.fn(),
  runMigrations: jest.fn(),
  initialize: jest.fn(),
} as Partial<DataSource>;

(getDataSource as jest.Mock).mockReturnValue(mockDataSource);

describe('Health Function', () => {
  it('should return status UP with no pending migrations', async () => {
    (mockDataSource.showMigrations as jest.Mock).mockReturnValue(false);

    const response = await healthHandler();

    const body: StandardResponseBody<HealthResponse> = JSON.parse(
      response.body,
    );

    expect(response.statusCode).toBe(200);
    expect(response.headers).toEqual({
      'Content-Type': 'application/json',
    });
    expect(body.result.status).toEqual('UP');
    expect(body.result.migrationResult).toEqual('No pending migrations found.');
  });

  it('should return status UP with pending migrations', async () => {
    (mockDataSource.showMigrations as jest.Mock).mockReturnValue(true);
    (mockDataSource.runMigrations as jest.Mock).mockReturnValue([
      { name: 'migration1' },
      { name: 'migration2' },
    ]);

    const response = await healthHandler();

    const body: StandardResponseBody<HealthResponse> = JSON.parse(
      response.body,
    );

    expect(response.statusCode).toBe(200);
    expect(response.headers).toEqual({
      'Content-Type': 'application/json',
    });
    expect(body.result.status).toEqual('UP');
    expect(body.result.migrationResult).toContain('Running migrations...');
    expect(body.result.migrationResult).toContain('migration1');
    expect(body.result.migrationResult).toContain('migration2');
  });
});
