import {
  CarModelCreatedEvent,
  CarModelDeletedEvent,
  EventName,
} from '@bluesg-2/event-library';
import { afterAll, beforeAll, describe, expect, it } from '@jest/globals';
import { jest } from '@jest/globals';
import { randomUUID } from 'crypto';
import { CarModelRepository } from 'src/car-model/entities/car-model.repository';
import { CarRepository } from 'src/car/entities/car.repository';
import { getDataSource } from 'src/shared/datasources/datasource';
import {
  CORRELATION_ID_HEADER,
  CORRELATION_ID_RESPONSE_KEY,
} from 'src/shared/middlewares/invocation-context.middleware';
import { AuthModuleMock } from 'test/auth/helpers/auth.module.mock';
import {
  getResponse,
  getSqsMessage,
  mockApiGatewayEvent,
} from 'test/shared/test-util';
import { DataSource } from 'typeorm';

jest.unstable_mockModule('src/shared/feature-flag.module', () => {
  const mockFeatureFlag = {
    getEnableConsumingVgEvents: jest.fn().mockResolvedValue(true as never),
    getEnableCsCarAvailabilityUpdatedEvent: jest
      .fn()
      .mockResolvedValue(true as never),
  };

  return {
    FeatureFlag: jest.fn().mockImplementation(() => mockFeatureFlag),
    FeatureFlagModule: {
      init: jest.fn().mockResolvedValue({
        featureFlag: mockFeatureFlag,
      } as never),
      module: mockFeatureFlag,
    },
  };
});

const { handler } = await import('src/functions/car-models');
const { handler: carsHandler } = await import('src/functions/cars');

describe('Car Model Function', () => {
  let dataSource: DataSource;
  beforeAll(async () => {
    await AuthModuleMock.mock();
    dataSource = await getDataSource();
  });
  let createdCarModelId: string;

  const testCarModel = {
    name: 'Test Model',
    description: 'Test Description',
  };

  describe('POST /car-models', () => {
    it('should create a car model', async () => {
      const event = mockApiGatewayEvent(
        'POST',
        '/admin/api/v1/car-models',
        testCarModel,
      );
      const correlationId = randomUUID();
      const { headers, statusCode, body } = await getResponse(
        handler,
        event,
        correlationId,
      );

      expect(statusCode).toBe(201);
      expect(headers[CORRELATION_ID_HEADER]).toBe(correlationId);
      expect(body.correlationId).toBe(correlationId);
      expect(body).toHaveProperty('success');
      expect(body).toHaveProperty('result');
      expect(body.result).toHaveProperty('id');
      expect(body.result).toHaveProperty('hrid');
      expect(body.result.name).toBe(testCarModel.name);
      expect(body.result.createdBy).toBe('123');
      expect(body.result.description).toBe(testCarModel.description);

      createdCarModelId = body.result.id;

      const sqsMessage: { Message: string; MessageAttributes: unknown } =
        await getSqsMessage();

      expect(sqsMessage.MessageAttributes).toEqual({
        eventType: {
          Type: 'String',
          Value: EventName.CarModelCreatedEvent,
        },
      });

      const message: CarModelCreatedEvent = JSON.parse(sqsMessage.Message);

      expect(message.correlationId).toBe(body[CORRELATION_ID_RESPONSE_KEY]);
      expect(message.event).toBe(EventName.CarModelCreatedEvent);
      expect(message.sender).toBe('car-service-test');
      expect(message.sentAt).toBeDefined();
      expect(message.payload.id).toBe(body.result.id);
      expect(message.payload.hrid).toBe(body.result.hrid);
      expect(message.payload.description).toBe(body.result.description);
    });

    it('should not create a car model with same hrid', async () => {
      const event = mockApiGatewayEvent(
        'POST',
        '/admin/api/v1/car-models',
        testCarModel,
      );
      const correlationId = randomUUID();
      const { headers, statusCode, body } = await getResponse(
        handler,
        event,
        correlationId,
      );

      expect(statusCode).toBe(409);
      expect(headers[CORRELATION_ID_HEADER]).toBe(correlationId);
      expect(body).toHaveProperty('message');
      expect(body.message).toBe(
        'Car model with hrid test-model already exists',
      );
      expect(body.code).toBe('CAR_MODEL_ALREADY_EXISTS');
      expect(body.correlationId).toBe(correlationId);
    });

    it('should fail to create car model without name', async () => {
      const event = mockApiGatewayEvent('POST', '/admin/api/v1/car-models', {
        description: 'No Name',
      });
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(400);
      expect(body).toHaveProperty('message');
    });
  });

  describe('GET /car-models/{id}', () => {
    it('should get car model by id', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        `/admin/api/v1/car-models/${createdCarModelId}`,
        null,
        { carModelId: createdCarModelId },
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(200);
      expect(body.result.id).toBe(createdCarModelId);
    });

    it('should return 400 for invalid car model id', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        `/admin/api/v1/car-models/0000000000`,
        null,
        { carModelId: '0000000000' },
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(400);
      expect(body).toHaveProperty('message');
      expect(body.message).toBe('Please provide correct UUID for Car Model');
    });

    it('should return 404 for non-existent car model', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        '/admin/api/v1/car-models/00000000-0000-0000-0000-000000000000',
        null,
        { carModelId: '00000000-0000-0000-0000-000000000000' },
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(404);
      expect(body).toHaveProperty('message');
    });
  });

  describe('DELETE /car-models/{id}', () => {
    it('should delete car model', async () => {
      const event = mockApiGatewayEvent(
        'DELETE',
        `/admin/api/v1/car-models/${createdCarModelId}`,
        null,
        { carModelId: createdCarModelId },
      );
      const { headers, statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(200);
      expect(headers).toHaveProperty(CORRELATION_ID_HEADER);
      expect(body.success).toBe(true);
      expect(body.message).toBe('The car model has been deleted.');
      expect(body.correlationId).toBe(headers[CORRELATION_ID_HEADER]);

      const sqsMessage: { Message: string; MessageAttributes: unknown } =
        await getSqsMessage();

      expect(sqsMessage.MessageAttributes).toEqual({
        eventType: {
          Type: 'String',
          Value: EventName.CarModelDeletedEvent,
        },
      });

      const message: CarModelDeletedEvent = JSON.parse(sqsMessage.Message);

      expect(message.correlationId).toBe(headers[CORRELATION_ID_HEADER]);
      expect(message.event).toBe(EventName.CarModelDeletedEvent);
      expect(message.sender).toBe('car-service-test');
      expect(message.sentAt).toBeDefined();
      expect(message.payload.id).toBe(createdCarModelId);
    });

    it('should return 404 for non-existent car model', async () => {
      const event = mockApiGatewayEvent(
        'DELETE',
        '/admin/api/v1/car-models/00000000-0000-0000-0000-000000000000',
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(404);
      expect(body).toHaveProperty('message');
      expect(body.message).toBe(
        'The car model 00000000-0000-0000-0000-000000000000 could not be found',
      );
    });

    it('should return 400 for malformed id', async () => {
      const event = mockApiGatewayEvent(
        'DELETE',
        '/admin/api/v1/car-models/foobar',
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(400);
      expect(body).toHaveProperty('message');
      expect(body.message).toBe('Please provide correct UUID for Car Model');
    });

    it('should return 400 when delete car model with active car', async () => {
      const createCarModelEvent = mockApiGatewayEvent(
        'POST',
        '/admin/api/v1/car-models',
        {
          name: 'Car Model in use',
        },
      );
      const createCarModelResponse = await getResponse(
        handler,
        createCarModelEvent,
      );

      expect(createCarModelResponse.statusCode).toBe(201);

      const carModelId = createCarModelResponse.body.result.id;

      const createCarEvent = mockApiGatewayEvent('POST', '/admin/api/v1/cars', {
        carModelId: carModelId,
        plateNumber: 'plate',
        vin: 'vin',
        registrationDate: '2025-03-09T06:07:18.734Z',
      });

      const createCarResponse = await getResponse(carsHandler, createCarEvent);

      expect(createCarResponse.statusCode).toBe(201);

      const carId = createCarResponse.body.result.id;

      const deleteCarModelInUseEvent = mockApiGatewayEvent(
        'DELETE',
        `/admin/api/v1/car-models/${carModelId}`,
      );

      const deleteCarModelInUseResponse = await getResponse(
        handler,
        deleteCarModelInUseEvent,
      );

      expect(deleteCarModelInUseResponse.statusCode).toBe(400);
      expect(deleteCarModelInUseResponse.body.code).toBe(
        'CAR_MODEL_CURRENTLY_IN_USE',
      );
      expect(deleteCarModelInUseResponse.body.message).toBe(
        `The car model ${carModelId} is currently in use`,
      );

      const deleteCarEvent = mockApiGatewayEvent(
        'DELETE',
        `/admin/api/v1/cars/${carId}`,
      );

      const deleteCarResponse = await getResponse(carsHandler, deleteCarEvent);

      expect(deleteCarResponse.statusCode).toBe(200);

      // the car model should be deleteable now :)
      const deleteCarModelEvent = mockApiGatewayEvent(
        'DELETE',
        `/admin/api/v1/car-models/${carModelId}`,
      );

      const deleteCarModelResponse = await getResponse(
        handler,
        deleteCarModelEvent,
      );

      expect(deleteCarModelResponse.statusCode).toBe(200);
    });

    afterAll(async () => {
      await new CarRepository(dataSource).delete({});
      await new CarModelRepository(dataSource).delete({});
    });
  });

  describe('GET /car-models', () => {
    beforeAll(async () => {
      const event = mockApiGatewayEvent('POST', '/admin/api/v1/car-models', {
        hrid: 'test-model-1',
        name: 'Test Model 1',
        description: 'Test Description 1',
      });
      await getResponse(handler, event);

      const event2 = mockApiGatewayEvent('POST', '/admin/api/v1/car-models', {
        hrid: 'test-model-2',
        name: 'Test Model 2',
        description: 'Test Description 2',
      });
      await getResponse(handler, event2);
    });
    it('should get all car models', async () => {
      const event = mockApiGatewayEvent('GET', '/admin/api/v1/car-models');
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(200);
      expect(Array.isArray(body.result)).toBe(true);
      expect(body.result.length).toBeGreaterThan(0);
    });

    it('should filter car models by name', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        '/admin/api/v1/car-models',
        null,
        null,
        {
          name: 'Test Model',
        },
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(200);
      expect(Array.isArray(body.result)).toBe(true);
      expect(body.result[0].name).toContain('Test');
    });

    it('should filter car models by hrid', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        '/admin/api/v1/car-models',
        null,
        null,
        {
          hrid: 'test-model',
        },
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(200);
      expect(Array.isArray(body.result)).toBe(true);
      expect(body.result[0].hrid).toContain('test-model');
    });

    it('should filter car models hrid case sensitive keyword', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        '/admin/api/v1/car-models',
        null,
        null,
        {
          hrid: 'Test',
        },
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(200);
      expect(Array.isArray(body.result)).toBe(true);
      expect(body.result.length).toBe(2);
    });

    it('should filter car models by name case sensitive keyword', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        '/admin/api/v1/car-models',
        null,
        null,
        {
          name: 'test',
        },
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(200);
      expect(Array.isArray(body.result)).toBe(true);
      expect(body.result.length).toBe(2);
    });
  });
});
