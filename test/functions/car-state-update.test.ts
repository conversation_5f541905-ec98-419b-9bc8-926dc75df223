import { jest } from '@jest/globals';
import { Context } from 'aws-lambda';
import { DateTime } from 'luxon';
import { CarModel } from 'src/car-model/car-model.type';
import { CarModelRepository } from 'src/car-model/entities/car-model.repository';
import { CarStateRepository } from 'src/car-state/entities/car-state.repository';
import { Car } from 'src/car/car.type';
import { CarRepository } from 'src/car/entities/car.repository';
import { getDataSource } from 'src/shared/datasources/datasource';
import { InvocationContext } from 'src/shared/invocation-context';
import { VulogCarId } from 'src/shared/shared.type';
import { CarModelBuilder } from 'test/car-model/car-model.builder';
import { CarStateEntityBuilder } from 'test/car-state/entities/car-state.entity.builder';
import { CarBuilder } from 'test/car/car.builder';
import { MockVulogClient } from 'test/mocks/vulog-client.mock';
import {
  initInvocationContext,
  mockScheduledEvent,
} from 'test/shared/test-util';
import { v4 as uuidv4 } from 'uuid';

jest.unstable_mockModule('src/shared/feature-flag.module', () => {
  const mockFeatureFlag = {
    getEnableConsumingVgEvents: jest.fn().mockResolvedValue(true as never),
    getEnableCsCarAvailabilityUpdatedEvent: jest
      .fn()
      .mockResolvedValue(true as never),
  };

  return {
    FeatureFlag: jest.fn().mockImplementation(() => mockFeatureFlag),
    FeatureFlagModule: {
      init: jest.fn().mockResolvedValue({
        featureFlag: mockFeatureFlag,
      } as never),
      module: mockFeatureFlag,
    },
  };
});

const mockVulogClient = new MockVulogClient();
jest.unstable_mockModule('src/vulog/vulog-client', () => {
  return {
    VulogClient: jest.fn().mockImplementation(() => {
      return mockVulogClient;
    }),
  };
});

const { handler } = await import('src/functions/car-state-update');

describe('Car State Update', () => {
  let carModelRepository: CarModelRepository;
  let carRepository: CarRepository;
  let carStateRepository: CarStateRepository;
  let carModel: CarModel;

  beforeAll(async () => {
    initInvocationContext();

    carModelRepository = new CarModelRepository(await getDataSource());
    carRepository = new CarRepository(await getDataSource());
    carStateRepository = new CarStateRepository(await getDataSource());

    await carStateRepository.delete({});
    await carRepository.delete({});
    await carModelRepository.delete({});

    carModel = await carModelRepository.createOrFail(
      new CarModelBuilder().build(),
    );

    InvocationContext.clear();
  });

  describe('cars with existing car states', () => {
    let car: Car;
    let fleetId: string;
    let vehicleId: string;
    afterAll(async () => {
      await carStateRepository.delete({});
      await carRepository.delete({});
    });

    beforeAll(async () => {
      initInvocationContext();
      fleetId = 'test-fleet-id';
      vehicleId = 'vulog-car-id-1';
      const car1Entity = new CarBuilder()
        .withCarModelId(carModel.id)
        .withExternalFleetId(fleetId)
        .withExternalCarId(vehicleId)
        .build();

      car = await carRepository.insertOne(car1Entity);

      const carStateId = uuidv4();
      const initialCarState = new CarStateEntityBuilder()
        .buildId(carStateId)
        .buildCarId(car.id.value)
        .buildLocked(false)
        .buildEvBatteryLevel(50)
        .buildAllDoorsWindowsClosed(false)
        .buildEngineOn(true)
        .buildCablePlugged(true)
        .buildCharging(true)
        .buildProviderPayload({ oldData: true })
        .buildCreatedAt(DateTime.now().minus({ minutes: 5 }).toJSDate())
        .buildModifiedAt(DateTime.now().minus({ minutes: 30 }).toJSDate())
        .build();

      await carStateRepository.save(initialCarState);

      mockVulogClient.setVehicleIds([new VulogCarId(vehicleId)]);

      InvocationContext.clear();
    });

    it('should update', async () => {
      await handler(mockScheduledEvent(), {} as Context);

      const updatedCarState = await carStateRepository.findOne({
        where: { carId: car.id.value },
      });

      expect(updatedCarState).toBeDefined();
      expect(updatedCarState.locked).toBe(true);
      expect(updatedCarState.evBatteryLevel).toBe('80');
      expect(updatedCarState.allDoorsWindowsClosed).toBe(true);
      expect(updatedCarState.engineOn).toBe(false);
      expect(updatedCarState.cablePlugged).toBe(false);
      expect(updatedCarState.charging).toBe(false);

      const oldModifiedAt = new Date(Date.now() - 30 * 60 * 1000);
      expect(updatedCarState.modifiedAt.getTime()).toBeGreaterThan(
        oldModifiedAt.getTime(),
      );
    });
  });

  describe('cars without existing car states', () => {
    let car: Car;
    let fleetId: string;
    let vehicleId: string;
    afterAll(async () => {
      await carStateRepository.delete({});
      await carRepository.delete({});
    });

    beforeAll(async () => {
      initInvocationContext();
      fleetId = 'test-fleet-id-2';
      vehicleId = 'vulog-car-id-2';
      const carEntity = new CarBuilder()
        .withCarModelId(carModel.id)
        .withExternalFleetId(fleetId)
        .withExternalCarId(vehicleId)
        .build();

      car = await carRepository.insertOne(carEntity);

      mockVulogClient.setVehicleIds([new VulogCarId(vehicleId)]);

      InvocationContext.clear();
    });

    it('should update', async () => {
      await handler(mockScheduledEvent(), {} as Context);

      const updatedCarState = await carStateRepository.findOne({
        where: { carId: car.id.value },
      });

      expect(updatedCarState).toBeDefined();
      expect(updatedCarState.locked).toBe(true);
      expect(updatedCarState.evBatteryLevel).toBe('80');
      expect(updatedCarState.allDoorsWindowsClosed).toBe(true);
      expect(updatedCarState.engineOn).toBe(false);
      expect(updatedCarState.cablePlugged).toBe(false);
      expect(updatedCarState.charging).toBe(false);

      const oldModifiedAt = new Date(Date.now() - 30 * 60 * 1000);
      expect(updatedCarState.modifiedAt.getTime()).toBeGreaterThan(
        oldModifiedAt.getTime(),
      );
    });
  });

  afterAll(async () => {
    await carStateRepository.delete({});
    await carRepository.delete({});
    await carModelRepository.delete({});
  });
});
