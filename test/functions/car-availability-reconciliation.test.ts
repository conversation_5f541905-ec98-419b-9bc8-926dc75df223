import { jest } from '@jest/globals';
import { Context } from 'aws-lambda';
import { DateTime } from 'luxon';
import { randomUUID } from 'node:crypto';
import { TokenPayload } from 'src/auth/authentication/token';
import { CarModel } from 'src/car-model/car-model.type';
import { CarModelRepository } from 'src/car-model/entities/car-model.repository';
import { Car, CarAvailability } from 'src/car/car.type';
import { CarRepository } from 'src/car/entities/car.repository';
import { CarAvailabilityInfo } from 'src/rental/car-rental.type';
import { getDataSource } from 'src/shared/datasources/datasource';
import { InvocationContext } from 'src/shared/invocation-context';
import { VulogCarId } from 'src/shared/shared.type';
import { CarModelBuilder } from 'test/car-model/car-model.builder';
import { CarBuilder } from 'test/car/car.builder';
import { mockScheduledEvent, purgeSqsQueue } from 'test/shared/test-util';

const mockGetCarAvailability = jest.fn();
jest.unstable_mockModule('src/shared/feature-flag.module', () => {
  const mockFeatureFlag = {
    getEnableConsumingVgEvents: jest.fn().mockResolvedValue(true as never),
    getEnableCsCarAvailabilityUpdatedEvent: jest
      .fn()
      .mockResolvedValue(true as never),
  };

  return {
    FeatureFlag: jest.fn().mockImplementation(() => mockFeatureFlag),
    FeatureFlagModule: {
      init: jest.fn().mockResolvedValue({
        featureFlag: mockFeatureFlag,
      } as never),
      module: mockFeatureFlag,
    },
  };
});
jest.unstable_mockModule('src/rental/car-rental-client', () => {
  return {
    CarRentalClient: jest.fn().mockImplementation(() => {
      return {
        getCarAvailability: mockGetCarAvailability,
      };
    }),
  };
});

jest.unstable_mockModule('src/station/station-client', () => {
  return {
    StationClient: jest.fn().mockImplementation(() => ({
      checkStationExists: jest.fn().mockResolvedValue(true as never),
    })),
  };
});

const { handler } = await import(
  'src/functions/car-availability-reconciliation'
);

describe('Car Reconciliation Function', () => {
  let carModel: CarModel;
  let carModelRepository: CarModelRepository;
  let carRepository: CarRepository;
  let car1: Car;
  let car2: Car;
  const dateNow = DateTime.now();

  const dateOneMinuteAgo = dateNow.minus({ minutes: 1 });
  const dateTwoMinutesAgo = dateNow.minus({ minutes: 2 });
  const dateHourAgo = dateNow.minus({ hours: 1 });

  beforeAll(async () => {
    initInvocationContext();
    carModelRepository = new CarModelRepository(await getDataSource());
    carRepository = new CarRepository(await getDataSource());
    carModel = await carModelRepository.createOrFail(
      new CarModelBuilder().build(),
    );
    car1 = await carRepository.insertOne(
      new CarBuilder()
        .withCarModelId(carModel.id)
        .withCarModel(carModel)
        .withAvailability(CarAvailability.AVAILABLE)
        .withAvailabilityUpdatedAt(dateHourAgo)
        .build(),
    );
    car2 = await carRepository.insertOne(
      new CarBuilder()
        .withCarModelId(carModel.id)
        .withCarModel(carModel)
        .withAvailability(CarAvailability.AVAILABLE)
        .withAvailabilityUpdatedAt(dateHourAgo)
        .build(),
    );
    InvocationContext.clear();
  });

  it('should successfully reconcile car availability to ON_RENTAL', async () => {
    const mockAvailabilities = [
      new CarAvailabilityInfo(
        randomUUID(),
        new VulogCarId(car1.externalCarId),
        CarAvailability.ON_RENTAL,
        dateOneMinuteAgo,
        dateOneMinuteAgo,
      ),
    ];

    mockGetCarAvailability.mockResolvedValue(mockAvailabilities as never);

    await handler(mockScheduledEvent(), {} as Context);

    const carResult1 = await carRepository.getOneByIdOrFail(car1.id);
    expect(carResult1.availability).toBe(CarAvailability.ON_RENTAL);
  });

  it('should ignore old availability updates and allow new ones', async () => {
    const mockAvailabilities = [
      new CarAvailabilityInfo(
        randomUUID(),
        new VulogCarId(car1.externalCarId),
        CarAvailability.AVAILABLE,
        dateTwoMinutesAgo,
        dateTwoMinutesAgo,
      ),
      new CarAvailabilityInfo(
        randomUUID(),
        new VulogCarId(car2.externalCarId),
        CarAvailability.RESERVED,
        dateNow,
        dateNow,
      ),
    ];

    mockGetCarAvailability.mockResolvedValue(mockAvailabilities as never);

    await handler(mockScheduledEvent(), {} as Context);

    const carResult1 = await carRepository.getOneByIdOrFail(car1.id);
    expect(carResult1.availability).toBe(CarAvailability.ON_RENTAL);

    const carResult2 = await carRepository.getOneByIdOrFail(car2.id);
    expect(carResult2.availability).toBe(CarAvailability.RESERVED);
  });

  afterAll(async () => {
    await carRepository.delete({});
    await carModelRepository.delete({});
    await purgeSqsQueue();
  });
});

function initInvocationContext() {
  InvocationContext.init('fake-request-id', 'fake-correlation-id');
  InvocationContext.setToken({
    payload: { userId: 'test-user-id' } as TokenPayload,
    signature: 'foobar',
  });
}
