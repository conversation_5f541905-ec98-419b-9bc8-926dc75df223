import { Context } from 'aws-lambda';
import { HealthResponse, handler } from 'src/functions/health';
import { StandardResponseBody } from 'src/shared/http-response';
import {
  CORRELATION_ID_HEADER,
  CORRELATION_ID_RESPONSE_KEY,
} from 'src/shared/middlewares/invocation-context.middleware';
import { mockApiGatewayEvent } from 'test/shared/test-util';

describe('Health Function', () => {
  it('should return no pending migrations', async () => {
    const event = mockApiGatewayEvent('GET', 'health');
    event.headers = undefined;

    const response = await handler(event, {} as Context);

    expect(response.statusCode).toBe(200);
    expect(response.headers).toHaveProperty(CORRELATION_ID_HEADER);

    const parsedBody: StandardResponseBody<HealthResponse> = JSON.parse(
      response.body,
    );
    expect(parsedBody.success).toBe(true);
    expect(parsedBody.result.status).toEqual('UP');
    expect(parsedBody.result.migrationResult).toEqual(
      'No pending migrations found.',
    );
    expect(parsedBody).toHaveProperty(CORRELATION_ID_RESPONSE_KEY);
  });
});
