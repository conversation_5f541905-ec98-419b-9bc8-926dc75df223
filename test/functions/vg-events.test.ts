import { jest } from '@jest/globals';
import * as falso from '@ngneat/falso';
import { DateTime } from 'luxon';
import { CarModel, CarModelName } from 'src/car-model/car-model.type';
import { CarModelRepository } from 'src/car-model/entities/car-model.repository';
import { CarStateRepository } from 'src/car-state/entities/car-state.repository';
import { Car, CarAvailability, CarId } from 'src/car/car.type';
import { CarRepository } from 'src/car/entities/car.repository';
import { getDataSource } from 'src/shared/datasources/datasource';
import { InvocationContext } from 'src/shared/invocation-context';
import { CarStateEntityBuilder } from 'test/car-state/entities/car-state.entity.builder';
import { CarBuilder } from 'test/car/car.builder';
import { MockVulogClient } from 'test/mocks/vulog-client.mock';
import {
  getResponse,
  mockApiGatewayEvent,
  mockEnv,
} from 'test/shared/test-util';

jest.unstable_mockModule('src/vulog/vulog-client', () => {
  return {
    VulogClient: jest.fn().mockImplementation(() => {
      return new MockVulogClient();
    }),
  };
});

jest.unstable_mockModule('src/shared/feature-flag.module', () => {
  const mockFeatureFlag = {
    getEnableConsumingVgEvents: jest.fn().mockResolvedValue(true as never),
    getEnableCsCarAvailabilityUpdatedEvent: jest
      .fn()
      .mockResolvedValue(true as never),
  };

  return {
    FeatureFlag: jest.fn().mockImplementation(() => mockFeatureFlag),
    FeatureFlagModule: {
      init: jest.fn().mockResolvedValue({
        featureFlag: mockFeatureFlag,
      } as never),
      module: mockFeatureFlag,
    },
  };
});

const { handler: vgEventHandler } = await import('src/functions/vg-events');

describe('VG Event Function', () => {
  let carModelRepository: CarModelRepository;
  let carRepository: CarRepository;
  let carStateRepository: CarStateRepository;
  let carModel: CarModel;
  let car: Car;
  let carId: string;

  beforeAll(async () => {
    InvocationContext.init('fake-request-id', 'fake-correlation-id');
    mockEnv();
    carRepository = new CarRepository(await getDataSource());
    carStateRepository = new CarStateRepository(await getDataSource());
    carModelRepository = new CarModelRepository(await getDataSource());

    carModel = await carModelRepository.createOrFail(
      new CarModel(null, new CarModelName('Test VG Model')),
    );

    car = await carRepository.insertOne(
      new CarBuilder()
        .withCarModelId(carModel.id)
        .withCarModel(carModel)
        .withExternalFleetId('externalFleetId1')
        .withExternalCarId('externalCarId1')
        .withAvailability(CarAvailability.AVAILABLE)
        .build(),
    );
    carId = car.id.value;

    const initialState = new CarStateEntityBuilder()
      .buildCarId(carId)
      .buildLocked(true)
      .buildEvBatteryLevel(80)
      .buildAllDoorsWindowsClosed(true)
      .buildProviderPayload(falso.randJSON())
      .build();

    await carStateRepository.insertOne(initialState.toCarState());
    InvocationContext.clear();
  });

  describe('Lock/Unlock Events', () => {
    it('should update car state when unlock event is received', async () => {
      const event = mockApiGatewayEvent(
        'POST',
        '/external/api/v1/vulog/vg-events',
        {
          type: 'VEHICLE_UNLOCK',
          fleetId: 'externalFleetId1',
          vehicleId: 'externalCarId1',
          insertionDate: DateTime.now().toISO(),
        },
      );

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { statusCode } = await getResponse(vgEventHandler, event);

      const updatedState = await carStateRepository.findByProps({
        carId: new CarId(carId),
      });

      expect(updatedState.result[0].locked).toBe(false);
    });

    it('should update car state when lock event is received', async () => {
      const event = mockApiGatewayEvent(
        'POST',
        '/external/api/v1/vulog/vg-events',
        {
          type: 'VEHICLE_LOCK',
          fleetId: 'externalFleetId1',
          vehicleId: 'externalCarId1',
          insertionDate: DateTime.now().toISO(),
        },
      );

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { statusCode } = await getResponse(vgEventHandler, event);

      const updatedState = await carStateRepository.findByProps({
        carId: new CarId(carId),
      });

      expect(updatedState.result[0].locked).toBe(true);
    });

    it('should ignore update car state when insertDate is in the pass', async () => {
      const event = mockApiGatewayEvent(
        'POST',
        '/external/api/v1/vulog/vg-events',
        {
          type: 'VEHICLE_UNLOCK',
          fleetId: 'externalFleetId1',
          vehicleId: 'externalCarId1',
          insertionDate: DateTime.now().minus({ seconds: 1 }).toISO(),
        },
      );

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { statusCode } = await getResponse(vgEventHandler, event);

      const updatedState = await carStateRepository.findByProps({
        carId: new CarId(carId),
      });

      expect(updatedState.result[0].locked).toBe(true);
    });
  });

  afterAll(async () => {
    await carStateRepository.delete({});
    await carRepository.delete({});
    await carModelRepository.delete({});
  });
});
