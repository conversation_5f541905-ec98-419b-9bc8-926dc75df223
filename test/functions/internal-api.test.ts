import { jest } from '@jest/globals';
import { HttpStatusCode } from 'axios';
import { CarModelService } from 'src/car-model/car-model.service';
import { CarModel } from 'src/car-model/car-model.type';
import { CarModelRepository } from 'src/car-model/entities/car-model.repository';
import { CarStateService } from 'src/car-state/car-state.service';
import { CarBatteryLevel } from 'src/car-state/car-state.type';
import { CarStateRepository } from 'src/car-state/entities/car-state.repository';
import { CarService } from 'src/car/car.service';
import { Car, CarAvailability, CarId } from 'src/car/car.type';
import { CarRepository } from 'src/car/entities/car.repository';
import { getDataSource } from 'src/shared/datasources/datasource';
import { InvocationContext } from 'src/shared/invocation-context';
import { CarModelBuilder } from 'test/car-model/car-model.builder';
import { CarStateBuilder } from 'test/car-state/car-state.builder';
import { CarBuilder } from 'test/car/car.builder';
import { aUuid } from 'test/shared/random-data';
import {
  TestApiResponse,
  getResponse,
  initInvocationContext,
  mockApiGatewayEvent,
  purgeSqsQueue,
} from 'test/shared/test-util';

jest.unstable_mockModule('src/shared/feature-flag.module', () => {
  const mockFeatureFlag = {
    getEnableConsumingVgEvents: jest.fn().mockResolvedValue(true as never),
    getEnableCsCarAvailabilityUpdatedEvent: jest
      .fn()
      .mockResolvedValue(true as never),
  };

  return {
    FeatureFlag: jest.fn().mockImplementation(() => mockFeatureFlag),
    FeatureFlagModule: {
      init: jest.fn().mockResolvedValue({
        featureFlag: mockFeatureFlag,
      } as never),
      module: mockFeatureFlag,
    },
  };
});

const { handler } = await import('src/functions/internal-api');

function carAtStation(
  model: CarModel,
  stationId: string,
  availability: CarAvailability,
  batteryLevel: number,
): Car {
  const carId = new CarId(aUuid());

  const builder = new CarBuilder()
    .withId(carId.value)
    .withCarModelId(model.id)
    .withAvailability(availability)
    .withStationId(stationId)
    .withCarState(
      new CarStateBuilder()
        .withCarId(carId)
        .withBatteryLevel(new CarBatteryLevel(batteryLevel))
        .build(),
    );

  return builder.build();
}

async function clearAll(): Promise<void> {
  const stateRepo = new CarStateRepository(await getDataSource());
  await stateRepo.delete({});
  const carRepository = new CarRepository(await getDataSource());
  await carRepository.delete({});
  const modelRepo = new CarModelRepository(await getDataSource());
  await modelRepo.delete({});
}

describe('Internal API', () => {
  let carService: CarService;
  let carStateService: CarStateService;
  let carModelService: CarModelService;

  let carModelRepository: CarModelRepository;
  let carRepository: CarRepository;
  let carStateRepository: CarStateRepository;

  let carModels: CarModel[];
  const station1 = aUuid();
  const station2 = aUuid();

  beforeAll(async () => {
    // Reset all static module instances to ensure mocks are used
    const { CarModule } = await import('src/car/car.module');
    const { CarModelModule } = await import('src/car-model/car-model.module');
    const { CarStateModule } = await import('src/car-state/car-state.module');
    const { CarApiModule } = await import('src/car/car.api.module');
    const { CarAvailabilityApiModule } = await import(
      'src/car-availability/car-availability.api.module'
    );
    const { FeatureFlagModule } = await import(
      'src/shared/feature-flag.module'
    );

    // Reset static instances to ensure mocks are used
    (CarModule as { module?: unknown }).module = undefined;
    (CarModelModule as { module?: unknown }).module = undefined;
    (CarStateModule as { module?: unknown }).module = undefined;
    (CarApiModule as { module?: unknown }).module = undefined;
    (CarAvailabilityApiModule as { module?: unknown }).module = undefined;
    (FeatureFlagModule as { module?: unknown }).module = undefined;

    carRepository = new CarRepository(await getDataSource());
    carStateRepository = new CarStateRepository(await getDataSource());
    carModelRepository = new CarModelRepository(await getDataSource());

    const carModelModule = await CarModelModule.init();
    carModelService = carModelModule.carModelService;

    const carModule = await CarModule.init();
    carService = carModule.carService;
    carStateService = (await CarStateModule.init()).carStateService;
  });

  beforeEach(async () => {
    initInvocationContext();
    carModels = [new CarModelBuilder().build(), new CarModelBuilder().build()];
    await Promise.all(carModels.map((cm) => carModelService.create(cm)));

    const cars = [
      carAtStation(carModels[0], station1, CarAvailability.ON_RENTAL, 20),
      carAtStation(carModels[0], station1, CarAvailability.AVAILABLE, 20),
      carAtStation(carModels[0], station1, CarAvailability.AVAILABLE, 20),
      carAtStation(carModels[0], station2, CarAvailability.AVAILABLE, 20),
      carAtStation(carModels[1], station2, CarAvailability.RESERVED, 20),
      carAtStation(carModels[1], station2, CarAvailability.AVAILABLE, 5), // low battery
    ];

    await Promise.all(cars.map((c) => carService.createCar(c)));
    await Promise.all(cars.map((c) => carStateService.upsertCarState(c.state)));
    InvocationContext.clear();
  });

  afterEach(async () => {
    await carRepository.delete({});
    await carModelRepository.delete({});
    await carStateRepository.delete({});
    await purgeSqsQueue();
  });

  describe('GET /internal/api/v1/availability-map', () => {
    function filters(batteryLevels: number[] = [0, 0]) {
      return {
        carModels: {
          [`${carModels[0].name.value}`]: {
            minBatteryLevel: batteryLevels[0],
          },
          [`${carModels[1].name.value}`]: {
            minBatteryLevel: batteryLevels[1],
          },
        },
      };
    }
    async function request(body: unknown) {
      return getResponse(
        handler,
        mockApiGatewayEvent('POST', '/internal/api/v1/availability-map', body),
      );
    }

    describe('when the response is successful', () => {
      let response: TestApiResponse;

      beforeEach(async () => {
        response = await request(filters([15, 10]));
      });

      afterEach(async () => {
        await clearAll();
      });

      it('should return a HTTP status OK', () => {
        expect(response.statusCode).toEqual(HttpStatusCode.Ok);
      });

      it('should return the correct response body', () => {
        expect(response.body).toStrictEqual({
          success: true,
          correlationId: response.body.correlationId,
          result: {
            [`${station1}`]: {
              id: station1,
              cars: {
                [`${carModels[0].name.value}`]: {
                  reserved: 0,
                  available: 2,
                },
                // [`${carModels[1].name.value}`]: { reserved: 0, available: 0 }, // empty result are not returned.
              },
            },
            [`${station2}`]: {
              id: station2,
              cars: {
                [`${carModels[0].name.value}`]: { reserved: 0, available: 1 },
                [`${carModels[1].name.value}`]: { reserved: 1, available: 0 },
              },
            },
          },
        });
      });
    });
  });

  describe('GET /internal/api/v1/cars', () => {
    it('should get all cars', async () => {
      const event = mockApiGatewayEvent('GET', '/internal/api/v1/cars');
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(200);
      expect(body.result.length).toBe(6);
      expect(body.result.every((car: Car) => !!car.carModel)).toBe(true);
    });

    it('should filter cars by availability and station with state', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        '/internal/api/v1/cars',
        null,
        null,
        {
          availability: CarAvailability.AVAILABLE,
          'station-id': station1,
          'include-state': 'true',
        },
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(200);
      expect(body.result.length).toBe(2);
      expect(
        body.result.every(
          (car: Car) => car.availability === CarAvailability.AVAILABLE,
        ),
      ).toBe(true);
      expect(body.result.every((car: Car) => car.stationId === station1)).toBe(
        true,
      );
      expect(body.result.every((car: Car) => !!car.state)).toBe(true);
    });
  });
});
