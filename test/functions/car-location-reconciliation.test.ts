import { EventName } from '@bluesg-2/event-library';
import { CarLocationUpdatedEvent } from '@bluesg-2/event-library/dist/car/car.event';
import { jest } from '@jest/globals';
import { Context } from 'aws-lambda';
import { DateTime } from 'luxon';
import { randomUUID } from 'node:crypto';
import { TokenPayload } from 'src/auth/authentication/token';
import { CarModelRepository } from 'src/car-model/entities/car-model.repository';
import { Car } from 'src/car/car.type';
import { CarRepository } from 'src/car/entities/car.repository';
import { CarLocation } from 'src/rental/car-rental.type';
import { getDataSource } from 'src/shared/datasources/datasource';
import { InvocationContext } from 'src/shared/invocation-context';
import { CarModelBuilder } from 'test/car-model/car-model.builder';
import { CarBuilder } from 'test/car/car.builder';
import {
  getSqsMessage,
  mockScheduledEvent,
  purgeSqsQueue,
} from 'test/shared/test-util';

const mockGetLastLocations = jest.fn();
jest.unstable_mockModule('src/shared/feature-flag.module', () => {
  const mockFeatureFlag = {
    getEnableConsumingVgEvents: jest.fn().mockResolvedValue(true as never),
    getEnableCsCarAvailabilityUpdatedEvent: jest
      .fn()
      .mockResolvedValue(true as never),
  };

  return {
    FeatureFlag: jest.fn().mockImplementation(() => mockFeatureFlag),
    FeatureFlagModule: {
      init: jest.fn().mockResolvedValue({
        featureFlag: mockFeatureFlag,
      } as never),
      module: mockFeatureFlag,
    },
  };
});
jest.unstable_mockModule('src/rental/car-rental-client', () => {
  return {
    CarRentalClient: jest.fn().mockImplementation(() => {
      return {
        getLastLocations: mockGetLastLocations,
      };
    }),
  };
});

jest.unstable_mockModule('src/station/station-client', () => {
  return {
    StationClient: jest.fn().mockImplementation(() => ({
      checkStationExists: jest.fn().mockResolvedValue(true as never),
    })),
  };
});

const { handler } = await import('src/functions/car-location-reconciliation');

describe('Car Location Reconciliation Function', () => {
  let carRepository: CarRepository;
  let carModelRepository: CarModelRepository;
  let car1: Car;
  let car2: Car;
  const dateNow = DateTime.now();

  const dateOneMinuteAgo = dateNow.minus({ minutes: 1 });
  const dateTwoMinutesAgo = dateNow.minus({ minutes: 2 });
  const dateHourAgo = dateNow.minus({ hours: 1 });

  beforeAll(async () => {
    await purgeSqsQueue();
    initInvocationContext();
    carRepository = new CarRepository(await getDataSource());
    carModelRepository = new CarModelRepository(await getDataSource());
    const carModel = await carModelRepository.createOrFail(
      new CarModelBuilder().build(),
    );
    car1 = await carRepository.insertOne(
      new CarBuilder()
        .withCarModelId(carModel.id)
        .withCarModel(carModel)
        .withLocatedAt(dateHourAgo)
        .build(),
    );
    car2 = await carRepository.insertOne(
      new CarBuilder()
        .withCarModelId(carModel.id)
        .withCarModel(carModel)
        .withLocatedAt(dateHourAgo)
        .build(),
    );
    InvocationContext.clear();
  });

  it('should successfully reconcile car locations', async () => {
    const stationId1 = randomUUID();
    const mockLocations = [
      new CarLocation(car1.externalCarId, stationId1, dateOneMinuteAgo),
    ];

    mockGetLastLocations.mockResolvedValue(mockLocations as never);

    await handler(mockScheduledEvent(), {} as Context);

    const carResult1 = await carRepository.getOneByIdOrFail(car1.id);
    expect(carResult1.stationId).toBe(stationId1);
    const sqsMessage: { Message: string; MessageAttributes: unknown } =
      await getSqsMessage();

    expect(sqsMessage.MessageAttributes).toEqual({
      eventType: {
        Type: 'String',
        Value: EventName.CarLocationUpdatedEvent,
      },
    });

    const message: CarLocationUpdatedEvent = JSON.parse(sqsMessage.Message);

    expect(message.event).toBe(EventName.CarLocationUpdatedEvent);
    expect(message.sentAt).toBeDefined();
    expect(message.payload.id).toBe(car1.id.value);
    expect(message.payload.stationId).toBe(stationId1);
    expect(message.payload.locatedAt).toBe(dateOneMinuteAgo.toISO());
  });

  it('should ignore old locations and allow new locations', async () => {
    const newStationId1 = randomUUID();
    const newStationId2 = randomUUID();
    const newStationId3 = randomUUID();
    const mockLocations = [
      new CarLocation(car1.externalCarId, newStationId1, dateTwoMinutesAgo),
      new CarLocation(car1.externalCarId, newStationId2, dateNow),
      new CarLocation(car2.externalCarId, newStationId3, dateNow),
    ];

    mockGetLastLocations.mockResolvedValue(mockLocations as never);

    await handler(mockScheduledEvent(), {} as Context);

    const carResult1 = await carRepository.getOneByIdOrFail(car1.id);
    expect(carResult1.stationId).toBe(newStationId2);
    const carResult2 = await carRepository.getOneByIdOrFail(car2.id);
    expect(carResult2.stationId).toBe(newStationId3);
  });

  afterAll(async () => {
    await purgeSqsQueue();
  });
});

function initInvocationContext() {
  InvocationContext.init('fake-request-id', 'fake-correlation-id');
  InvocationContext.setToken({
    payload: { userId: 'test-user-id' } as TokenPayload,
    signature: 'foobar',
  });
}
