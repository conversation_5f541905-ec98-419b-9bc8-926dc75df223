import {
  CarCreated<PERSON><PERSON>,
  CarDeletedEvent,
  EventName,
} from '@bluesg-2/event-library';
import { CarLocationUpdatedEvent } from '@bluesg-2/event-library/dist/car/car.event';
import { jest } from '@jest/globals';
import { randomUUID } from 'crypto';
import { DateTime } from 'luxon';
import { CarModel, CarModelId } from 'src/car-model/car-model.type';
import { CarModelRepository } from 'src/car-model/entities/car-model.repository';
import { CarStateRepository } from 'src/car-state/entities/car-state.repository';
import { Car, CarAvailability } from 'src/car/car.type';
import { CarRepository } from 'src/car/entities/car.repository';
import { getDataSource } from 'src/shared/datasources/datasource';
import { InvocationContext } from 'src/shared/invocation-context';
import {
  CORRELATION_ID_HEADER,
  CORRELATION_ID_RESPONSE_KEY,
} from 'src/shared/middlewares/invocation-context.middleware';
import { AuthModuleMock } from 'test/auth/helpers/auth.module.mock';
import { CarModelBuilder } from 'test/car-model/car-model.builder';
import { CarStateEntityBuilder } from 'test/car-state/entities/car-state.entity.builder';
import { MockVulogClient } from 'test/mocks/vulog-client.mock';
import {
  getResponse,
  getSqsMessage,
  initInvocationContext,
  mockApiGatewayEvent,
  purgeSqsQueue,
} from 'test/shared/test-util';

const mockStationClient = {
  checkStationExists: jest.fn().mockResolvedValue(false as never),
};

jest.unstable_mockModule('src/shared/feature-flag.module', () => {
  const mockFeatureFlag = {
    getEnableConsumingVgEvents: jest.fn().mockResolvedValue(true as never),
    getEnableCsCarAvailabilityUpdatedEvent: jest
      .fn()
      .mockResolvedValue(true as never),
  };

  return {
    FeatureFlag: jest.fn().mockImplementation(() => mockFeatureFlag),
    FeatureFlagModule: {
      init: jest.fn().mockResolvedValue({
        featureFlag: mockFeatureFlag,
      } as never),
      module: mockFeatureFlag,
    },
  };
});

jest.unstable_mockModule('src/vulog/vulog-client', () => {
  return {
    VulogClient: jest.fn().mockImplementation(() => {
      return new MockVulogClient();
    }),
  };
});
jest.unstable_mockModule('src/station/station-client', () => {
  return {
    StationClient: jest.fn().mockImplementation(() => mockStationClient),
  };
});

const { handler } = await import('src/functions/cars');

async function requestCarState(carId: string, maxAge?: string) {
  const queryParams = maxAge ? { 'max-age': maxAge } : {};
  return getResponse(
    handler,
    mockApiGatewayEvent(
      'GET',
      `/admin/api/v1/cars/${carId}/state`,
      null,
      null,
      queryParams,
    ),
  );
}

describe('Car Function', () => {
  let carModelRepository: CarModelRepository;
  let carRepository: CarRepository;
  let carStateRepository: CarStateRepository;
  let carModel: CarModel;

  beforeAll(async () => {
    carRepository = new CarRepository(await getDataSource());

    carStateRepository = new CarStateRepository(await getDataSource());

    carModelRepository = new CarModelRepository(await getDataSource());

    initInvocationContext();
    carModel = await carModelRepository.createOrFail(
      new CarModelBuilder().build(),
    );
    InvocationContext.clear();
  });

  beforeEach(async () => {
    await AuthModuleMock.mock();

    await purgeSqsQueue();
  });

  describe('POST /cars', () => {
    it('should create a car', async () => {
      const testCar = {
        carModelId: carModel.id.value,
        plateNumber: 'Test Plate Number',
        vin: 'Test Vin',
        registrationDate: '2025-03-09T06:07:18.734Z',
      };

      const event = mockApiGatewayEvent('POST', '/admin/api/v1/cars', testCar);
      const { headers, statusCode, body } = await getResponse(handler, event);
      console.log('Testing: ');
      console.log(await getResponse(handler, event));
      expect(statusCode).toBe(201);
      expect(headers).toHaveProperty(CORRELATION_ID_HEADER);
      expect(body).toHaveProperty(CORRELATION_ID_RESPONSE_KEY);
      expect(body).toHaveProperty('success');
      expect(body).toHaveProperty('result');
      expect(body.result).toHaveProperty('id');
      expect(body.result.carModelId).toBe(testCar.carModelId);
      expect(body.result.plateNumber).toBe(testCar.plateNumber);
      expect(body.result.vin).toBe(testCar.vin);
      expect(body.result).toHaveProperty('createdAt');
      expect(body.result).toHaveProperty('modifiedAt');
      expect(body.result.createdBy).toBe('123');
      expect(body.result.modifiedBy).toBe('123');

      const sqsMessage: { Message: string; MessageAttributes: unknown } =
        await getSqsMessage();

      expect(sqsMessage.MessageAttributes).toEqual({
        eventType: {
          Type: 'String',
          Value: EventName.CarCreatedEvent,
        },
      });

      const message: CarCreatedEvent = JSON.parse(sqsMessage.Message);

      expect(message.correlationId).toBe(body[CORRELATION_ID_RESPONSE_KEY]);
      expect(message.event).toBe(EventName.CarCreatedEvent);
      expect(message.sender).toBe('car-service-test');
      expect(message.sentAt).toBeDefined();
      expect(message.payload.id).toBe(body.result.id);
      expect(message.payload.plateNumber).toBe(body.result.plateNumber);
      expect(message.payload.vin).toBe(body.result.vin);
      expect(message.payload.registrationDate).toBe(
        body.result.registrationDate,
      );
    });

    it('should not create car with invalid car model id', async () => {
      const testCar = {
        carModelId: new CarModelId(randomUUID().toString()),
        plateNumber: 'Test Plate Number',
        vin: 'Test Vin',
        registrationDate: '2025-03-10T06:07:18.734Z',
      };

      const event = mockApiGatewayEvent('POST', '/admin/api/v1/cars', testCar);
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(404);
      expect(body.code).toBe('CAR_MODEL_NOT_FOUND');
      expect(body).toHaveProperty('message');
    });

    it('should not create car with malformed car model id', async () => {
      const testCar = {
        carModelId: new CarModelId('foobar'),
        plateNumber: 'Test Plate Number',
        vin: 'Test Vin',
        registrationDate: '2025-03-10T06:07:18.734Z',
      };

      const event = mockApiGatewayEvent('POST', '/admin/api/v1/cars', testCar);
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(400);
      expect(body.message).toBe(
        'Please provide correct UUID for carModelId field',
      );
    });

    it('should not create car with malformed registration date', async () => {
      const testCar = {
        carModelId: new CarModelId(randomUUID().toString()),
        plateNumber: 'Test Plate Number',
        vin: 'Test Vin',
        registrationDate: '2025-03-33T06:07:18.734Z',
      };

      const event = mockApiGatewayEvent('POST', '/admin/api/v1/cars', testCar);
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(400);
      expect(body.message).toBe(
        'Please provide correct registrationDate in ISO 8601 format (e.g. YYYY-MM-DDTHH:mm:ssZ / YYYY-MM-DDTHH:mm:ss / YYYY-MM-DD)',
      );
    });

    afterAll(async () => {
      await carRepository.delete({});
    });
  });

  describe('GET /cars', () => {
    let testCarId1: string;
    let testCarId2: string;

    beforeAll(async () => {
      let event = mockApiGatewayEvent('POST', '/admin/api/v1/cars', {
        carModelId: carModel.id.value,
        plateNumber: 'plate 1',
        vin: 'vin 1',
        registrationDate: '2025-03-09T06:07:18.734Z',
        externalFleetId: 'test-fleet-1',
        externalCarId: 'test-car-1',
      });

      const response1 = await getResponse(handler, event);
      testCarId1 = response1.body.result.id;

      // Create second test car
      event = mockApiGatewayEvent('POST', '/admin/api/v1/cars', {
        carModelId: carModel.id.value,
        plateNumber: 'plate 2',
        vin: 'vin 2',
        registrationDate: '2025-03-09T06:07:18.734Z',
        externalFleetId: 'test-fleet-2',
        externalCarId: 'test-car-2',
      });

      const response2 = await getResponse(handler, event);
      testCarId2 = response2.body.result.id;

      // Add car state data
      const fakeState1 = new CarStateEntityBuilder()
        .buildCarId(testCarId1)
        .buildLocked(true)
        .buildEvBatteryLevel(85)
        .buildAllDoorsWindowsClosed(true)
        .buildCablePlugged(true)
        .buildCharging(true)
        .buildProviderPayload({ data: 'test payload 1' })
        .build();

      // Insert car state for the second car
      const fakeState2 = new CarStateEntityBuilder()
        .buildCarId(testCarId2)
        .buildEvBatteryLevel(45)
        .buildCablePlugged(true)
        .buildCharging(true)
        .buildProviderPayload({ data: 'test payload 2' })
        .build();

      initInvocationContext();
      await carStateRepository.insertOne(fakeState1.toCarState());
      await carStateRepository.insertOne(fakeState2.toCarState());
      InvocationContext.clear();
    });

    it('should get all cars', async () => {
      const event = mockApiGatewayEvent('GET', '/admin/api/v1/cars');
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(200);
      expect(body.result.length).toBe(2);
      expect(body.result.every((car: Car) => !!car.carModel)).toBe(true);
    });

    it('should return bad request when filter cars by car malformed model id', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        '/admin/api/v1/cars',
        null,
        null,
        {
          'car-model': 'foobar',
        },
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(400);
      expect(body.message).toBe(
        'Please provide correct UUID for carModelId field',
      );
    });

    it('should filter cars by car model id', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        '/admin/api/v1/cars',
        null,
        null,
        {
          'car-model': carModel.id.value,
        },
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(200);
      expect(body.result.length).toBe(2);
    });

    it('should filter cars by car plate number', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        '/admin/api/v1/cars',
        null,
        null,
        {
          'plate-number': 'plate 1',
        },
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(200);
      expect(body.result.length).toBe(1);
    });

    it('should filter cars by car vin', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        '/admin/api/v1/cars',
        null,
        null,
        {
          vin: 'vin 2',
        },
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(200);
      expect(body.result.length).toBe(1);
    });

    it('should filter cars by availability OUT_OF_SERVICE', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        '/admin/api/v1/cars',
        null,
        null,
        {
          availability: CarAvailability.OUT_OF_SERVICE,
        },
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(200);
      expect(body.result.length).toBe(2);
    });

    it('should filter cars by availability ON_RENTAL', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        '/admin/api/v1/cars',
        null,
        null,
        {
          availability: CarAvailability.ON_RENTAL,
        },
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(200);
      expect(body.result.length).toBe(0);
    });

    it('should reject invalid availability filter value', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        '/admin/api/v1/cars',
        null,
        null,
        {
          availability: 'foobar',
        },
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(400);
      expect(body.message).toBe(
        "Expected: 'AVAILABLE, OUT_OF_SERVICE, RESERVED, ON_RENTAL' for field 'availability', but got: 'foobar'",
      );
    });

    it('should filter cars by station id', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        '/admin/api/v1/cars',
        null,
        null,
        {
          'station-id': randomUUID().toString(),
        },
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(200);
      expect(body.result.length).toBe(0);
    });

    it('should reject malformed station id', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        '/admin/api/v1/cars',
        null,
        null,
        {
          'station-id': 'foobar',
        },
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(400);
      expect(body.message).toBe(
        'Please provide correct UUID for stationId field',
      );
    });

    it('should reject invalid filter', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        '/admin/api/v1/cars',
        null,
        null,
        {
          carModel: 'foobar',
        },
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(400);
      expect(body.message).toBe("Unrecognized fields: 'carModel'");
    });

    it('should get cars without state information by default', async () => {
      const event = mockApiGatewayEvent('GET', '/admin/api/v1/cars');
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(200);
      expect(body.result.length).toBe(2);
      // Check that state is not included in the response
      expect(body.result[0].state).toBeUndefined();
      expect(body.result[1].state).toBeUndefined();
    });

    it('should get cars without state when include-state=false', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        '/admin/api/v1/cars',
        null,
        null,
        {
          'include-state': 'false',
        },
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(200);
      expect(body.result.length).toBe(2);
      // Check that state is not included in the response
      expect(body.result[0].state).toBeUndefined();
      expect(body.result[1].state).toBeUndefined();
    });

    it('should get cars with state when include-state=true', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        '/admin/api/v1/cars',
        null,
        null,
        {
          'include-state': 'true',
        },
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(200);
      expect(body.result.length).toBe(2);

      // Verify state information is included
      expect(body.result[0].state).toBeDefined();
      expect(body.result[1].state).toBeDefined();

      // Find car 1 and car 2 in the results
      interface CarWithState {
        id: string;
        state: {
          locked: boolean;
          batteryLevel: { value: number };
          allDoorsWindowsClosed: boolean;
          engineOn: boolean;
          cablePlugged: boolean;
          charging: boolean;
        };
      }

      const car1 = body.result.find((c: CarWithState) => c.id === testCarId1);
      const car2 = body.result.find((c: CarWithState) => c.id === testCarId2);

      // Verify car 1 state
      expect(car1.state).toBeDefined();
      expect(car1.state.locked).toBe(true);
      expect(car1.state.batteryLevel).toBe(85);
      expect(car1.state.allDoorsWindowsClosed).toBe(true);
      expect(car1.state.engineOn).toBe(false);
      expect(car1.state.cablePlugged).toBe(true);
      expect(car1.state.charging).toBe(true);

      // Verify car 2 state
      expect(car2.state).toBeDefined();
      expect(car2.state.locked).toBe(false);
      expect(car2.state.batteryLevel).toBe(45);
      expect(car2.state.allDoorsWindowsClosed).toBe(false);
      expect(car2.state.engineOn).toBe(false);
      expect(car2.state.cablePlugged).toBe(true);
      expect(car2.state.charging).toBe(true);
    });

    afterAll(async () => {
      // Clean up the car state data
      await carStateRepository.delete({});

      // Clean up the car data
      await carRepository.delete({});
    });
  });

  describe('DELETE /cars', () => {
    it('should delete car', async () => {
      const postEvent = mockApiGatewayEvent('POST', '/admin/api/v1/cars', {
        carModelId: carModel.id.value,
        plateNumber: 'delete plate',
        vin: 'delete vin',
        registrationDate: '2025-03-09T06:07:18.734Z',
      });

      const postResponse = await getResponse(handler, postEvent);

      await getSqsMessage();

      const carIdToDelete = postResponse.body.result.id;

      const deleteEvent = mockApiGatewayEvent(
        'DELETE',
        `/admin/api/v1/cars/${carIdToDelete}`,
      );

      const { headers, statusCode, body } = await getResponse(
        handler,
        deleteEvent,
      );

      expect(statusCode).toBe(200);
      expect(headers).toHaveProperty(CORRELATION_ID_HEADER);
      expect(body.message).toBe('The car has been deleted.');
      expect(body.correlationId).toBe(headers[CORRELATION_ID_HEADER]);

      const sqsMessage: { Message: string; MessageAttributes: unknown } =
        await getSqsMessage();

      expect(sqsMessage.MessageAttributes).toEqual({
        eventType: {
          Type: 'String',
          Value: EventName.CarDeletedEvent,
        },
      });

      const message: CarDeletedEvent = JSON.parse(sqsMessage.Message);

      expect(message.correlationId).toBe(body[CORRELATION_ID_RESPONSE_KEY]);
      expect(message.event).toBe(EventName.CarDeletedEvent);
      expect(message.sender).toBe('car-service-test');
      expect(message.sentAt).toBeDefined();
      expect(message.payload.id).toBe(carIdToDelete);
    });

    it('should return 400 when delete malformed id', async () => {
      const event = mockApiGatewayEvent('DELETE', '/admin/api/v1/cars/foobar');

      const { headers, statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(400);
      expect(headers).toHaveProperty(CORRELATION_ID_HEADER);
      expect(body.message).toBe('Please provide correct UUID for Car');
      expect(body.correlationId).toBe(headers[CORRELATION_ID_HEADER]);
    });

    it('should return 404 when delete car not found', async () => {
      const notFoundId = randomUUID().toString();

      const event = mockApiGatewayEvent(
        'DELETE',
        `/admin/api/v1/cars/${notFoundId}`,
      );

      const { headers, statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(404);
      expect(headers).toHaveProperty(CORRELATION_ID_HEADER);
      expect(body.code).toBe('CAR_NOT_FOUND');
      expect(body.message).toBe(`The car ${notFoundId} could not be found`);
      expect(body.correlationId).toBe(headers[CORRELATION_ID_HEADER]);
    });
  });

  describe('GET /cars/{carId}', () => {
    it('should get car', async () => {
      const postEvent = mockApiGatewayEvent('POST', '/admin/api/v1/cars', {
        carModelId: carModel.id.value,
        plateNumber: 'car plate',
        vin: 'car vin',
        registrationDate: '2025-03-09T06:07:18.734Z',
      });

      const postResponse = await getResponse(handler, postEvent);

      const carId = postResponse.body.result.id;

      const getEvent = mockApiGatewayEvent(
        'GET',
        `/admin/api/v1/cars/${carId}`,
      );

      const { headers, statusCode, body } = await getResponse(
        handler,
        getEvent,
      );

      expect(statusCode).toBe(200);
      expect(headers).toHaveProperty(CORRELATION_ID_HEADER);
      expect(body.correlationId).toBe(headers[CORRELATION_ID_HEADER]);
      expect(body.result.id).toBe(carId);
      expect(body.result.carModel.id).toBe(carModel.id.value);
    });

    it('should return 400 when get malformed id', async () => {
      const event = mockApiGatewayEvent('GET', '/admin/api/v1/cars/foobar');

      const { headers, statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(400);
      expect(headers).toHaveProperty(CORRELATION_ID_HEADER);
      expect(body.message).toBe('Please provide correct UUID for Car');
      expect(body.correlationId).toBe(headers[CORRELATION_ID_HEADER]);
    });

    it('should return 404 when get car not found', async () => {
      const notFoundId = randomUUID().toString();

      const event = mockApiGatewayEvent(
        'GET',
        `/admin/api/v1/cars/${notFoundId}`,
      );

      const { headers, statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(404);
      expect(headers).toHaveProperty(CORRELATION_ID_HEADER);
      expect(body.code).toBe('CAR_NOT_FOUND');
      expect(body.message).toBe(`The car ${notFoundId} could not be found`);
      expect(body.correlationId).toBe(headers[CORRELATION_ID_HEADER]);
    });
  });

  describe('GET /cars/:carId/state', () => {
    let carId: string;

    beforeAll(async () => {
      const testCar = {
        carModelId: carModel.id.value,
        plateNumber: 'PLATE-123',
        vin: 'VIN12345678',
        registrationDate: '2025-03-09T06:07:18.734Z',
        externalFleetId: 'BLUESG-SINGA',
        externalCarId: '123f122b-6bc2-4be2-8f67-6b2ad3777271',
      };

      const postEvent = mockApiGatewayEvent(
        'POST',
        '/admin/api/v1/cars',
        testCar,
      );
      const response = await getResponse(handler, postEvent);

      carId = response.body.result.id;
    });

    it('should get car state from Vulog API and return the existing car state when it is not stale', async () => {
      let response = await requestCarState(carId);

      expect(response.statusCode).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.result).toBeDefined();

      const carState = response.body.result;
      expect(carState.carId).toBe(carId);
      expect(typeof carState.batteryLevel).toBe('number');
      expect(typeof carState.locked).toBe('boolean');
      expect(typeof carState.allDoorsWindowsClosed).toBe('boolean');
      expect(carState.doors).toBeDefined();
      expect(carState.windows).toBeDefined();
      expect(typeof carState.engineOn).toBe('boolean');
      expect(typeof carState.cablePlugged).toBe('boolean');
      expect(typeof carState.charging).toBe('boolean');
      expect(Array.isArray(carState.stations)).toBe(true);

      expect(carState.stations).toContain('test-zone-id-1');
      expect(carState.stations).toContain('test-zone-id-2');

      expect(carState.modificationDate).toBeDefined();

      // Second request should return cached state
      response = await requestCarState(carId);

      expect(response.statusCode).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.result).toBeDefined();

      expect(response.body.result.modificationDate).toBe(
        carState.modificationDate,
      );
    });

    it('should return the updated car state when it is stale', async () => {
      const response = await requestCarState(carId, '1');

      expect(response.statusCode).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.result).toBeDefined();

      const firstModifiedAt = new Date(response.body.result.modificationDate);

      await new Promise((resolve) => setTimeout(resolve, 1500));

      const secondResponse = await requestCarState(carId, '1');
      const secondModifiedAt = new Date(
        secondResponse.body.result.modificationDate,
      );

      expect(secondModifiedAt.getTime()).toBeGreaterThan(
        firstModifiedAt.getTime(),
      );
    });

    it('should return 404 when car is not found', async () => {
      const notFoundId = randomUUID().toString();
      const { statusCode, body } = await requestCarState(notFoundId);

      expect(statusCode).toBe(404);
      expect(body.code).toBe('CAR_NOT_FOUND');
    });

    it('should return 400 when car ID is malformed', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        '/admin/api/v1/cars/invalid-uuid/state',
      );
      const { statusCode, body } = await getResponse(handler, event);

      expect(statusCode).toBe(400);
      expect(body.message).toContain('UUID');
    });

    it('should get car state with custom max-age parameter', async () => {
      const response = await requestCarState(carId, '10');

      expect(response.statusCode).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.result).toBeDefined();

      const carState = response.body.result;
      expect(carState.carId).toBe(carId);
      expect(typeof carState.batteryLevel).toBe('number');
    });

    it('should reject invalid max-age parameter', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        `/admin/api/v1/cars/${carId}/state`,
        null,
        null,
        {
          'max-age': 'invalid',
        },
      );
      const response = await getResponse(handler, event);

      expect(response.statusCode).toBe(400);
    });

    it('should reject when using incorrect parameter name', async () => {
      const event = mockApiGatewayEvent(
        'GET',
        `/admin/api/v1/cars/${carId}/state`,
        null,
        null,
        {
          max_age: '10',
        },
      );
      const response = await getResponse(handler, event);

      expect(response.statusCode).toBe(400);
    });

    afterAll(async () => {
      if (carId) {
        const deleteEvent = mockApiGatewayEvent(
          'DELETE',
          `/admin/api/v1/cars/${carId}`,
        );
        await getResponse(handler, deleteEvent);
        await getSqsMessage();
      }
    });
  });

  describe('PUT /cars/:carId', () => {
    let carId: string;
    const stationId = randomUUID();

    beforeAll(async () => {
      const testCar = {
        carModelId: carModel.id.value,
        plateNumber: 'PLATE-123',
        vin: 'VIN12345678',
        registrationDate: '2025-03-09T06:07:18.734Z',
        externalFleetId: 'BLUESG-SINGA',
        externalCarId: '123f122b-6bc2-4be2-8f67-6b2ad3777272',
      };

      const postEvent = mockApiGatewayEvent(
        'POST',
        '/admin/api/v1/cars',
        testCar,
      );
      const response = await getResponse(handler, postEvent);

      expect(response.statusCode).toBe(201);
      expect(response.body.result.availability).toBe(
        CarAvailability.OUT_OF_SERVICE,
      );
      carId = response.body.result.id;
    });

    it('should return 400 when availability is invalid', async () => {
      const event = mockApiGatewayEvent('PUT', `/admin/api/v1/cars/${carId}`, {
        availability: 'foobar',
        stationId: randomUUID(),
        asOf: DateTime.now().minus({ seconds: 10 }).toISO(),
      });

      const response = await getResponse(handler, event);

      expect(response.statusCode).toBe(400);
      expect(response.body.message).toBe(
        "Expected: 'AVAILABLE, OUT_OF_SERVICE, RESERVED, ON_RENTAL' for field 'availability', but got: 'foobar'",
      );
    });

    it('should return 400 when asOf is in the future', async () => {
      const event = mockApiGatewayEvent('PUT', `/admin/api/v1/cars/${carId}`, {
        availability: CarAvailability.ON_RENTAL,
        stationId: randomUUID(),
        asOf: DateTime.now().plus({ hours: 1 }).toISO(),
      });

      const response = await getResponse(handler, event);

      expect(response.statusCode).toBe(400);
      expect(response.body.code).toBe('INVALID_AS_OF_TIME_IN_FUTURE');
    });

    it('should return 400 when stationId is malformed', async () => {
      const event = mockApiGatewayEvent('PUT', `/admin/api/v1/cars/${carId}`, {
        availability: CarAvailability.ON_RENTAL,
        stationId: 'foobar',
        asOf: DateTime.now().minus({ seconds: 10 }).toISO(),
      });

      const response = await getResponse(handler, event);

      expect(response.statusCode).toBe(400);
      expect(response.body.message).toBe(
        'Please provide correct UUID for stationId field',
      );
    });

    it('should return station not found when station does not exist', async () => {
      const event = mockApiGatewayEvent(
        'PUT',
        `/admin/api/v1/cars/${randomUUID()}`,
        {
          availability: CarAvailability.ON_RENTAL,
          stationId: randomUUID(),
          asOf: DateTime.now().minus({ seconds: 10 }).toISO(),
        },
      );

      const response = await getResponse(handler, event);

      expect(response.statusCode).toBe(404);
      expect(response.body.code).toBe('STATION_NOT_FOUND');
    });

    it('Should failed to transition from OUT_OF_SERVICE to ON_RENTAL', async () => {
      mockStationClient.checkStationExists.mockResolvedValueOnce(true as never);
      const event = mockApiGatewayEvent('PUT', `/admin/api/v1/cars/${carId}`, {
        availability: CarAvailability.ON_RENTAL,
        stationId: stationId,
        asOf: DateTime.now().minus({ seconds: 10 }).toISO(),
      });

      const response = await getResponse(handler, event);

      expect(response.statusCode).toBe(409);
      expect(response.body.code).toBe('CAR_AVAILABILITY_TRANSITION_FORBIDDEN');
    });

    it('Should update successfully', async () => {
      mockStationClient.checkStationExists.mockResolvedValueOnce(true as never);
      const event = mockApiGatewayEvent('PUT', `/admin/api/v1/cars/${carId}`, {
        availability: CarAvailability.AVAILABLE,
        stationId: stationId,
        asOf: DateTime.now().minus({ seconds: 10 }).toISO(),
      });

      const response = await getResponse(handler, event);

      expect(response.statusCode).toBe(200);
      expect(response.body.success).toBe(true);

      const sqsMessage: { Message: string; MessageAttributes: unknown } =
        await getSqsMessage();

      expect(sqsMessage.MessageAttributes).toEqual({
        eventType: {
          Type: 'String',
          Value: EventName.CarLocationUpdatedEvent,
        },
      });

      const message: CarLocationUpdatedEvent = JSON.parse(sqsMessage.Message);

      expect(message.event).toBe(EventName.CarLocationUpdatedEvent);
      expect(message.payload.id).toBe(carId);
      expect(message.payload.stationId).toBe(stationId);
    });

    it('get car by id will return the car with the updated availability and location', async () => {
      const response = await getResponse(
        handler,
        mockApiGatewayEvent('GET', `/admin/api/v1/cars/${carId}`),
      );
      expect(response.statusCode).toBe(200);
      expect(response.body.result.availability).toBe(CarAvailability.AVAILABLE);
      expect(response.body.result.stationId).toBe(stationId);
    });

    it('get list of cars will return the car with the updated availability and location', async () => {
      const response = await getResponse(
        handler,
        mockApiGatewayEvent('GET', `/admin/api/v1/cars`),
      );
      expect(response.statusCode).toBe(200);
      const cars = response.body.result;
      const car = cars.find((car: { id: string }) => car.id === carId);
      expect(car?.availability).toBe(CarAvailability.AVAILABLE);
      expect(car?.stationId).toBe(stationId);
    });

    it('should return 409 when asOf is old', async () => {
      mockStationClient.checkStationExists.mockResolvedValueOnce(true as never);

      const event = mockApiGatewayEvent('PUT', `/admin/api/v1/cars/${carId}`, {
        availability: CarAvailability.AVAILABLE,
        stationId: randomUUID(),
        asOf: DateTime.now().minus({ minutes: 10 }).toISO(),
      });

      const response = await getResponse(handler, event);

      expect(response.statusCode).toBe(409);
      expect(response.body.code).toBe('CAR_UPDATE_REQUEST_OUTDATED');
    });

    it('should return 404 when car not found', async () => {
      mockStationClient.checkStationExists.mockResolvedValueOnce(true as never);

      const event = mockApiGatewayEvent(
        'PUT',
        `/admin/api/v1/cars/${randomUUID()}`,
        {
          availability: CarAvailability.ON_RENTAL,
          stationId: randomUUID(),
          asOf: DateTime.now().minus({ seconds: 10 }).toISO(),
        },
      );

      const response = await getResponse(handler, event);

      expect(response.statusCode).toBe(404);
      expect(response.body.code).toBe('CAR_NOT_FOUND');
    });
  });

  afterAll(async () => {
    await carRepository.delete({});
    await carModelRepository.delete({});
    await carStateRepository.delete({});
    await purgeSqsQueue();
  });
});
