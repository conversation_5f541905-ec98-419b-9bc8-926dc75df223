import { jest } from '@jest/globals';
import { Context } from 'aws-lambda';
import { randomUUID } from 'crypto';
import { DateTime } from 'luxon';
import { CarModel, CarModelName } from 'src/car-model/car-model.type';
import { CarModelRepository } from 'src/car-model/entities/car-model.repository';
import { Car, CarAvailability } from 'src/car/car.type';
import { CarRepository } from 'src/car/entities/car.repository';
import { getDataSource } from 'src/shared/datasources/datasource';
import { InvocationContext } from 'src/shared/invocation-context';
import { VulogCarId } from 'src/shared/shared.type';
import { CarBuilder } from 'test/car/car.builder';
import {
  createAimaCarEnabledEvent,
  createAimaCarPutOutOfServiceEvent,
  createRentalEndedEvent,
  createRentalReservationCancelledEvent,
  createRentalReservationCreatedEvent,
  createRentalReservationExpiredEvent,
  createRentalReservationSwitchedEvent,
  createRentalStartedEvent,
  createSQSMessage,
} from 'test/shared/sqs-event';
import {
  initInvocationContext,
  mockEnv,
  mockSQSEvent,
  purgeSqsQueue,
} from 'test/shared/test-util';

jest.unstable_mockModule('src/shared/feature-flag.module', () => {
  const mockFeatureFlag = {
    getEnableConsumingVgEvents: jest.fn().mockResolvedValue(true as never),
    getEnableCsCarAvailabilityUpdatedEvent: jest
      .fn()
      .mockResolvedValue(true as never),
  };

  return {
    FeatureFlag: jest.fn().mockImplementation(() => mockFeatureFlag),
    FeatureFlagModule: {
      init: jest.fn().mockResolvedValue({
        featureFlag: mockFeatureFlag,
      } as never),
      module: mockFeatureFlag,
    },
  };
});

const { handler } = await import('src/functions/bluesg-events');

jest.setTimeout(100000);

describe('Bluesg Events Function', () => {
  let carModelRepository: CarModelRepository;
  let carRepository: CarRepository;
  let carModel: CarModel;
  let car: Car;
  const externalFleetId = randomUUID().toString();
  const externalCarId = randomUUID().toString();
  const now = DateTime.now();

  beforeAll(async () => {
    mockEnv();

    carRepository = new CarRepository(await getDataSource());

    carModelRepository = new CarModelRepository(await getDataSource());

    initInvocationContext();

    carModel = await carModelRepository.createOrFail(
      new CarModel(null, new CarModelName('rental-ended-car-model')),
    );

    car = await carRepository.insertOne(
      new CarBuilder()
        .withCarModelId(carModel.id)
        .withCarModel(carModel)
        .withExternalFleetId(externalFleetId)
        .withExternalCarId(externalCarId)
        .withAvailability(CarAvailability.ON_RENTAL)
        .withLocatedAt(now.minus({ hours: 1 }))
        .build(),
    );

    InvocationContext.clear();
  });

  afterAll(async () => {
    await carRepository.delete({});
    await carModelRepository.delete({});
    await purgeSqsQueue();
  });

  it('should process event without error when missing end station id', async () => {
    const event = mockSQSEvent(
      createSQSMessage(
        createRentalEndedEvent({
          carId: car.id,
          rentalEndedAt: now,
        }),
      ),
    );

    await handler(event, {} as Context);
  });

  describe('Rental ended event', () => {
    it('should update location for car', async () => {
      const eventEndStationId = randomUUID().toString();

      const event = mockSQSEvent(
        createSQSMessage(
          createRentalEndedEvent({
            carId: new VulogCarId(externalCarId),
            rentalEndedAt: now,
            endStationId: eventEndStationId,
          }),
        ),
      );

      await handler(event, {} as Context);

      const updatedCar = await carRepository.getOneByIdOrFail(car.id);

      expect(updatedCar.stationId).toEqual(eventEndStationId);
      expect(updatedCar.locatedAt.toMillis()).toBeCloseTo(now.toMillis(), -3);
      expect(updatedCar.availability).toEqual(CarAvailability.AVAILABLE);
    });

    it('should ignore event with old rental ended at timestamp', async () => {
      const oldCar = await carRepository.getOneByIdOrFail(car.id);

      const eventEndStationId = randomUUID().toString();

      const event = mockSQSEvent(
        createSQSMessage(
          createRentalEndedEvent({
            carId: new VulogCarId(externalCarId),
            rentalEndedAt: now.minus({ minutes: 2 }),
            endStationId: eventEndStationId,
            correlationId: randomUUID().toString(),
          }),
        ),
      );

      await handler(event, {} as Context);

      const updatedCar = await carRepository.getOneByIdOrFail(car.id);

      expect(updatedCar.stationId).toEqual(oldCar.stationId);
      expect(updatedCar.locatedAt.toMillis()).toBeCloseTo(
        oldCar.locatedAt.toMillis(),
        -3,
      );
      expect(updatedCar.availability).toEqual(CarAvailability.AVAILABLE);
    });
  });

  describe('Rental start event', () => {
    const externalFleetId = randomUUID().toString();
    const externalCarId = randomUUID().toString();

    beforeAll(async () => {
      initInvocationContext();

      carModel = await carModelRepository.createOrFail(
        new CarModel(null, new CarModelName('rental-started-car-model')),
      );

      car = await carRepository.insertOne(
        new CarBuilder()
          .withCarModelId(carModel.id)
          .withCarModel(carModel)
          .withExternalFleetId(externalFleetId)
          .withExternalCarId(externalCarId)
          .withAvailability(CarAvailability.AVAILABLE)
          .withLocatedAt(now.minus({ hours: 1 }))
          .build(),
      );

      InvocationContext.clear();
    });

    it('should update location for car when rental started', async () => {
      const eventEndStationId = randomUUID().toString();

      const event = mockSQSEvent(
        createSQSMessage(
          createRentalStartedEvent({
            carId: new VulogCarId(externalCarId),
            rentalStartedAt: now,
            startStationId: eventEndStationId,
            correlationId: randomUUID().toString(),
          }),
        ),
      );

      await handler(event, {} as Context);

      const updatedCar = await carRepository.getOneByIdOrFail(car.id);

      expect(updatedCar.stationId).toEqual(eventEndStationId);
      expect(updatedCar.locatedAt.toMillis()).toBeCloseTo(now.toMillis(), -3);
      expect(updatedCar.availability).toEqual(CarAvailability.ON_RENTAL);
    });

    it('should ignore event with old rental ended at timestamp', async () => {
      const oldCar = await carRepository.getOneByIdOrFail(car.id);

      const eventEndStationId = randomUUID().toString();

      const event = mockSQSEvent(
        createSQSMessage(
          createRentalStartedEvent({
            carId: new VulogCarId(externalCarId),
            rentalStartedAt: now.minus({ minutes: 2 }),
            startStationId: eventEndStationId,
            correlationId: randomUUID().toString(),
          }),
        ),
      );

      await handler(event, {} as Context);

      const updatedCar = await carRepository.getOneByIdOrFail(car.id);

      expect(updatedCar.stationId).toEqual(oldCar.stationId);
      expect(updatedCar.locatedAt.toMillis()).toBeCloseTo(
        oldCar.locatedAt.toMillis(),
        -3,
      );
    });
  });

  describe('Rental reservation created event', () => {
    const externalFleetId = randomUUID().toString();
    const externalCarId = randomUUID().toString();
    const now = DateTime.now();

    beforeAll(async () => {
      initInvocationContext();

      carModel = await carModelRepository.createOrFail(
        new CarModel(
          null,
          new CarModelName('rental-reservation-created-car-model'),
        ),
      );

      car = await carRepository.insertOne(
        new CarBuilder()
          .withCarModelId(carModel.id)
          .withCarModel(carModel)
          .withExternalFleetId(externalFleetId)
          .withExternalCarId(externalCarId)
          .withAvailability(CarAvailability.AVAILABLE)
          .build(),
      );

      InvocationContext.clear();
    });

    it('should update availability for car when rental reservation created', async () => {
      const oldCar = await carRepository.getOneByIdOrFail(car.id);

      expect(oldCar.availability).toEqual(CarAvailability.AVAILABLE);

      const event = mockSQSEvent(
        createSQSMessage(
          createRentalReservationCreatedEvent({
            carId: new VulogCarId(externalCarId),
            reservationStartedAt: now,
            reservedAt: now,
            correlationId: randomUUID().toString(),
          }),
        ),
      );

      await handler(event, {} as Context);

      const updatedCar = await carRepository.getOneByIdOrFail(car.id);

      expect(updatedCar.availabilityUpdatedAt).toEqual(now);
      expect(updatedCar.availability).toEqual(CarAvailability.RESERVED);
    });

    it('should ignore event with old reservation started at timestamp', async () => {
      const event = mockSQSEvent(
        createSQSMessage(
          createRentalReservationCreatedEvent({
            carId: new VulogCarId(externalCarId),
            reservationStartedAt: now.minus({ minutes: 2 }),
            reservedAt: now.minus({ minutes: 2 }),
            correlationId: randomUUID().toString(),
          }),
        ),
      );

      await handler(event, {} as Context);

      const updatedCar = await carRepository.getOneByIdOrFail(car.id);

      expect(updatedCar.availabilityUpdatedAt).toEqual(now);
      expect(updatedCar.availability).toEqual(CarAvailability.RESERVED);
    });
  });

  describe('Rental reservation cancelled event', () => {
    const externalFleetId = randomUUID().toString();
    const externalCarId = randomUUID().toString();
    const now = DateTime.now();

    beforeAll(async () => {
      initInvocationContext();

      carModel = await carModelRepository.createOrFail(
        new CarModel(
          null,
          new CarModelName('rental-reservation-cancelled-car-model'),
        ),
      );

      car = await carRepository.insertOne(
        new CarBuilder()
          .withCarModelId(carModel.id)
          .withCarModel(carModel)
          .withExternalFleetId(externalFleetId)
          .withExternalCarId(externalCarId)
          .withAvailability(CarAvailability.AVAILABLE)
          .build(),
      );

      InvocationContext.clear();
    });

    it('should update availability for car when rental reservation cancelled', async () => {
      const oldCar = await carRepository.getOneByIdOrFail(car.id);

      expect(oldCar.availability).toEqual(CarAvailability.AVAILABLE);

      const event = mockSQSEvent(
        createSQSMessage(
          createRentalReservationCancelledEvent({
            carId: new VulogCarId(externalCarId),
            reservationCancelledAt: now,
            correlationId: randomUUID().toString(),
          }),
        ),
      );

      await handler(event, {} as Context);

      const updatedCar = await carRepository.getOneByIdOrFail(car.id);

      expect(updatedCar.availabilityUpdatedAt).toEqual(now);
      expect(updatedCar.availability).toEqual(CarAvailability.AVAILABLE);
    });

    it('should ignore event with old reservation cancelled at timestamp', async () => {
      const event = mockSQSEvent(
        createSQSMessage(
          createRentalReservationCancelledEvent({
            carId: new VulogCarId(externalCarId),
            reservationCancelledAt: now.minus({ minutes: 2 }),
            correlationId: randomUUID().toString(),
          }),
        ),
      );

      await handler(event, {} as Context);

      const updatedCar = await carRepository.getOneByIdOrFail(car.id);

      expect(updatedCar.availabilityUpdatedAt).toEqual(now);
      expect(updatedCar.availability).toEqual(CarAvailability.AVAILABLE);
    });
  });

  describe('Rental reservation expired event', () => {
    const externalFleetId = randomUUID().toString();
    const externalCarId = randomUUID().toString();
    const now = DateTime.now();

    beforeAll(async () => {
      initInvocationContext();

      carModel = await carModelRepository.createOrFail(
        new CarModel(
          null,
          new CarModelName('rental-reservation-expired-car-model'),
        ),
      );

      car = await carRepository.insertOne(
        new CarBuilder()
          .withCarModelId(carModel.id)
          .withCarModel(carModel)
          .withExternalFleetId(externalFleetId)
          .withExternalCarId(externalCarId)
          .withAvailability(CarAvailability.RESERVED)
          .build(),
      );

      InvocationContext.clear();
    });

    it('should update availability for car when rental reservation expired', async () => {
      const event = mockSQSEvent(
        createSQSMessage(
          createRentalReservationExpiredEvent({
            carId: new VulogCarId(externalCarId),
            reservationExpiredAt: now,
            correlationId: randomUUID().toString(),
          }),
        ),
      );

      await handler(event, {} as Context);

      const updatedCar = await carRepository.getOneByIdOrFail(car.id);

      expect(updatedCar.availabilityUpdatedAt).toEqual(now);
      expect(updatedCar.availability).toEqual(CarAvailability.AVAILABLE);
    });

    it('should ignore event with old reservation expired at timestamp', async () => {
      const event = mockSQSEvent(
        createSQSMessage(
          createRentalReservationExpiredEvent({
            carId: new VulogCarId(externalCarId),
            reservationExpiredAt: now.minus({ minutes: 2 }),
            correlationId: randomUUID().toString(),
          }),
        ),
      );

      await handler(event, {} as Context);

      const updatedCar = await carRepository.getOneByIdOrFail(car.id);

      expect(updatedCar.availabilityUpdatedAt).toEqual(now);
      expect(updatedCar.availability).toEqual(CarAvailability.AVAILABLE);
    });
  });

  describe('Rental reservation switched event', () => {
    const externalFleetId = randomUUID().toString();
    const externalCarId1 = randomUUID().toString();
    const externalCarId2 = randomUUID().toString();
    let car1: Car;
    let car2: Car;
    const now = DateTime.now();

    beforeAll(async () => {
      initInvocationContext();

      carModel = await carModelRepository.createOrFail(
        new CarModel(
          null,
          new CarModelName('rental-reservation-switched-car-model'),
        ),
      );

      car1 = await carRepository.insertOne(
        new CarBuilder()
          .withCarModelId(carModel.id)
          .withCarModel(carModel)
          .withExternalFleetId(externalFleetId)
          .withExternalCarId(externalCarId1)
          .withAvailability(CarAvailability.RESERVED)
          .build(),
      );

      car2 = await carRepository.insertOne(
        new CarBuilder()
          .withCarModelId(carModel.id)
          .withCarModel(carModel)
          .withExternalFleetId(externalFleetId)
          .withExternalCarId(externalCarId2)
          .withAvailability(CarAvailability.AVAILABLE)
          .build(),
      );

      InvocationContext.clear();
    });

    it('should update availability for car when rental reservation switched', async () => {
      const event = mockSQSEvent(
        createSQSMessage(
          createRentalReservationSwitchedEvent({
            previousCarId: new VulogCarId(externalCarId1),
            carId: new VulogCarId(externalCarId2),
            createdAt: now,
            reservedAt: now,
            switchedAt: now,
            correlationId: randomUUID().toString(),
          }),
        ),
      );

      await handler(event, {} as Context);

      const updatedCar1 = await carRepository.getOneByIdOrFail(car1.id);
      const updatedCar2 = await carRepository.getOneByIdOrFail(car2.id);
      expect(updatedCar1.availabilityUpdatedAt).toEqual(now);
      expect(updatedCar1.availability).toEqual(CarAvailability.AVAILABLE);
      expect(updatedCar2.availabilityUpdatedAt).toEqual(now);
      expect(updatedCar2.availability).toEqual(CarAvailability.RESERVED);
    });

    it('should ignore event with old reservation switched at timestamp', async () => {
      const event = mockSQSEvent(
        createSQSMessage(
          createRentalReservationSwitchedEvent({
            carId: new VulogCarId(externalCarId1),
            previousCarId: new VulogCarId(externalCarId2),
            createdAt: now.minus({ minutes: 2 }),
            reservedAt: now.minus({ minutes: 2 }),
            switchedAt: now.minus({ minutes: 2 }),
            correlationId: randomUUID().toString(),
          }),
        ),
      );

      await handler(event, {} as Context);

      const updatedCar1 = await carRepository.getOneByIdOrFail(car1.id);
      const updatedCar2 = await carRepository.getOneByIdOrFail(car2.id);

      expect(updatedCar1.availabilityUpdatedAt).toEqual(now);
      expect(updatedCar1.availability).toEqual(CarAvailability.AVAILABLE);
      expect(updatedCar2.availabilityUpdatedAt).toEqual(now);
      expect(updatedCar2.availability).toEqual(CarAvailability.RESERVED);
    });
  });

  describe('AIMA car enabled event', () => {
    const externalFleetId = randomUUID().toString();
    const externalCarId = randomUUID().toString();
    const now = DateTime.now();

    beforeAll(async () => {
      initInvocationContext();

      carModel = await carModelRepository.createOrFail(
        new CarModel(null, new CarModelName('aima-car-enabled-car-model')),
      );

      car = await carRepository.insertOne(
        new CarBuilder()
          .withCarModelId(carModel.id)
          .withCarModel(carModel)
          .withExternalFleetId(externalFleetId)
          .withExternalCarId(externalCarId)
          .withAvailability(CarAvailability.OUT_OF_SERVICE)
          .build(),
      );

      InvocationContext.clear();
    });

    it('should update availability for car when car is enabled on AIMA', async () => {
      const oldCar = await carRepository.getOneByIdOrFail(car.id);

      expect(oldCar.availability).toEqual(CarAvailability.OUT_OF_SERVICE);

      const event = mockSQSEvent(
        createSQSMessage(
          createAimaCarEnabledEvent({
            carId: new VulogCarId(externalCarId),
            timestamp: now,
            correlationId: randomUUID().toString(),
          }),
        ),
      );

      await handler(event, {} as Context);

      const updatedCar = await carRepository.getOneByIdOrFail(car.id);

      expect(updatedCar.availabilityUpdatedAt).toEqual(now);
      expect(updatedCar.availability).toEqual(CarAvailability.AVAILABLE);
    });
  });

  describe('AIMA car put OOS event', () => {
    const externalFleetId = randomUUID().toString();
    const externalCarId = randomUUID().toString();
    const now = DateTime.now();

    beforeAll(async () => {
      initInvocationContext();

      carModel = await carModelRepository.createOrFail(
        new CarModel(null, new CarModelName('aima-car-put-oos-car-model')),
      );

      car = await carRepository.insertOne(
        new CarBuilder()
          .withCarModelId(carModel.id)
          .withCarModel(carModel)
          .withExternalFleetId(externalFleetId)
          .withExternalCarId(externalCarId)
          .withAvailability(CarAvailability.AVAILABLE)
          .build(),
      );

      InvocationContext.clear();
    });

    it('should update availability for car when car is put OOS on AIMA', async () => {
      const oldCar = await carRepository.getOneByIdOrFail(car.id);

      expect(oldCar.availability).toEqual(CarAvailability.AVAILABLE);

      const event = mockSQSEvent(
        createSQSMessage(
          createAimaCarPutOutOfServiceEvent({
            carId: new VulogCarId(externalCarId),
            timestamp: now,
            correlationId: randomUUID().toString(),
          }),
        ),
      );

      await handler(event, {} as Context);

      const updatedCar = await carRepository.getOneByIdOrFail(car.id);

      expect(updatedCar.availabilityUpdatedAt).toEqual(now);
      expect(updatedCar.availability).toEqual(CarAvailability.OUT_OF_SERVICE);
    });
  });
});
