import { beforeEach, describe, expect, it } from '@jest/globals';
import jwt from 'jsonwebtoken';
import { AuthenticationService } from 'src/auth/authentication/authentication.service';
import { AuthConfigOptions } from 'src/auth/config/auth.config';
import { UnauthorizedError } from 'src/shared/errors/http.error';
import { mockEnv } from 'test/shared/test-util';

describe('AuthenticationService', () => {
  let authService: AuthenticationService;
  const secret = 'test-secret';
  const validPayload = { userId: '123', role: 'admin' };

  beforeEach(() => {
    mockEnv();
    authService = new AuthenticationService(new AuthConfigOptions(secret));
  });

  describe('verifyToken', () => {
    it('should successfully verify a valid token', async () => {
      const token = jwt.sign(validPayload, secret);

      const { payload, signature } = await authService.verifyToken(token);

      expect(payload).toMatchObject(validPayload);
      expect(signature).toBeTruthy();
    });

    it('should throw UnauthorizedError when token is empty', async () => {
      const token = '';

      try {
        await authService.verifyToken(token);
      } catch (error) {
        if (error instanceof UnauthorizedError) {
          expect(error.cause).toEqual('jwt must be provided');
          return;
        }
      }

      fail('should have thrown UnauthorizedError');
    });

    it('should throw UnauthorizedError when token is invalid', async () => {
      const token = 'invalid-token';

      try {
        await authService.verifyToken(token);
      } catch (error) {
        if (error instanceof UnauthorizedError) {
          expect(error.cause).toEqual('jwt malformed');
          return;
        }
      }

      fail('should have thrown UnauthorizedError');
    });

    it('should throw UnauthorizedError when token is signed with different secret', async () => {
      const token = jwt.sign(validPayload, 'wrong-secret');

      try {
        await authService.verifyToken(token);
      } catch (error) {
        if (error instanceof UnauthorizedError) {
          expect(error.cause).toEqual('invalid signature');
          return;
        }
      }

      fail('should have thrown UnauthorizedError');
    });

    it('should throw UnauthorizedError when token is expired', async () => {
      const token = jwt.sign(validPayload, secret, { expiresIn: '0s' });

      try {
        await authService.verifyToken(token);
      } catch (error) {
        if (error instanceof UnauthorizedError) {
          expect(error.cause).toEqual('jwt expired');
          return;
        }
      }

      fail('should have thrown UnauthorizedError');
    });
  });
});
