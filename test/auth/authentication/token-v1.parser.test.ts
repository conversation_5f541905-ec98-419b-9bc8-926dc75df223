import { beforeEach, describe, expect, it } from '@jest/globals';
import {
  Permission,
  PermissionAction,
} from 'src/auth/authorization/permission';
import { Resource } from 'src/auth/authorization/resource';
import { TokenV1Parser } from 'src/auth/authorization/token-v1-parser.util';
import { createTestToken } from 'test/shared/test-util';

describe('TokenV1Parser', () => {
  describe('validatePermissions', () => {
    let parser: TokenV1Parser;

    beforeEach(() => {
      parser = new TokenV1Parser();
    });

    it('should return true when user has required permission', async () => {
      const payload = createTestToken({
        permissions: {
          car_model: PermissionAction.Read,
        },
      });
      const hasPermission = await parser.hasAccess(
        payload,
        new Permission(Resource.CarModel, PermissionAction.Read),
      );

      expect(hasPermission).toBe(true);
    });

    it('should return false when user lacks required permission', async () => {
      const payload = createTestToken({
        permissions: {
          'car-model': PermissionAction.Read,
        },
      });
      const hasPermission = await parser.hasAccess(
        payload,
        new Permission(Resource.CarModel, PermissionAction.Create),
      );

      expect(hasPermission).toBe(false);
    });

    it('should handle empty permissions', async () => {
      const payload = createTestToken({
        permissions: {},
      });
      const hasPermission = await parser.hasAccess(
        payload,
        new Permission(Resource.CarModel, PermissionAction.Read),
      );

      expect(hasPermission).toBe(false);
    });

    it('should return true when user has all permission', async () => {
      const payload = createTestToken({
        permissions: {
          all: PermissionAction.All,
        },
      });
      const hasPermission = await parser.hasAccess(
        payload,
        new Permission(Resource.CarModel, PermissionAction.Create),
      );

      expect(hasPermission).toBe(true);
    });
    it('should return true when required Read and user has all resource with only R permission', async () => {
      const payload = createTestToken({
        permissions: {
          all: PermissionAction.Read,
        },
      });
      const hasPermission = await parser.hasAccess(
        payload,
        new Permission(Resource.CarModel, PermissionAction.Read),
      );

      expect(hasPermission).toBe(true);
    });
    it('should return false when required Delete and user has all resource with only R permission', async () => {
      const payload = createTestToken({
        permissions: {
          all: PermissionAction.Read,
        },
      });
      const hasPermission = await parser.hasAccess(
        payload,
        new Permission(Resource.CarModel, PermissionAction.Delete),
      );

      expect(hasPermission).toBe(false);
    });
  });
});
