import { AuthFeatureFlag } from 'src/auth/auth.feature-flag';
import {
  Auth0Token,
  Auth0TokenParser,
} from 'src/auth/authorization/auth0.token-parser';
import {
  Permission,
  PermissionAction,
} from 'src/auth/authorization/permission';
import { Resource } from 'src/auth/authorization/resource';
import { AuthConfigOptions } from 'src/auth/config/auth.config';
import { Auth0TokenBuilder } from 'test/auth/helpers/auth0.token.builder';
import { aRecentDate, aSoonDate, aString } from 'test/shared/random-data';

describe('Auth0TokenParser', () => {
  const options = new AuthConfigOptions('', 'auth0-server');
  const flags = new AuthFeatureFlag(null);

  const parser = new Auth0TokenParser(options, flags);

  const validToken: Auth0Token = {
    userId: 'auth0|12345678-aaaa-bbbb-cccc-1234567890ab',
    username: 'test-username',
    email: '<EMAIL>',
    name: 'Test User',
    lastChangePasswordTime: aRecentDate(),
    'https://bo-api.blusg.com/roles': ['Grand Admin'],
    iss: 'auth0-server/',
    sub: 'auth0|12345678-aaaa-bbbb-cccc-1234567890ab',
    aud: ['test', 'auth0-server/userinfo'],
    iat: aRecentDate().valueOf(),
    exp: aSoonDate().valueOf(),
    scope: 'openid profile email offline_access',
    org_id: 'org_test',
    azp: aString(),
    permissions: [`${PermissionAction.Read}:${Resource.Car}`],
  };

  describe('isValid', () => {
    it('should not recognize a token payload if not issued by our Auth0 server', () => {
      const payload = new Auth0TokenBuilder()
        .withIss('not-our-auth0-server/')
        .build();

      const result = parser.isValid(payload);

      expect(result).toBe(false);
    });

    it('should recognize a Auth0 payload as soon as the iss is matching', () => {
      const result = parser.isValid(validToken);

      expect(result).toBe(true);
    });
  });

  describe('hasAccess', () => {
    it('should reject a token that no permission', async () => {
      const result = await parser.hasAccess(
        new Auth0TokenBuilder().withPermissions([]).build(),
        new Permission(Resource.Car, PermissionAction.Read),
      );

      expect(result).toBe(false);
    });

    it('should accept the valid auth0 token on a permitted action', async () => {
      const result = await parser.hasAccess(
        new Auth0TokenBuilder()
          .withPermissions([
            new Permission(Resource.Car, PermissionAction.Read),
          ])
          .build(),
        new Permission(Resource.Car, PermissionAction.Read),
      );

      expect(result).toBe(true);
    });

    it('should reject the valid auth0 token on an action not permitted', async () => {
      const result = await parser.hasAccess(
        new Auth0TokenBuilder()
          .withPermissions([
            new Permission(Resource.Car, PermissionAction.Read),
          ])
          .build(),
        new Permission(Resource.Car, PermissionAction.Delete),
      );

      expect(result).toBe(false);
    });
  });
});
