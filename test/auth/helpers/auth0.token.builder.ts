import { Auth0Token } from 'src/auth/authorization/auth0.token-parser';
import {
  Permission,
  PermissionAction,
} from 'src/auth/authorization/permission';
import { Resource } from 'src/auth/authorization/resource';
import {
  aRecentDate,
  aSoonDate,
  aString,
  aUrl,
  aUuid,
} from 'test/shared/random-data';

export class Auth0TokenBuilder {
  constructor(
    private userId: string = `auth0!${aUuid()}`,
    private name: string = aString(),
    private email: string = aString(),
    private username: string = aString(),
    private lastChangePasswordTime: Date = aRecentDate(),
    private iss: string = aUrl(),
    private sub: string = `auth0!${aUuid()}`,
    private aud: string[] = [aString()],
    private iat: number = aRecentDate().valueOf(),
    private exp: number = aSoonDate().valueOf(),
    private org_id: string = aString(),
    private azp: string = aString(),
    private permissions: string[] = [
      `${Resource.Car}:${PermissionAction.All}`,
      `${Resource.CarModel}:${PermissionAction.All}`,
    ],
  ) {}

  withIss(iss: string) {
    this.iss = iss;
    return this;
  }

  withPermissions(permissions: Permission[]) {
    this.permissions = permissions.map((p) => p.toTokenFormat());
    return this;
  }

  build(): Auth0Token {
    return {
      aud: this.aud,
      exp: this.exp,
      azp: this.azp,
      iat: this.iat,
      iss: this.iss,
      sub: this.sub,
      org_id: this.org_id,
      permissions: this.permissions,
      name: this.name,
      email: this.email,
      username: this.username,
      userId: this.userId,
      lastChangePasswordTime: this.lastChangePasswordTime,
    };
  }
}
