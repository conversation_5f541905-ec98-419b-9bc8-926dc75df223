import { beforeEach, describe, expect, it } from '@jest/globals';
import { APIGatewayEvent } from 'aws-lambda';
import jwt from 'jsonwebtoken';
import { AuthServiceAdapter } from 'src/auth/auth.service.adapter';
import { AuthenticationService } from 'src/auth/authentication/authentication.service';
import {
  Permission,
  PermissionAction,
} from 'src/auth/authorization/permission';
import { Resource } from 'src/auth/authorization/resource';
import { AuthConfigOptions } from 'src/auth/config/auth.config';
import {
  ForbiddenError,
  UnauthorizedError,
} from 'src/shared/errors/http.error';
import { InvocationContext } from 'src/shared/invocation-context';
import { AuthFeatureFlagMock } from 'test/auth/helpers/auth.feature-flag.mock';
import { createTestToken } from 'test/shared/test-util';

const jwtSecret = 'test-secret';
const options = new AuthConfigOptions(jwtSecret, 'auth0-server');

function authorizationHeader(payload: string | object): string {
  const token = jwt.sign(payload, jwtSecret, {
    expiresIn: '1h',
  });
  return `Bearer ${token}`;
}

describe('AuthServiceAdapter', () => {
  let authServiceAdapter: AuthServiceAdapter;
  let mockEvent: APIGatewayEvent;

  beforeEach(async () => {
    authServiceAdapter = new AuthServiceAdapter(
      new AuthenticationService(options),
      new AuthFeatureFlagMock(),
      options,
    );
    mockEvent = {
      headers: {
        Authorization: 'Bearer valid-token',
      },
    } as unknown as APIGatewayEvent;

    InvocationContext.init('fake-request-id', 'fake-correlation-id');
  });

  describe('authorizeRequest', () => {
    it('should throw UnauthorizedError when Authorization header is missing', async () => {
      const eventWithoutAuth = {
        headers: {},
      } as APIGatewayEvent;

      await expect(
        authServiceAdapter.authorizeRequest(
          eventWithoutAuth,
          new Permission(Resource.CarModel, PermissionAction.Read),
        ),
      ).rejects.toThrow(UnauthorizedError);
    });

    it('should throw UnauthorizedError when the header is not bearer', async () => {
      mockEvent.headers.authorization = 'invalid-token';

      await expect(
        authServiceAdapter.authorizeRequest(
          mockEvent,
          new Permission(Resource.CarModel, PermissionAction.Read),
        ),
      ).rejects.toThrow(UnauthorizedError);
    });

    it('should throw UnauthorizedError when token is invalid', async () => {
      mockEvent.headers.authorization = 'Bearer invalid-token';

      await expect(
        authServiceAdapter.authorizeRequest(
          mockEvent,
          new Permission(Resource.CarModel, PermissionAction.Read),
        ),
      ).rejects.toThrow(UnauthorizedError);
    });

    it('should throw UnauthorizedError when the token does not match any parser', async () => {
      mockEvent.headers.authorization = authorizationHeader({
        iss: 'test',
        comment: 'This payload does not match any parser.',
      });

      await expect(
        authServiceAdapter.authorizeRequest(
          mockEvent,
          new Permission(Resource.CarModel, PermissionAction.Read),
        ),
      ).rejects.toThrow(UnauthorizedError);
    });

    it('should throw ForbiddenError when user lacks required permission', async () => {
      mockEvent.headers.authorization = authorizationHeader(
        createTestToken({
          permissions: {
            'car-model': PermissionAction.Read,
          },
        }),
      );

      await expect(
        authServiceAdapter.authorizeRequest(
          mockEvent,
          new Permission(Resource.CarModel, PermissionAction.Create),
        ),
      ).rejects.toThrow(ForbiddenError);
    });

    it('should pass when user has required permission', async () => {
      mockEvent.headers.authorization = authorizationHeader(
        createTestToken({
          permissions: {
            car_model: PermissionAction.Create,
          },
        }),
      );

      await expect(
        authServiceAdapter.authorizeRequest(
          mockEvent,
          new Permission(Resource.CarModel, PermissionAction.Create),
        ),
      ).resolves.not.toThrow();
    });
  });

  afterEach(() => {
    InvocationContext.clear();
  });
});
