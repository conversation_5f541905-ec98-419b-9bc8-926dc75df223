import { CarState } from 'src/car-state/car-state.type';
import { CarId } from 'src/car/car.type';
import { VulogCarId, VulogFleetId } from 'src/shared/shared.type';
import { VulogVehicleInfoResponseDto } from 'src/vulog/dto/vulog-vehicle-info-response.dto';

export class MockVulogClient {
  async getCarState(
    fleetId: VulogFleetId,
    vehicleId: VulogCarId,
    carId: CarId,
  ): Promise<CarState> {
    // Return a mock car state with predictable data for testing
    const rawVulogVehicleInfo: unknown = buildRawVulogVehicleInfo(
      fleetId,
      vehicleId,
    );

    return new VulogVehicleInfoResponseDto(rawVulogVehicleInfo).toCarState(
      carId,
    );
  }

  private vehicleIds: VulogCarId[] = [];

  setVehicleIds(vehicleIds: VulogCarId[]) {
    this.vehicleIds = vehicleIds;
  }

  async getVehiclesInfo(
    fleetId: VulogFleetId,
  ): Promise<VulogVehicleInfoResponseDto[]> {
    return this.vehicleIds.map(
      (id) =>
        new VulogVehicleInfoResponseDto(buildRawVulogVehicleInfo(fleetId, id)),
    );
  }
}

export function buildRawVulogVehicleInfo(
  fleetId: VulogFleetId,
  vehicleId: VulogCarId,
): unknown {
  return {
    // providerPayload
    id: vehicleId.value,
    fleetId: fleetId.value,
    status: {
      locked: true,
      energyLevel: 80,
      doorsAndWindowsClosed: true,
      doors: {
        frontLeftClosed: true,
        frontRightClosed: true,
        rearLeftClosed: true,
        rearRightClosed: true,
        trunkClosed: true,
      },
      windows: {
        frontLeftClosed: true,
        frontRightClosed: true,
        rearLeftClosed: true,
        rearRightClosed: true,
        trunkClosed: true,
      },
      cablePlugged: false,
      charging: false,
      engineOn: false,
    },
    zones: {
      current: [
        {
          type: 'operational',
          zoneId: 'test-zone-id-1',
          sticky: false,
          labels: [],
        },
        {
          type: 'allowed',
          zoneId: 'test-zone-id-2',
          sticky: false,
          labels: [],
        },
      ],
    },
  };
}
