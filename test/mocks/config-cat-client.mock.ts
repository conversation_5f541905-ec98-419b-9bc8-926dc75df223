import {
  IConfigCatClient,
  SettingTypeOf,
  SettingValue,
  User,
} from 'configcat-node';

export class MockConfigCatClient implements Partial<IConfigCatClient> {
  private flags: { [key: string]: boolean };

  constructor() {
    this.setFlags();
  }

  setFlags(
    flags: { [key: string]: boolean } = {
      enableConsumingVgEvents: true,
      enableCsCarAvailabilityUpdatedEvent: true,
    },
  ) {
    this.flags = { ...flags };
  }

  async getValueAsync<T extends SettingValue>(
    key: string,
    defaultValue?: T,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    user?: User,
  ): Promise<SettingTypeOf<T>> {
    const value = this.flags[key];

    if (!value && !!defaultValue) {
      return defaultValue as SettingTypeOf<T>;
    }

    return value as SettingTypeOf<T>;
  }
}
