import {
  AimaCarEnabledEvent,
  AimaCarPutOutOfServiceEvent,
  CarInfo,
  RentalEndedEvent,
  RentalEndedEventPayload,
  RentalReservationCancelledEvent,
  RentalReservationCreatedEvent,
  RentalReservationExpiredEvent,
  RentalReservationSwitchedEvent,
  RentalStartedEvent,
  StationInfo,
  TripInfo,
} from '@bluesg-2/event-library';
import { randomUUID } from 'crypto';
import { DateTime } from 'luxon';
import { VulogCarId } from 'src/shared/shared.type';

export function createSQSMessage(
  rentalEndedEvent:
    | RentalEndedEvent
    | RentalStartedEvent
    | RentalReservationCreatedEvent
    | RentalReservationSwitchedEvent
    | RentalReservationCancelledEvent
    | RentalReservationExpiredEvent
    | AimaCarEnabledEvent
    | AimaCarPutOutOfServiceEvent,
): Record<string, string> {
  return {
    Type: 'Notification',
    MessageId: randomUUID().toString(),
    Message: JSON.stringify(rentalEndedEvent),
  };
}

export function createRentalStartedEvent(props: {
  carId: VulogCarId;
  rentalStartedAt: DateTime;
  startStationId?: string;
  correlationId?: string;
  userId?: string;
  status?: string;
}): RentalStartedEvent {
  return new RentalStartedEvent({
    sender: 'rental-service',
    sentAt: props.rentalStartedAt.toMillis(),
    correlationId: props.correlationId,
    payload: {
      carId: props.carId.value,
      startStationId: props.startStationId,
      startedAt: props.rentalStartedAt.toISO(),
      stationId: props.startStationId,
      carMetadata: {
        id: props.carId.value,
        model: 'rental-started-car-model',
        vulogId: props.carId.value,
      },
      id: randomUUID().toString(),
      createdAt: props.rentalStartedAt.toISO(),
      reservedAt: props.rentalStartedAt.toISO(),
      status: props.status,
      userId: props.userId,
    },
  });
}

export function createRentalEndedEvent(props: {
  carId: VulogCarId;
  rentalEndedAt: DateTime;
  endStationId?: string;
  rentalId?: string;
  correlationId?: string;
}): RentalEndedEvent {
  return new RentalEndedEvent({
    sender: 'rental-service',
    sentAt: props.rentalEndedAt.toMillis(),
    correlationId: props.correlationId,
    payload: {
      carId: props.carId.value,
      endStationId: props.endStationId,
      endedAt: props.rentalEndedAt.toISO(),
      tripInfo: {
        id: props.rentalId ?? randomUUID().toString(),
        endedAt: props.rentalEndedAt.toISO(),
      } as TripInfo,
      carInfo: {
        id: props.carId.value,
      } as CarInfo,
      endStationInfo: props.endStationId
        ? ({ id: props.endStationId } as StationInfo)
        : undefined,
    } as RentalEndedEventPayload,
  });
}

export function createRentalReservationCreatedEvent(props: {
  carId: VulogCarId;
  reservationStartedAt: DateTime;
  reservedAt: DateTime;
  correlationId?: string;
  userId?: string;
  status?: string;
}): RentalReservationCreatedEvent {
  return new RentalReservationCreatedEvent({
    sender: 'rental-service',
    sentAt: props.reservationStartedAt.toMillis(),
    correlationId: props.correlationId,
    payload: {
      carId: props.carId.value,
      createdAt: props.reservationStartedAt.toISO(),
      id: randomUUID().toString(),
      reservedAt: props.reservedAt.toISO(),
      status: props.status,
      userId: props.userId,
    },
  });
}

export function createRentalReservationCancelledEvent(props: {
  carId: VulogCarId;
  reservationCancelledAt: DateTime;
  createdAt?: DateTime;
  reservedAt?: DateTime;
  correlationId?: string;
  userId?: string;
  status?: string;
}): RentalReservationCancelledEvent {
  return new RentalReservationCancelledEvent({
    sender: 'rental-service',
    sentAt: props.reservationCancelledAt.toMillis(),
    correlationId: props.correlationId,
    payload: {
      carId: props.carId.value,
      cancelledAt: props.reservationCancelledAt.toISO(),
      id: randomUUID().toString(),
      createdAt: props.createdAt?.toISO() ?? new Date().toISOString(),
      reservedAt: props.reservedAt?.toISO() ?? new Date().toISOString(),
      status: props.status,
      userId: props.userId,
    },
  });
}

export function createRentalReservationSwitchedEvent(props: {
  carId: VulogCarId;
  previousCarId: VulogCarId;
  createdAt: DateTime;
  reservedAt: DateTime;
  switchedAt: DateTime;
  correlationId?: string;
  userId?: string;
  status?: string;
}): RentalReservationSwitchedEvent {
  return new RentalReservationSwitchedEvent({
    sender: 'rental-service',
    sentAt: props.reservedAt.toMillis(),
    correlationId: props.correlationId,
    payload: {
      carId: props.carId.value,
      previousCarId: props.previousCarId.value,
      id: randomUUID().toString(),
      createdAt: props.createdAt.toISO(),
      reservedAt: props.reservedAt.toISO(),
      switchedAt: props.switchedAt.toISO(),
      status: props.status,
      userId: props.userId,
    },
  });
}

export function createRentalReservationExpiredEvent(props: {
  carId: VulogCarId;
  reservationExpiredAt: DateTime;
  createdAt?: DateTime;
  reservedAt?: DateTime;
  correlationId?: string;
  userId?: string;
  status?: string;
}): RentalReservationExpiredEvent {
  return new RentalReservationExpiredEvent({
    sender: 'rental-service',
    sentAt: props.reservationExpiredAt.toMillis(),
    correlationId: props.correlationId,
    payload: {
      carId: props.carId.value,
      expiredAt: props.reservationExpiredAt.toISO(),
      id: randomUUID().toString(),
      createdAt: props.createdAt?.toISO() ?? new Date().toISOString(),
      reservedAt: props.reservedAt?.toISO() ?? new Date().toISOString(),
      status: props.status,
      userId: props.userId,
    },
  });
}

export function createAimaCarEnabledEvent(props: {
  carId: VulogCarId;
  timestamp: DateTime;
  correlationId?: string;
}): AimaCarEnabledEvent {
  return new AimaCarEnabledEvent({
    sender: 'rental-service',
    sentAt: props.timestamp.toMillis(),
    correlationId: props.correlationId,
    payload: {
      vulogCarId: props.carId.value,
      timestamp: props.timestamp.toISO(),
    },
  });
}

export function createAimaCarPutOutOfServiceEvent(props: {
  carId: VulogCarId;
  timestamp: DateTime;
  correlationId?: string;
}): AimaCarPutOutOfServiceEvent {
  return new AimaCarPutOutOfServiceEvent({
    sender: 'rental-service',
    sentAt: props.timestamp.toMillis(),
    correlationId: props.correlationId,
    payload: {
      vulogCarId: props.carId.value,
      timestamp: props.timestamp.toISO(),
    },
  });
}
