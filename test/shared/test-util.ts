import {
  DeleteMessageCommand,
  PurgeQueueCommand,
  ReceiveMessageCommand,
  SQSClient,
} from '@aws-sdk/client-sqs';
import { jest } from '@jest/globals';
import {
  APIGatewayEventDefaultAuthorizerContext,
  APIGatewayEventRequestContextWithAuthorizer,
  APIGatewayProxyEvent,
  Context,
  SQSEvent,
  ScheduledEvent,
} from 'aws-lambda';
import { randomUUID } from 'crypto';
import jwt from 'jsonwebtoken';
import { DateTime } from 'luxon';
import { TokenPayload } from 'src/auth/authentication/token';
import { PermissionAction } from 'src/auth/authorization/permission';
import { TokenV1 } from 'src/auth/authorization/token-v1-parser.util';
import { getAwsClientConfig, getConfig } from 'src/shared/env';
import { InvocationContext } from 'src/shared/invocation-context';
import { CORRELATION_ID_HEADER } from 'src/shared/middlewares/invocation-context.middleware';

export async function purgeSqsQueue(): Promise<void> {
  const client = new SQSClient(await getAwsClientConfig());

  const purgeCommand = new PurgeQueueCommand({
    QueueUrl: await getConfig('DUMP_QUEUE_URL'),
  });

  await client.send(purgeCommand);
}
export async function getSqsMessage<T>(): Promise<T> {
  const client = new SQSClient(await getAwsClientConfig());

  const receiveCommand = new ReceiveMessageCommand({
    QueueUrl: await getConfig('DUMP_QUEUE_URL'),
    MaxNumberOfMessages: 1,
    VisibilityTimeout: 10,
    WaitTimeSeconds: 1,
  });

  const receiveResponse = await client.send(receiveCommand);

  expect(receiveResponse.Messages).toBeDefined();
  expect(receiveResponse.Messages.length).toBeGreaterThanOrEqual(1);

  const lastIndex = receiveResponse.Messages.length - 1;

  const deleteCommand = new DeleteMessageCommand({
    QueueUrl: await getConfig('DUMP_QUEUE_URL'),
    ReceiptHandle: receiveResponse.Messages[lastIndex].ReceiptHandle,
  });

  await client.send(deleteCommand);

  return JSON.parse(receiveResponse.Messages[lastIndex].Body);
}

// TODO: the responses our APIs are returning should be typed
export interface TestApiResponse {
  headers: Record<string, string>;
  statusCode: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  body?: any;
}

export async function getResponse(
  handler: (
    event: APIGatewayProxyEvent,
    context: Context,
  ) => Promise<{
    headers: { [key: string]: string };
    statusCode: number;
    body: string | null;
  }>,
  event: APIGatewayProxyEvent,
  correlationId?: string,
): Promise<TestApiResponse> {
  const payload = createTestToken({
    permissions: {
      all: PermissionAction.All,
    },
  });
  const token = jwt.sign(payload, 'test-secret', {
    expiresIn: '1h',
  });
  event.headers['Authorization'] = `Bearer ${token}`;
  if (correlationId) {
    event.headers[CORRELATION_ID_HEADER] = correlationId;
  }
  const response = await handler(event, {} as Context);
  return {
    headers: response.headers,
    statusCode: response.statusCode,
    body: response.body ? JSON.parse(response.body) : null,
  };
}

export function mockScheduledEvent(): ScheduledEvent {
  return {
    id: randomUUID().toString(),
    'detail-type': 'Scheduled Event',
    source: 'aws.scheduler',
    time: DateTime.now().toString(),
  } as ScheduledEvent;
}

export function mockSQSEvent(innerEvent: Record<string, string>): SQSEvent {
  return {
    Records: [
      {
        body: JSON.stringify(innerEvent),
        messageId: randomUUID().toString(),
        receiptHandle: randomUUID().toString(),
        eventSource: 'aws:sqs',
      },
    ],
  } as SQSEvent;
}

export function mockApiGatewayEvent(
  method: string,
  path: string,
  body?: unknown,
  pathParameters?: { [key: string]: string } | null,
  queryStringParameters?: { [key: string]: string } | null,
): APIGatewayProxyEvent {
  return {
    httpMethod: method,
    path,
    body: body ? JSON.stringify(body) : null,
    pathParameters: pathParameters || null,
    queryStringParameters: queryStringParameters || null,
    isBase64Encoded: false,
    resource: '',
    stageVariables: null,
    requestContext:
      {} as APIGatewayEventRequestContextWithAuthorizer<APIGatewayEventDefaultAuthorizerContext>,
    headers: {
      'Content-Type': 'application/json',
    },
    multiValueHeaders: {},
    multiValueQueryStringParameters: null,
  };
}
export function mockEnv() {
  process.env.JWT_SECRET = 'test-secret';
  jest.unstable_mockModule('src/shared/env', () => ({
    getConfig: jest.fn().mockResolvedValue('test-secret' as never), // Mock getConfig to return 'test-secret'
  }));
}

export const createTestToken = (overrides: Partial<TokenV1> = {}): TokenV1 => {
  return {
    userId: '123',
    username: 'test',
    email: '<EMAIL>',
    name: 'test',
    permissions: {
      all: PermissionAction.All,
    },
    lastChangePasswordTime: new Date(),
    v: 1,
    ...overrides,
  };
};

export function initInvocationContext() {
  InvocationContext.init('fake-request-id', 'fake-correlation-id');
  InvocationContext.setToken({
    payload: { userId: 'test-user-id' } as TokenPayload,
    signature: 'foobar',
  });
}
