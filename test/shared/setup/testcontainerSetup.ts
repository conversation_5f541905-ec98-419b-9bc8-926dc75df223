// sort-imports-ignore
// this has to be the very first import for ts to work with jest setup
import 'tsconfig-paths/register';

import { PostgreSqlContainer } from '@testcontainers/postgresql';
import { LocalstackContainer } from '@testcontainers/localstack';
import { getDataSource } from 'src/shared/datasources/datasource';

const setupTestcontainer = async (): Promise<void> => {
  const env = process.env;

  // Logs
  env.LOG_LEVEL = 'info';
  env.LOG_FORMAT = 'console';

  env.NODE_ENV = 'test';
  env.AWS_DEFAULT_REGION = 'ap-southeast-1';
  env.AWS_REGION = 'ap-southeast-1';
  env.AWS_ACCESS_KEY_ID = 'x';
  env.AWS_SECRET_ACCESS_KEY = 'x';
  env.DB_TYPE = 'postgres';
  env.DB_HOST = 'localhost';
  env.DB_PORT = '5555';
  env.DB_USERNAME = 'postgres';
  env.DB_PASSWORD = 'password';
  env.DB_NAME = 'car_service_test';
  env.DB_SSL = 'false';
  env.DB_SSL_CERT = '';
  env.DB_AUTH_MODE = '';
  env.ENV_NAME = 'test';
  env.VULOG_BASE_API_URL = 'https://vg-sta.vulog.net';
  env.VULOG_CLIENT_ID = 'xxx';
  env.VULOG_CLIENT_SECRET = 'xxx';
  env.VULOG_API_KEY = 'xxx';
  env.VULOG_USERNAME = 'xxx';
  env.VULOG_GRANT_TYPE = 'password';
  env.LOCATION_RECONCILIATION_WINDOW_MINUTES = '20';
  env.AVAILABILITY_RECONCILIATION_WINDOW_MINUTES = '20';
  env.VULOG_PASSWORD = 'xxx';
  env.CONFIG_CAT_SDK_KEY = 'xxx';
  env.STS_BASE_API_URL = 'http://example.com';
  env.JWT_SECRET = 'test-secret';
  env.AUTH0_SERVER_URL = 'auth0.test';
  env.CONFIG_CAT_AUTH_SDK_KEY = 'configcat-auth-sdk-key';

  if (!env.DB_NAME || !env.DB_USERNAME || !env.DB_PASSWORD) {
    throw new Error('DB_NAME, DB_USERNAME, and DB_PASSWORD must be set');
  }
  console.log('Setting up test postgres container...');
  const postgresContainer = await new PostgreSqlContainer('postgres:16')
    .withDatabase(env.DB_NAME)
    .withUsername(env.DB_USERNAME)
    .withPassword(env.DB_PASSWORD)
    .withCommand([
      'postgres',
      '-c',
      'log_statement=all', // Log all SQL statements
    ])
    .start();

  console.log('Test postgres container started');

  env.DB_HOST = postgresContainer.getHost();
  env.DB_PORT = postgresContainer.getPort().toString();

  const dataSource = await getDataSource();
  await dataSource.runMigrations();
  console.log('Database migrated');

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (globalThis as any).__TEST_POSTGRES_CONTAINER__ = postgresContainer;

  console.log('Setting up test localstack container...');
  const localstackContainer = await new LocalstackContainer(
    'localstack/localstack:stable',
  )
    .withCopyDirectoriesToContainer([
      { source: './localstack_setup', target: '/etc/localstack/init/ready.d' },
    ])
    .start();

  console.log('Test localstack container started');

  env.LOCALSTACK_ENDPOINT = localstackContainer.getConnectionUri();

  env.MESSAGE_BUS_TOPIC_ARN =
    'arn:aws:sns:ap-southeast-1:000000000000:topic56789';

  env.DUMP_QUEUE_URL =
    'http://sqs.ap-southeast-1.localhost.localstack.cloud:4566/000000000000/queue123';

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (globalThis as any).__TEST_LOCALSTACK_CONTAINER__ = localstackContainer;
};

export default setupTestcontainer;
