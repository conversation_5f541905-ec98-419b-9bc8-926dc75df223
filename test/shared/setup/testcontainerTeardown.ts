import 'tsconfig-paths/register';

const teardownTestcontainer = async (): Promise<void> => {
  console.log('Stopping test postgres container...');
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const postgresContainer = (globalThis as any).__TEST_POSTGRES_CONTAINER__;
  await postgresContainer.stop();
  console.log('Test postgres container stopped');

  console.log('Stopping test localstack container...');
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const localstackContainer = (globalThis as any).__TEST_LOCALSTACK_CONTAINER__;
  await localstackContainer.stop();
  console.log('Test localstack container stopped');
};

export default teardownTestcontainer;
