import { jest } from '@jest/globals';

const mockSend = jest.fn();

jest.mock('@aws-sdk/client-secrets-manager', () => ({
  GetSecretValueCommand: jest.fn(),
  SecretsManagerClient: jest.fn().mockImplementation(() => {
    return { send: mockSend };
  }),
}));

describe('env', () => {
  beforeEach(() => {
    jest.resetModules();
  });

  it('should retrieve secret from Secrets Manager', async () => {
    (mockSend as jest.Mock).mockReturnValue({
      SecretString: JSON.stringify({ FOO: 'SUPER_SECRET_BAR' }),
    });

    process.env.SECRET_NAME = 'car-service-secret';
    process.env.FOO = 'BAR';

    const { getConfig } = await import('src/shared/env');

    const value = await getConfig('FOO');
    expect(value).toBe('SUPER_SECRET_BAR');
  });

  it('should fallback to env var when secret is missing from Secrets Manager', async () => {
    (mockSend as jest.Mock).mockReturnValue({
      SecretString: JSON.stringify({ FOO2: 'SUPER_SECRET_BAR' }),
    });

    process.env.SECRET_NAME = 'car-service-secret';
    process.env.FOO = 'BAR';

    const { getConfig } = await import('src/shared/env');

    const value = await getConfig('FOO');
    expect(value).toBe('BAR');
  });

  it('should return default value when provided', async () => {
    (mockSend as jest.Mock).mockReturnValue({
      SecretString: JSON.stringify({}),
    });

    process.env.SECRET_NAME = 'car-service-secret';
    delete process.env.FOO;

    const { getConfig } = await import('src/shared/env');

    const value = await getConfig('FOO', { default: 'DEFAULT_BAR' });
    expect(value).toBe('DEFAULT_BAR');
  });

  it('should return undefined when config is optional', async () => {
    (mockSend as jest.Mock).mockReturnValue({
      SecretString: JSON.stringify({}),
    });

    process.env.SECRET_NAME = 'car-service-secret';
    delete process.env.FOO;

    const { getConfig } = await import('src/shared/env');

    const value = await getConfig('FOO', { isOptional: true });
    expect(value).toBe(undefined);
  });

  it('should throw error when default is not provided and is not optional', async () => {
    (mockSend as jest.Mock).mockReturnValue({
      SecretString: JSON.stringify({}),
    });

    process.env.SECRET_NAME = 'car-service-secret';
    delete process.env.FOO;

    const { getConfig } = await import('src/shared/env');

    try {
      await getConfig('FOO');
    } catch (error) {
      if (error instanceof Error) {
        expect(error.message).toBe('ENV_NOT_FOUND: FOO');
        return;
      }
    }

    throw new Error('Should have thrown EnvironmentConfigNotFoundError');
  });

  it('should return env var correctly when SECRET_NAME is not set', async () => {
    delete process.env.SECRET_NAME;
    process.env.FOO = 'BAR';

    const { getConfig } = await import('src/shared/env');

    const actual = await getConfig('FOO');

    expect(actual).toBe('BAR');
  });

  it('should return env var as number correctly when SECRET_NAME is not set', async () => {
    delete process.env.SECRET_NAME;
    process.env.FOO = '42.1';

    const { getConfigNumber } = await import('src/shared/env');

    const actual = await getConfigNumber('FOO');

    expect(actual).toBe(42.1);
  });

  it('should return env var as boolean correctly when SECRET_NAME is not set', async () => {
    delete process.env.SECRET_NAME;
    process.env.FOO = 'TRUE';

    const { getConfigBoolean } = await import('src/shared/env');

    const actual = await getConfigBoolean('FOO');

    expect(actual).toBe(true);
  });
});
