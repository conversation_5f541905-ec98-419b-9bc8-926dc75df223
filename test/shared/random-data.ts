import * as falso from '@ngneat/falso';

export function aBoolean(): boolean {
  return falso.randBoolean();
}

export function aNumber(max: number = 1000): number {
  return falso.randNumber({
    max: max,
  });
}

export function aString(nbWords: number = 1): string {
  return falso
    .randWord({
      length: nbWords,
    })
    .join(' ');
}

export function aUuid(): string {
  return falso.randUuid();
}

export function aUrl(): string {
  return falso.randUrl();
}

export function aRecentDate(): Date {
  return falso.randRecentDate();
}

export function aSoonDate(): Date {
  return falso.randSoonDate();
}
