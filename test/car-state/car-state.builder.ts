import { DateTime } from 'luxon';
import {
  CarBatteryLevel,
  CarState,
  CarStateId,
} from 'src/car-state/car-state.type';
import { CarId } from 'src/car/car.type';
import {
  DoorsDetails,
  StationDetails,
  WindowsDetails,
} from 'src/vulog/vulog.type';
import { aBoolean, aNumber, aRecentDate, aUuid } from 'test/shared/random-data';

export class CarStateBuilder<RawProviderPayload extends object> {
  constructor(
    private id: CarStateId = new CarStateId(aUuid()),
    private carId: CarId = new CarId(aUuid()),
    private locked: boolean = aBoolean(),
    private batteryLevel: CarBatteryLevel = new CarBatteryLevel(aNumber(100)),
    private allDoorsWindowsClosed: boolean = aBoolean(),
    private doors: DoorsDetails = {
      frontLeftClosed: aBoolean(),
      frontRightClosed: aBoolean(),
      rearLeftClosed: aBoolean(),
      rearRightClosed: aBoolean(),
      trunkClosed: aBoolean(),
    },
    private windows: WindowsDetails = {
      frontLeftClosed: aBoolean(),
      frontRightClosed: aBoolean(),
      rearLeftClosed: aBoolean(),
      rearRightClosed: aBoolean(),
      trunkClosed: aBoolean(),
    },
    private engineOn: boolean = aBoolean(),
    private cablePlugged: boolean = aBoolean(),
    private charging: boolean = aBoolean(),
    private stations: StationDetails = {
      current: [{ zoneId: aUuid() }],
    },
    private providerPayload: RawProviderPayload = {} as RawProviderPayload,
    private createdAt: DateTime = DateTime.fromJSDate(aRecentDate()),
    private modifiedAt: DateTime = DateTime.fromJSDate(aRecentDate()),
  ) {}

  withCarId(id: CarId): CarStateBuilder<RawProviderPayload> {
    this.carId = id;
    return this;
  }

  withBatteryLevel(
    level: CarBatteryLevel,
  ): CarStateBuilder<RawProviderPayload> {
    this.batteryLevel = level;
    return this;
  }

  build(): CarState<RawProviderPayload> {
    return new CarState(
      this.id,
      this.carId,
      this.locked,
      this.batteryLevel,
      this.allDoorsWindowsClosed,
      this.doors,
      this.windows,
      this.engineOn,
      this.cablePlugged,
      this.charging,
      this.stations,
      this.providerPayload,
      this.createdAt,
      this.modifiedAt,
    );
  }
}
