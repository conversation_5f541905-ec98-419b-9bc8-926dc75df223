import * as falso from '@ngneat/falso';
import { klona } from 'klona';
import { DateTime } from 'luxon';
import { CarStateEntity } from 'src/car-state/entities/car-state.entity';
import { MockBuilder } from 'test/shared/mock.builder';

export class CarStateEntityBuilder extends MockBuilder<CarStateEntity> {
  protected result: CarStateEntity = new CarStateEntity();

  buildId(value: string = falso.randUuid()): CarStateEntityBuilder {
    this.result.id = value;

    return this;
  }

  buildCarId(value: string): CarStateEntityBuilder {
    this.result.carId = value;

    return this;
  }

  buildLocked(value: boolean = falso.randBoolean()): CarStateEntityBuilder {
    this.result.locked = value;

    return this;
  }

  buildEvBatteryLevel(
    value: number = falso.randNumber({ max: 100 }),
  ): CarStateEntityBuilder {
    this.result.evBatteryLevel = value.toString();

    return this;
  }

  buildAllDoorsWindowsClosed(
    value: boolean = falso.randBoolean(),
  ): CarStateEntityBuilder {
    this.result.allDoorsWindowsClosed = value;

    return this;
  }

  buildDoors(value: object): CarStateEntityBuilder {
    this.result.doors = value;

    return this;
  }

  buildWindows(value: object): CarStateEntityBuilder {
    this.result.windows = value;

    return this;
  }

  buildEngineOn(value: boolean = falso.randBoolean()): CarStateEntityBuilder {
    this.result.engineOn = value;

    return this;
  }

  buildCablePlugged(
    value: boolean = falso.randBoolean(),
  ): CarStateEntityBuilder {
    this.result.cablePlugged = value;

    return this;
  }

  buildCharging(value: boolean = falso.randBoolean()): CarStateEntityBuilder {
    this.result.charging = value;

    return this;
  }

  buildStations(value: object): CarStateEntityBuilder {
    this.result.stations = value;

    return this;
  }

  buildProviderPayload(value: object): CarStateEntityBuilder {
    this.result.providerPayload = value;

    return this;
  }

  buildCreatedAt(value: Date): CarStateEntityBuilder {
    this.result.createdAt = value;

    return this;
  }

  buildModifiedAt(value: Date): CarStateEntityBuilder {
    this.result.modifiedAt = value;

    return this;
  }

  build: () => CarStateEntity = () => {
    this.result.id = this.result.id ?? falso.randUuid();
    this.result.carId = this.result.carId ?? falso.randUuid();
    this.result.locked = this.result.locked ?? false;
    this.result.evBatteryLevel =
      this.result.evBatteryLevel ?? falso.randNumber({ max: 100 }).toString();
    this.result.allDoorsWindowsClosed =
      this.result.allDoorsWindowsClosed ?? false;
    this.result.doors = this.result.doors ?? {};
    this.result.windows = this.result.windows ?? {};
    this.result.engineOn = this.result.engineOn ?? false;
    this.result.cablePlugged = this.result.cablePlugged ?? false;
    this.result.charging = this.result.charging ?? false;
    this.result.stations = this.result.stations ?? {};
    this.result.providerPayload = this.result.providerPayload ?? {};
    this.result.createdAt = this.result.createdAt ?? DateTime.now().toJSDate();
    this.result.modifiedAt =
      this.result.modifiedAt ?? DateTime.now().toJSDate();

    return klona(this.result);
  };
}
