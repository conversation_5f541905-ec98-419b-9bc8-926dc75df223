import * as falso from '@ngneat/falso';
import { DateTime } from 'luxon';
import { TokenPayload } from 'src/auth/authentication/token';
import { CarModel } from 'src/car-model/car-model.type';
import { CarModelRepository } from 'src/car-model/entities/car-model.repository';
import { CarState } from 'src/car-state/car-state.type';
import { CarStateRepository } from 'src/car-state/entities/car-state.repository';
import { Car, CarAvailability, CarId } from 'src/car/car.type';
import { CarEntity } from 'src/car/entities/car.entity';
import { CarRepository } from 'src/car/entities/car.repository';
import { getDataSource } from 'src/shared/datasources/datasource';
import { InvocationContext } from 'src/shared/invocation-context';
import { CarModelBuilder } from 'test/car-model/car-model.builder';
import { CarStateEntityBuilder } from 'test/car-state/entities/car-state.entity.builder';
import { CarBuilder } from 'test/car/car.builder';

describe('Car State Repository', () => {
  let carModelRepository: CarModelRepository;
  let carRepository: CarRepository;
  let carStateRepository: CarStateRepository;
  let car: Car;
  let carStates: CarState[];

  const lowerModifiedAt: Date = falso.randPastDate({ years: 2 });
  const upperModifiedAt: Date = falso.randRecentDate({ days: 10 });
  const frontLowerModifiedAt: Date = DateTime.fromJSDate(lowerModifiedAt)
    .minus({ years: 1 })
    .toJSDate(); // Not in the range [lower, upper] of `modifiedAt`

  const mock_carModel: CarModel = new CarModelBuilder().build();

  const mock_carEntity: CarEntity = CarEntity.fromCar(
    new CarBuilder()
      .withCarModelId(mock_carModel.id)
      .withAvailability(CarAvailability.AVAILABLE)
      .build(),
  );

  const mock_carStates: CarState[] = [
    new CarStateEntityBuilder()
      .buildId()
      .buildCarId(mock_carEntity.id)
      .buildLocked()
      .buildEvBatteryLevel()
      .buildAllDoorsWindowsClosed()
      .buildDoors(falso.randJSON())
      .buildWindows(falso.randJSON())
      .buildEngineOn()
      .buildCablePlugged()
      .buildCharging()
      .buildStations(falso.randJSON())
      .buildProviderPayload(falso.randJSON())
      .buildModifiedAt(
        falso.randBetweenDate({ from: lowerModifiedAt, to: upperModifiedAt }),
      )
      .buildCreatedAt(falso.randPastDate())
      .build()
      .toCarState(),

    new CarStateEntityBuilder()
      .buildId()
      .buildCarId(falso.randUuid())
      .buildLocked()
      .buildEvBatteryLevel()
      .buildAllDoorsWindowsClosed()
      .buildDoors(falso.randJSON())
      .buildWindows(falso.randJSON())
      .buildEngineOn()
      .buildCablePlugged()
      .buildCharging()
      .buildStations(falso.randJSON())
      .buildProviderPayload(falso.randJSON())
      .buildModifiedAt(
        falso.randBetweenDate({ from: lowerModifiedAt, to: upperModifiedAt }),
      )
      .buildCreatedAt(falso.randPastDate())
      .build()
      .toCarState(),

    new CarStateEntityBuilder()
      .buildId()
      .buildCarId(falso.randUuid())
      .buildLocked()
      .buildEvBatteryLevel()
      .buildAllDoorsWindowsClosed()
      .buildDoors(falso.randJSON())
      .buildWindows(falso.randJSON())
      .buildEngineOn()
      .buildCablePlugged()
      .buildCharging()
      .buildStations(falso.randJSON())
      .buildProviderPayload(falso.randJSON())
      .buildModifiedAt(
        falso.randBetweenDate({ from: lowerModifiedAt, to: upperModifiedAt }),
      )
      .buildCreatedAt(falso.randPastDate())
      .build()
      .toCarState(),

    new CarStateEntityBuilder()
      .buildId()
      .buildCarId(falso.randUuid())
      .buildLocked()
      .buildEvBatteryLevel()
      .buildAllDoorsWindowsClosed()
      .buildDoors(falso.randJSON())
      .buildWindows(falso.randJSON())
      .buildEngineOn()
      .buildCablePlugged()
      .buildCharging()
      .buildStations(falso.randJSON())
      .buildProviderPayload(falso.randJSON())
      .buildModifiedAt(
        falso.randBetweenDate({ from: lowerModifiedAt, to: upperModifiedAt }),
      )
      .buildCreatedAt(falso.randPastDate())
      .build()
      .toCarState(),

    new CarStateEntityBuilder()
      .buildId()
      .buildCarId(falso.randUuid())
      .buildLocked()
      .buildEvBatteryLevel()
      .buildAllDoorsWindowsClosed()
      .buildDoors(falso.randJSON())
      .buildWindows(falso.randJSON())
      .buildEngineOn()
      .buildCablePlugged()
      .buildCharging()
      .buildStations(falso.randJSON())
      .buildProviderPayload(falso.randJSON())
      .buildCreatedAt(falso.randPastDate())
      .buildModifiedAt(frontLowerModifiedAt)
      .build()
      .toCarState(),
  ];

  beforeAll(async () => {
    console.log('Starting tests');

    InvocationContext.init('fake-request-id', 'fake-correlation-id');
    InvocationContext.setToken({
      payload: {
        userId: 'test-user-id',
      } as TokenPayload,
      signature: 'foobar',
    });

    carModelRepository = new CarModelRepository(await getDataSource());

    carRepository = new CarRepository(await getDataSource());

    carStateRepository = new CarStateRepository(await getDataSource());

    await carRepository.delete({});
    await carModelRepository.delete({});
    await carStateRepository.delete({});

    await carModelRepository.createOrFail(mock_carModel);

    car = await carRepository.insertOne(Car.fromEntity(mock_carEntity));
  });

  it('should insert car states', async () => {
    carStates = await Promise.all(
      mock_carStates.map((carState) => carStateRepository.insertOne(carState)),
    );

    expect(carStates.every((carState) => !!carState.id)).toBe(true);
    expect(
      carStates.every((carState, index) => {
        const mock_carState = mock_carStates[index];

        return JSON.stringify(carState.toJson()).localeCompare(
          JSON.stringify(mock_carState.toJson()),
        );
      }),
    );
  });

  it.each(mock_carStates.map((carState) => carState.id))(
    'should check exists by props, car state id: %s',
    async (id) => {
      const result = await carStateRepository.existsByProps({
        id,
      });

      expect(result).toBe(true);
    },
  );

  it('should find by empty props', async () => {
    const { result, total } = await carStateRepository.findByProps({});

    expect(total).toBe(5);
    expect(result.length).toBe(5);

    expect(result.every((carState) => !!carState)).toBe(true);
  });

  it('should find with skip', async () => {
    const { result, total } = await carStateRepository.findByProps({ skip: 5 });

    expect(total).toBe(5);
    expect(result.length).toBe(0);
  });

  it('should find with take', async () => {
    const { result, total } = await carStateRepository.findByProps({ take: 2 });

    expect(total).toBe(5);
    expect(result.length).toBe(2);
  });

  it.each(mock_carStates.map((carState) => carState.id))(
    'should find by id, car state id: %s',
    async (id) => {
      const { result, total } = await carStateRepository.findByProps({ id });

      expect(total).toBe(1);
      expect(result.length).toBe(1);
    },
  );

  it('should find by car id', async () => {
    const { result, total } = await carStateRepository.findByProps({
      carId: car.id,
    });

    expect(total).toBe(1);
    expect(result.length).toBe(1);
  });

  it('should find by and conditions', async () => {
    const { result, total } = await carStateRepository.findByProps({
      id: carStates[0].id,
      carId: carStates[0].carId,
      skip: 0,
      take: 1,
    });

    expect(total).toBe(1);
    expect(result.length).toBe(1);
  });

  afterAll(async () => {
    await carStateRepository.delete({});
    await carRepository.delete({});
    await carModelRepository.delete({});
    InvocationContext.clear();
  });
});

describe('Car State Repository upsertNewerOnConflictCarId', () => {
  let carModelRepository: CarModelRepository;
  let carRepository: CarRepository;
  let carStateRepository: CarStateRepository;
  let newCarEntity: CarEntity;
  let newCar: Car;
  const mock_carModel: CarModel = new CarModelBuilder().build();

  beforeAll(async () => {
    InvocationContext.init('fake-request-id', 'fake-correlation-id');
    InvocationContext.setToken({
      payload: {
        userId: 'test-user-id',
      } as TokenPayload,
      signature: 'foobar',
    });
    carModelRepository = new CarModelRepository(await getDataSource());
    carRepository = new CarRepository(await getDataSource());
    carStateRepository = new CarStateRepository(await getDataSource());

    await carModelRepository.createOrFail(mock_carModel);

    newCarEntity = CarEntity.fromCar(
      new CarBuilder().withCarModelId(mock_carModel.id).build(),
    );

    newCar = await carRepository.insertOne(Car.fromEntity(newCarEntity));
  });

  afterAll(async () => {
    await carStateRepository.delete({});
    await carRepository.delete({});
    await carModelRepository.delete({});
    InvocationContext.clear();
  });

  it('should insert when car state with carId does not exist', async () => {
    // Create a new car state
    const now = DateTime.now();
    const newCarState = new CarStateEntityBuilder()
      .buildId()
      .buildCarId(newCar.id.value)
      .buildLocked()
      .buildEvBatteryLevel(80)
      .buildAllDoorsWindowsClosed(true)
      .buildDoors({ frontLeftClosed: true, frontRightClosed: true })
      .buildWindows({ frontLeftClosed: true, frontRightClosed: true })
      .buildEngineOn(false)
      .buildCablePlugged(false)
      .buildCharging(false)
      .buildStations({ current: [] })
      .buildProviderPayload({ source: 'test' })
      .buildModifiedAt(now.toJSDate())
      .buildCreatedAt(now.toJSDate())
      .build()
      .toCarState();

    // Upsert the car state
    const result =
      await carStateRepository.upsertNewerOnConflictCarId(newCarState);

    expect(result).toBeDefined();
    expect(result.carId.value).toBe(newCar.id.value);
    expect(result.batteryLevel.value).toBe(80);
    expect(result.modifiedAt.toMillis()).toEqual(now.toMillis());

    const exists = await carStateRepository.existsByProps({ id: result.id });
    expect(exists).toBe(true);

    return result;
  });

  it('should update when existing car state has older modifiedAt', async () => {
    // Get the existing car state
    const { result: existingStates } = await carStateRepository.findByProps({
      carId: newCar.id,
    });
    expect(existingStates.length).toBe(1);
    const existingState = existingStates[0];

    // Create updated car state with newer modifiedAt
    const newerTimestamp = DateTime.fromJSDate(
      existingState.modifiedAt.toJSDate(),
    ).plus({ hours: 1 });
    const updatedCarState = new CarStateEntityBuilder()
      .buildId(existingState.id.value)
      .buildCarId(newCar.id.value)
      .buildLocked(false) // Changed value
      .buildEvBatteryLevel(90) // Changed value
      .buildAllDoorsWindowsClosed(true)
      .buildDoors({ frontLeftClosed: true, frontRightClosed: true })
      .buildWindows({ frontLeftClosed: true, frontRightClosed: true })
      .buildEngineOn(false)
      .buildCablePlugged(false)
      .buildCharging(false)
      .buildStations({ current: [] })
      .buildProviderPayload({ source: 'test-updated' })
      .buildModifiedAt(newerTimestamp.toJSDate())
      .buildCreatedAt(existingState.createdAt.toJSDate())
      .build()
      .toCarState();

    // Upsert the car state
    const result =
      await carStateRepository.upsertNewerOnConflictCarId(updatedCarState);

    // Verify the car state was updated
    expect(result).toBeDefined();
    expect(result.id.value).toBe(existingState.id.value);
    expect(result.carId.value).toBe(newCar.id.value);
    expect(result.batteryLevel.value).toBe(90); // Should be updated value
    expect(result.locked).toBe(false); // Should be updated value
    expect(result.modifiedAt.toISO()).toBe(newerTimestamp.toISO());

    return { existingState, updatedState: result };
  });

  it('should ignore when existing car state has newer modifiedAt', async () => {
    // Get the existing car state
    const { result: existingStates } = await carStateRepository.findByProps({
      carId: newCar.id,
    });
    expect(existingStates.length).toBe(1);
    const existingState = existingStates[0];

    // Create outdated car state with older modifiedAt
    const olderTimestamp = DateTime.fromJSDate(
      existingState.modifiedAt.toJSDate(),
    ).minus({ hours: 1 });
    const outdatedCarState = new CarStateEntityBuilder()
      .buildId(existingState.id.value)
      .buildCarId(newCar.id.value)
      .buildLocked(true) // Changed value, but should be ignored
      .buildEvBatteryLevel(50) // Changed value, but should be ignored
      .buildAllDoorsWindowsClosed(true)
      .buildDoors({ frontLeftClosed: true, frontRightClosed: true })
      .buildWindows({ frontLeftClosed: true, frontRightClosed: true })
      .buildEngineOn(false)
      .buildCablePlugged(false)
      .buildCharging(false)
      .buildStations({ current: [] })
      .buildProviderPayload({ source: 'test-outdated' })
      .buildModifiedAt(olderTimestamp.toJSDate())
      .buildCreatedAt(existingState.createdAt.toJSDate())
      .build()
      .toCarState();

    // Upsert the car state
    const result =
      await carStateRepository.upsertNewerOnConflictCarId(outdatedCarState);

    // Verify the car state was not updated (should maintain the existing values)
    expect(result).toBeDefined();
    expect(result.id.value).toBe(existingState.id.value);
    expect(result.carId.value).toBe(newCar.id.value);
    expect(result.modifiedAt.toISO()).toBe(existingState.modifiedAt.toISO()); // Should remain unchanged
    expect(result.batteryLevel.value).toBe(existingState.batteryLevel.value); // Should remain unchanged
    expect(result.locked).toBe(existingState.locked); // Should remain unchanged
  });
});

describe('Car State Repository updateCarStateLockStatus', () => {
  let carModelRepository: CarModelRepository;
  let carRepository: CarRepository;
  let carStateRepository: CarStateRepository;
  let testCar: Car;
  let initialCarState: CarState;
  const mock_carModel: CarModel = new CarModelBuilder().build();

  beforeAll(async () => {
    InvocationContext.init('fake-request-id', 'fake-correlation-id');
    InvocationContext.setToken({
      payload: {
        userId: 'test-user-id',
      } as TokenPayload,
      signature: 'foobar',
    });
    carModelRepository = new CarModelRepository(await getDataSource());
    carRepository = new CarRepository(await getDataSource());
    carStateRepository = new CarStateRepository(await getDataSource());

    await carModelRepository.createOrFail(mock_carModel);

    const testCarEntity = CarEntity.fromCar(
      new CarBuilder().withCarModelId(mock_carModel.id).build(),
    );

    testCar = await carRepository.insertOne(Car.fromEntity(testCarEntity));

    const initialCarStateEntity = new CarStateEntityBuilder()
      .buildId()
      .buildCarId(testCar.id.value)
      .buildLocked(true)
      .buildEvBatteryLevel(75)
      .buildAllDoorsWindowsClosed(true)
      .buildDoors({ frontLeftClosed: true, frontRightClosed: true })
      .buildWindows({ frontLeftClosed: true, frontRightClosed: true })
      .buildEngineOn(false)
      .buildCablePlugged(false)
      .buildCharging(false)
      .buildStations({ current: [] })
      .buildProviderPayload({ source: 'test' })
      .buildModifiedAt(DateTime.now().minus({ minutes: 30 }).toJSDate())
      .buildCreatedAt(DateTime.now().minus({ minutes: 30 }).toJSDate())
      .build()
      .toCarState();

    initialCarState = await carStateRepository.insertOne(initialCarStateEntity);
  });

  afterAll(async () => {
    await carStateRepository.delete({});
    await carRepository.delete({});
    await carModelRepository.delete({});
    InvocationContext.clear();
  });

  it('should update car state lock status when existing state has older modified date', async () => {
    const newerModifiedDate = DateTime.fromJSDate(
      initialCarState.modifiedAt.toJSDate(),
    ).plus({ minutes: 5 });

    await carStateRepository.updateCarStateLockStatus(
      testCar.id,
      newerModifiedDate,
      false,
    );

    const { result: updatedStates } = await carStateRepository.findByProps({
      carId: testCar.id,
    });
    expect(updatedStates.length).toBe(1);
    expect(updatedStates[0].locked).toBe(false);
    expect(updatedStates[0].modifiedAt.toISO()).toBe(newerModifiedDate.toISO());
  });

  it('should not update car state lock status when existing state has newer modified date', async () => {
    const { result: existingStates } = await carStateRepository.findByProps({
      carId: testCar.id,
    });
    const existingState = existingStates[0];

    const olderModifiedDate = DateTime.fromJSDate(
      existingState.modifiedAt.toJSDate(),
    ).minus({ minutes: 5 });

    await carStateRepository.updateCarStateLockStatus(
      testCar.id,
      olderModifiedDate,
      true,
    );

    const { result: updatedStates } = await carStateRepository.findByProps({
      carId: testCar.id,
    });
    expect(updatedStates.length).toBe(1);
    expect(updatedStates[0].locked).toBe(false);
    expect(updatedStates[0].modifiedAt.toISO()).toBe(
      existingState.modifiedAt.toISO(),
    );
  });

  it('should not affect other car state when car state does not exist', async () => {
    const nonExistentCarId = new CarId(falso.randUuid());

    const modifiedDate = DateTime.now();

    await carStateRepository.updateCarStateLockStatus(
      nonExistentCarId,
      modifiedDate,
      true,
    );

    const { result: existingStates } = await carStateRepository.findByProps({
      carId: testCar.id,
    });
    expect(existingStates.length).toBe(1);

    const { result: nonExistentStates } = await carStateRepository.findByProps({
      carId: nonExistentCarId,
    });
    expect(nonExistentStates.length).toBe(0);
  });
});
