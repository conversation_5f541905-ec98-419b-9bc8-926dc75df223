import { jest } from '@jest/globals';
import * as falso from '@ngneat/falso';
import { CarAvailabilityService } from 'src/car-availability/car-availability.service';
import {
  CarAvailabilityMap,
  CarAvailabilityRecord,
} from 'src/car-availability/car-availability.type';
import { CarAvailabilityQuery } from 'src/car-availability/dto/car-availability.query';
import { CarModel } from 'src/car-model/car-model.type';
import { CarAvailability } from 'src/car/car.type';
import { InvocationContext } from 'src/shared/invocation-context';
import { CarBuilder } from 'test/car/car.builder';

describe('CarService', () => {
  async function cleanUp() {
    jest.resetAllMocks();
  }

  InvocationContext.init('fake-request-id', 'fake-correlation-id');

  const mock_carAvailabilityRepository = {
    getCarAvailability: jest.fn(),
  };

  const mock_carRepository = {
    existsBy: jest.fn(),
    updateAvailability: jest.fn(),
    updateAvailabilityAndLocationAsOf: jest.fn(),
    switchCarAvailability: jest.fn(),
  };

  const mock_eventPublisher = {
    send: jest.fn(),
  };

  const mock_featureFlag = {
    getEnableCsCarAvailabilityUpdatedEvent: jest.fn(),
  };

  const carService = new CarAvailabilityService(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mock_carAvailabilityRepository as any,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mock_carRepository as any,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mock_eventPublisher as any,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mock_featureFlag as any,
  );

  it('should get all cars when with empty body - get available cars', async () => {
    const car1 = new CarBuilder().build();
    const car2 = new CarBuilder().build();

    car1.availability = CarAvailability.AVAILABLE;
    car2.availability = CarAvailability.RESERVED;

    const mockCarModel1 = {
      id: { value: falso.randUuid() },
      name: { value: 'Model1' },
      toJson: () => ({ id: falso.randUuid(), name: 'Model1' }),
    } as unknown as CarModel;

    const mockCarModel2 = {
      id: { value: falso.randUuid() },
      name: { value: 'Model2' },
      toJson: () => ({ id: falso.randUuid(), name: 'Model2' }),
    } as unknown as CarModel;

    const car1WithCarModel = Object.assign({}, car1, {
      car_model: mockCarModel1,
    });
    const car2WithCarModel = Object.assign({}, car2, {
      car_model: mockCarModel2,
    });
    const query = new CarAvailabilityQuery({});

    const mockReturnData: CarAvailabilityRecord[] = [
      {
        carModelName: car1WithCarModel.car_model.name.value,
        stationId: car1WithCarModel.stationId,
        availableCount: '1',
        reservedCount: '0',
      },
      {
        carModelName: car2WithCarModel.car_model.name.value,
        stationId: car2WithCarModel.stationId,
        availableCount: '0',
        reservedCount: '1',
      },
    ];
    mock_carAvailabilityRepository.getCarAvailability.mockResolvedValueOnce(
      mockReturnData as never,
    );
    const result: CarAvailabilityMap =
      await carService.getCarAvailability(query);

    expect(car1.stationId in result).toBe(true);
    expect(car2.stationId in result).toBe(true);
  });

  it('should get all cars when with default minBatteryLevel - get available cars', async () => {
    const car1 = new CarBuilder().build();
    const car2 = new CarBuilder().build();

    car1.availability = CarAvailability.AVAILABLE;
    car2.availability = CarAvailability.RESERVED;

    const mockCarModel1 = {
      id: { value: falso.randUuid() },
      name: { value: 'Model1' },
      toJson: () => ({ id: falso.randUuid(), name: 'Model1' }),
    } as unknown as CarModel;

    const mockCarModel2 = {
      id: { value: falso.randUuid() },
      name: { value: 'Model2' },
      toJson: () => ({ id: falso.randUuid(), name: 'Model2' }),
    } as unknown as CarModel;

    const car1WithCarModel = Object.assign({}, car1, {
      car_model: mockCarModel1,
    });
    const car2WithCarModel = Object.assign({}, car2, {
      car_model: mockCarModel2,
    });

    const query = new CarAvailabilityQuery({
      stationIds: [car1WithCarModel.stationId, car2WithCarModel.stationId],
      default: {
        minBatteryLevel: 75,
      },
    });

    const mockReturnData: CarAvailabilityRecord[] = [
      {
        carModelName: car1WithCarModel.car_model.name.value,
        stationId: car1WithCarModel.stationId,
        availableCount: '1',
        reservedCount: '0',
      },
      {
        carModelName: car2WithCarModel.car_model.name.value,
        stationId: car2WithCarModel.stationId,
        availableCount: '0',
        reservedCount: '0',
      },
    ];
    mock_carAvailabilityRepository.getCarAvailability.mockResolvedValueOnce(
      mockReturnData as never,
    );
    const result: CarAvailabilityMap =
      await carService.getCarAvailability(query);

    expect(car1.stationId in result).toBe(true);
    expect(car2.stationId in result).toBe(true);
    expect(
      result[car1.stationId]['cars'][car1WithCarModel.car_model.name.value]
        .available,
    ).toBe(1);
    expect(
      result[car1.stationId]['cars'][car1WithCarModel.car_model.name.value]
        .reserved,
    ).toBe(0);
    expect(
      result[car2.stationId]['cars'][car2WithCarModel.car_model.name.value]
        .available,
    ).toBe(0);
    expect(
      result[car2.stationId]['cars'][car2WithCarModel.car_model.name.value]
        .reserved,
    ).toBe(0);
  });

  it('should get all cars when with specific carModelName minBatteryLevel - get available cars', async () => {
    const car1 = new CarBuilder().build();
    const car2 = new CarBuilder().build();

    car1.availability = CarAvailability.AVAILABLE;
    car2.availability = CarAvailability.RESERVED;

    const mockCarModel1 = {
      id: { value: falso.randUuid() },
      name: { value: 'Model1' },
      toJson: () => ({ id: falso.randUuid(), name: 'Model1' }),
    } as unknown as CarModel;

    const mockCarModel2 = {
      id: { value: falso.randUuid() },
      name: { value: 'Model2' },
      toJson: () => ({ id: falso.randUuid(), name: 'Model2' }),
    } as unknown as CarModel;

    const car1WithCarModel = Object.assign({}, car1, {
      car_model: mockCarModel1,
    });
    const car2WithCarModel = Object.assign({}, car2, {
      car_model: mockCarModel2,
    });

    const query = new CarAvailabilityQuery({
      stationIds: [car1WithCarModel.stationId, car2WithCarModel.stationId],
      carModels: {
        [car1WithCarModel.car_model.name.value]: {
          minBatteryLevel: 50,
        },
      },
    });

    const mockReturnData: CarAvailabilityRecord[] = [
      {
        carModelName: car1WithCarModel.car_model.name.value,
        stationId: car1WithCarModel.stationId,
        availableCount: '1',
        reservedCount: '0',
      },
    ];
    mock_carAvailabilityRepository.getCarAvailability.mockResolvedValueOnce(
      mockReturnData as never,
    );
    const result: CarAvailabilityMap =
      await carService.getCarAvailability(query);

    expect(car1.stationId in result).toBe(true);
    expect(car2.stationId in result).toBe(false);
    expect(
      result[car1.stationId]['cars'][car1WithCarModel.car_model.name.value]
        .available,
    ).toBe(1);
    expect(
      result[car1.stationId]['cars'][car1WithCarModel.car_model.name.value]
        .reserved,
    ).toBe(0);
  });

  afterAll(async () => {
    await cleanUp();
  });
});
