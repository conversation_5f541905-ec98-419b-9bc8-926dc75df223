import { jest } from '@jest/globals';
import * as falso from '@ngneat/falso';
import { status as HttpStatus } from 'http-status';
import { DateTime } from 'luxon';
import { CarModelId } from 'src/car-model/car-model.type';
import { CarModelNotFoundError } from 'src/car-model/errors/car-model.error';
import { CarController } from 'src/car/car.controller';
import { CarId, CarPlateNumber, CarProvider, CarVin } from 'src/car/car.type';
import { CreateCarRequestDto } from 'src/car/dtos/admin/v1/create-car.request.dto';
import { StandardResponseBody } from 'src/shared/http-response';
import { InvocationContext } from 'src/shared/invocation-context';

describe('CarController', () => {
  async function cleanUp() {
    jest.resetAllMocks();
  }

  beforeAll(() => {
    InvocationContext.init('fake-request-id', 'fake-correlation-id');
  });

  const mock_carService = {
    createCar: jest.fn(),
    getAvailableCars: jest.fn(),
  };
  const mock_carModelService = { existsById: jest.fn() };
  const mock_vulogCarStateService = { getCarState: jest.fn() };

  const carController = new CarController(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mock_carService as any,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    {} as any,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mock_carModelService as any,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mock_vulogCarStateService as any,
  );

  it('should create car', async () => {
    const createCarRequest = new CreateCarRequestDto({
      carModelId: falso.randUuid(),
      plateNumber: falso.randText(),
      vin: falso.randText(),
      registrationDate: DateTime.now().toISO(),
    });

    const carId = falso.randUuid();

    mock_carModelService.existsById.mockResolvedValueOnce(true as never);

    mock_carService.createCar.mockResolvedValueOnce({
      id: new CarId(carId),
      carModelId: new CarModelId(createCarRequest.carModelId),
      plateNumber: new CarPlateNumber(createCarRequest.plateNumber),
      vin: new CarVin(createCarRequest.vin),
      registrationDate: DateTime.fromISO(createCarRequest.registrationDate),
      managedBy: CarProvider.VULOG,
      externalFleetId: 'fleetId1',
      externalCarId: 'carId1',
    } as never);

    const result = (
      await carController.createCar(createCarRequest)
    ).toGatewayResult();

    expect(result).toEqual({
      statusCode: HttpStatus.CREATED,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(
        new StandardResponseBody({
          id: carId,
          carModelId: createCarRequest.carModelId,
          plateNumber: createCarRequest.plateNumber,
          vin: createCarRequest.vin,
          registrationDate: createCarRequest.registrationDate,
          managedBy: CarProvider.VULOG,
          externalFleetId: 'fleetId1',
          externalCarId: 'carId1',
        }),
      ),
    });
  });

  it('should throw error car model not found', async () => {
    const createCarRequest = new CreateCarRequestDto({
      carModelId: falso.randUuid(),
      plateNumber: falso.randText(),
      vin: falso.randText(),
      registrationDate: DateTime.now().toISO(),
      managedBy: CarProvider.VULOG,
      externalFleetId: 'fleetId1',
      externalCarId: 'carId1',
    });

    mock_carModelService.existsById.mockResolvedValueOnce(null as never);

    try {
      await carController.createCar(createCarRequest);
    } catch (error) {
      if (error instanceof CarModelNotFoundError) {
        expect(error.message).toEqual(
          `The car model ${createCarRequest.carModelId} could not be found`,
        );

        expect(error.statusCode).toEqual(HttpStatus.NOT_FOUND);
        return;
      }
    }

    fail('should have thrown CarModelNotFoundError');
  });

  afterAll(async () => {
    await cleanUp();
    InvocationContext.clear();
  });
});
