import { jest } from '@jest/globals';
import * as falso from '@ngneat/falso';
import { status as HttpStatus } from 'http-status';
import { CarState } from 'src/car-state/car-state.type';
import { CarService } from 'src/car/car.service';
import { CarProvider } from 'src/car/car.type';
import { CarQuery } from 'src/car/dtos/admin/v1/car.query';
import { CarAlreadyExistsError } from 'src/car/errors/car.error';
import { InvocationContext } from 'src/shared/invocation-context';
import { Page } from 'src/shared/query/pagination';
import { CarBuilder } from 'test/car/car.builder';

describe('CarService', () => {
  async function cleanUp() {
    jest.resetAllMocks();
  }

  InvocationContext.init('fake-request-id', 'fake-correlation-id');

  const mock_carRepository = {
    existsByProps: jest.fn(),
    insertOne: jest.fn(),
    findByProps: jest.fn(),
    getCarAvailability: jest.fn(),
  };

  const mock_eventProducer = { send: jest.fn() };

  const mock_featureFlag = {};
  const carService = new CarService(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mock_carRepository as any,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mock_eventProducer as any,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mock_featureFlag as any,
  );

  it('should create car not existing', async () => {
    const car = new CarBuilder().build();

    mock_carRepository.existsByProps.mockResolvedValueOnce(null as never);

    mock_carRepository.insertOne.mockResolvedValueOnce(
      new CarBuilder()
        .withId(car.id.value)
        .withCarModelId(car.carModelId)
        .withPlateNumber(car.plateNumber.value)
        .withVin(car.vin.value)
        .withRegistrationDate(car.registrationDate)
        .withManagedBy(CarProvider.VULOG)
        .withExternalFleetId(car.externalFleetId)
        .withExternalCarId(car.externalCarId)
        .build() as never,
    );

    mock_eventProducer.send.mockResolvedValueOnce(undefined as never);

    const result = await carService.createCar(car);

    expect(result.id.value).toEqual(car.id.value);
    expect(result.carModelId.value).toEqual(car.carModelId.value);
    expect(result.plateNumber.value).toEqual(car.plateNumber.value);
    expect(result.vin.value).toEqual(car.vin.value);
    expect(result.registrationDate.toISO()).toEqual(
      car.registrationDate.toISO(),
    );
    expect(result.createdAt.toISO()).toBeDefined();
    expect(result.modifiedAt.toISO()).toBeDefined();
    expect(result.managedBy).toEqual(CarProvider.VULOG);
    expect(result.externalFleetId).toEqual(car.externalFleetId);
    expect(result.externalCarId).toEqual(car.externalCarId);
  });

  it('should not create existing car', async () => {
    const car = new CarBuilder().build();

    mock_carRepository.existsByProps.mockResolvedValueOnce(car as never);

    mock_carRepository.insertOne.mockResolvedValueOnce(car as never);

    mock_eventProducer.send.mockResolvedValueOnce(undefined as never);

    try {
      await carService.createCar(car);
    } catch (error) {
      if (error instanceof CarAlreadyExistsError) {
        expect(error.statusCode).toEqual(HttpStatus.CONFLICT);
        expect(error.message).toEqual(
          `Car ${car.id.value} is already existing`,
        );
        return;
      }
    }

    fail('should have thrown CarAlreadyExistsError');
  });

  it('should get cars without state information when includeState is false', async () => {
    const car1 = new CarBuilder().build();

    const car2 = new CarBuilder().build();

    const cars = [car1, car2];
    const total = cars.length;

    // Create query with includeState set to false
    const query = new CarQuery({
      page: 1,
      nbPerPage: 10,
      'include-state': 'false',
    });

    // Mock the repository response
    mock_carRepository.findByProps.mockResolvedValueOnce({
      result: cars,
      total,
    } as never);

    // Execute
    const result = await carService.getByQuery(query);

    // Verify
    expect(result).toBeInstanceOf(Page);
    expect(result.items.length).toBe(2);
    expect(result.pagination.total).toBe(2);

    // Make sure includeState was passed to the repository
    expect(mock_carRepository.findByProps).toHaveBeenCalledWith(
      expect.objectContaining({
        includeState: false,
      }),
    );

    // Check that none of the cars have state information
    expect(result.items[0].state).toBeUndefined();
    expect(result.items[1].state).toBeUndefined();
  });

  it('should get cars with state information when includeState is true', async () => {
    const car1 = new CarBuilder().build();

    const car2 = new CarBuilder().build();

    // Add state to the cars
    const mockState1 = {
      id: { value: falso.randUuid() },
      carId: car1.id,
      batteryLevel: { value: 75 },
      locked: true,
      toJson: () => ({ id: falso.randUuid(), batteryLevel: 75, locked: true }),
    } as unknown as CarState;

    const mockState2 = {
      id: { value: falso.randUuid() },
      carId: car2.id,
      batteryLevel: { value: 50 },
      locked: false,
      toJson: () => ({ id: falso.randUuid(), batteryLevel: 50, locked: false }),
    } as unknown as CarState;

    const car1WithState = Object.assign({}, car1, { state: mockState1 });
    const car2WithState = Object.assign({}, car2, { state: mockState2 });

    const cars = [car1WithState, car2WithState];
    const total = cars.length;

    // Create query with includeState set to true
    const query = new CarQuery({
      page: 1,
      nbPerPage: 10,
      'include-state': 'true',
    });

    // Mock the repository response
    mock_carRepository.findByProps.mockResolvedValueOnce({
      result: cars,
      total,
    } as never);

    // Execute
    const result = await carService.getByQuery(query);

    // Verify
    expect(result).toBeInstanceOf(Page);
    expect(result.items.length).toBe(2);
    expect(result.pagination.total).toBe(2);

    // Make sure includeState was passed to the repository
    expect(mock_carRepository.findByProps).toHaveBeenCalledWith(
      expect.objectContaining({
        includeState: true,
      }),
    );

    // Check that the cars have state information
    expect(result.items[0].state).toBeDefined();
    expect(result.items[0].state).toEqual(mockState1);
    expect(result.items[1].state).toBeDefined();
    expect(result.items[1].state).toEqual(mockState2);
  });

  afterAll(async () => {
    await cleanUp();
  });
});
