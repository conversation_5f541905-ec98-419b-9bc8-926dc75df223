import * as falso from '@ngneat/falso';
import { DateTime } from 'luxon';
import { randomUUID } from 'node:crypto';
import { CarModel, CarModelId } from 'src/car-model/car-model.type';
import { CarState } from 'src/car-state/car-state.type';
import {
  Car,
  CarAvailability,
  CarId,
  CarPlateNumber,
  CarProvider,
  CarVin,
} from 'src/car/car.type';
import { AdminUuid } from 'src/shared/shared.type';
import { MockBuilder } from 'test/shared/mock.builder';
import { aString, aUuid } from 'test/shared/random-data';

export class CarBuilder extends MockBuilder<Car> {
  protected result: Car = new Car(
    undefined,
    new CarModelId(aUuid()),
    undefined,
    new CarPlateNumber(aString()),
    new CarVin(aUuid()),
    DateTime.fromJSDate(
      falso.randPastDate({
        years: 1,
      }),
    ),
    CarProvider.VULOG,
    falso.randText(),
    falso.randText(),
    falso.randUuid(),
    DateTime.now(),
    CarAvailability.AVAILABLE,
    undefined,
    DateTime.now(),
    DateTime.now(),
    DateTime.now(),
    new AdminUuid(falso.randUuid()),
    new AdminUuid(falso.randUuid()),
    new AdminUuid(falso.randUuid()),
    undefined,
  );

  withId(value?: string): CarBuilder {
    this.result.id = new CarId(value || randomUUID());
    return this;
  }

  withCarModelId(value?: CarModelId): CarBuilder {
    this.result.carModelId = value || new CarModelId(randomUUID());
    return this;
  }

  withCarModel(carModel?: CarModel): CarBuilder {
    this.result.carModel = carModel;
    return this;
  }

  withPlateNumber(value?: string): CarBuilder {
    this.result.plateNumber = new CarPlateNumber(value || randomUUID());
    return this;
  }

  withVin(value?: string): CarBuilder {
    this.result.vin = new CarVin(value || randomUUID());
    return this;
  }

  withRegistrationDate(value?: DateTime): CarBuilder {
    this.result.registrationDate = value || DateTime.now();
    return this;
  }

  withManagedBy(value?: CarProvider): CarBuilder {
    this.result.managedBy = value;
    return this;
  }

  withExternalFleetId(value?: string): CarBuilder {
    this.result.externalFleetId = value;
    return this;
  }

  withExternalCarId(value?: string): CarBuilder {
    this.result.externalCarId = value;
    return this;
  }

  withStationId(value?: string): CarBuilder {
    this.result.stationId = value;
    return this;
  }

  withLocatedAt(value?: DateTime): CarBuilder {
    this.result.locatedAt = value;
    return this;
  }

  withAvailability(value?: CarAvailability): CarBuilder {
    this.result.availability = value;
    return this;
  }

  withAvailabilityUpdatedAt(value?: DateTime): CarBuilder {
    this.result.availabilityUpdatedAt = value;
    return this;
  }

  withCreatedAt(value?: DateTime): CarBuilder {
    this.result.createdAt = value || DateTime.now();
    return this;
  }

  withModifiedAt(value?: DateTime): CarBuilder {
    this.result.modifiedAt = value;
    return this;
  }

  withDeletedAt(value?: DateTime): CarBuilder {
    this.result.deletedAt = value;
    return this;
  }

  withCreatedBy(value?: string): CarBuilder {
    this.result.createdBy = value ? new AdminUuid(value) : undefined;
    return this;
  }

  withModifiedBy(value?: string): CarBuilder {
    this.result.modifiedBy = value ? new AdminUuid(value) : undefined;
    return this;
  }

  withDeletedBy(value?: string): CarBuilder {
    this.result.deletedBy = value ? new AdminUuid(value) : undefined;
    return this;
  }

  withCarState(carState: CarState): CarBuilder {
    this.result.state = carState;
    return this;
  }

  build: () => Car = () => {
    return this.result;
  };
}
