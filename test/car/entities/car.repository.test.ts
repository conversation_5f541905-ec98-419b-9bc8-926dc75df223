import { jest } from '@jest/globals';
import * as falso from '@ngneat/falso';
import { randomUUID } from 'crypto';
import { DateTime } from 'luxon';
import { TokenPayload } from 'src/auth/authentication/token';
import { CarModel, CarModelId } from 'src/car-model/car-model.type';
import { CarModelRepository } from 'src/car-model/entities/car-model.repository';
import {
  Car,
  CarAvailability,
  CarId,
  CarPlateNumber,
  CarProvider,
  CarVin,
  StationId,
} from 'src/car/car.type';
import { CarRepository } from 'src/car/entities/car.repository';
import {
  CarAvailabilityNotUpdatedError,
  CarAvailabilityTransitionForbidden,
  CarAvailabilityUpdateAvailabilityTooOldError,
  CarLocationNotUpdatedError,
  CarLocationUpdateLocationTooOldError,
  CarNotFoundError,
  CarUpdateRequestOutdatedError,
} from 'src/car/errors/car.error';
import { getDataSource } from 'src/shared/datasources/datasource';
import { InvocationContext } from 'src/shared/invocation-context';
import { CarModelBuilder } from 'test/car-model/car-model.builder';
import { CarBuilder } from 'test/car/car.builder';

jest.setTimeout(100000);

describe('Car Repository', () => {
  let carModelRepository: CarModelRepository;
  let carRepository: CarRepository;
  let carModel: CarModel;
  let cars: Car[];
  const stationId1 = randomUUID().toString();
  const stationId2 = randomUUID().toString();

  beforeAll(async () => {
    console.log('Starting tests');

    InvocationContext.init('fake-request-id', 'fake-correlation-id');
    InvocationContext.setToken({
      payload: {
        userId: 'test-user-id',
      } as TokenPayload,
      signature: 'foobar',
    });

    carRepository = new CarRepository(await getDataSource());

    carModelRepository = new CarModelRepository(await getDataSource());
    await carRepository.delete({});
    await carModelRepository.delete({});
    carModel = await carModelRepository.createOrFail(
      new CarModelBuilder().build(),
    );
  });

  it('should insert cars', async () => {
    cars = [
      new CarBuilder()
        .withCarModelId(carModel.id)
        .withCarModel(carModel)
        .withPlateNumber('plateNumber1')
        .withVin('vin1')
        .withExternalFleetId('fleetId1')
        .withExternalCarId('carId1')
        .withAvailability(CarAvailability.OUT_OF_SERVICE)
        .withStationId(stationId1)
        .build(),
      new CarBuilder()
        .withCarModelId(carModel.id)
        .withCarModel(carModel)
        .withPlateNumber('plateNumber2')
        .withVin('vin2')
        .withExternalFleetId('fleetId2')
        .withExternalCarId('carId2')
        .withAvailability(CarAvailability.AVAILABLE)
        .withStationId(stationId1)
        .build(),
      new CarBuilder()
        .withCarModelId(carModel.id)
        .withCarModel(carModel)
        .withPlateNumber('plateNumber3')
        .withVin('vin3')
        .withExternalFleetId('fleetId3')
        .withExternalCarId('carId3')
        .withAvailability(CarAvailability.AVAILABLE)
        .withStationId(stationId2)
        .build(),
      new CarBuilder()
        .withCarModelId(carModel.id)
        .withCarModel(carModel)
        .withPlateNumber('plateNumber4')
        .withVin('vin4')
        .withExternalFleetId('fleetId4')
        .withExternalCarId('carId4')
        .withAvailability(CarAvailability.ON_RENTAL)
        .build(),
    ];

    cars = await Promise.all(cars.map((car) => carRepository.insertOne(car)));

    expect(cars.every((car) => car.id)).toBe(true);
    expect(cars.every((car) => car.createdBy.value === 'test-user-id')).toBe(
      true,
    );
    expect(cars.every((car) => car.managedBy === CarProvider.VULOG)).toBe(true);
    const regexCarId = /^carId\d+$/;
    const regexFleetId = /^fleetId\d+$/;
    expect(cars.every((car) => regexFleetId.test(car.externalFleetId))).toBe(
      true,
    );
    expect(cars.every((car) => regexCarId.test(car.externalCarId))).toBe(true);
  });

  it('should check exists by props', async () => {
    let result = await carRepository.existsByProps({
      id: cars[0].id,
    });

    expect(result).toBe(true);

    result = await carRepository.existsByProps({
      plateNumber: new CarPlateNumber('plateNumber3'),
    });

    expect(result).toBe(true);

    result = await carRepository.existsByProps({ vin: new CarVin('vin4') });

    expect(result).toBe(true);

    result = await carRepository.existsByProps({
      id: cars[0].id,
      plateNumber: new CarPlateNumber('plateNumber3'),
      vin: new CarVin('vin4'),
    });

    expect(result).toBe(true);

    result = await carRepository.existsByProps({});

    expect(result).toBe(false);

    expect(result).toBe(false);
  });

  it('should find by empty props', async () => {
    const { result, total } = await carRepository.findByProps({});

    expect(total).toBe(4);
    expect(result.length).toBe(4);

    expect(result.every((car) => !!car.carModel)).toBe(true);
  });

  it('should find with skip', async () => {
    const { result, total } = await carRepository.findByProps({ skip: 4 });

    expect(total).toBe(4);
    expect(result.length).toBe(0);
  });

  it('should find with take', async () => {
    const { result, total } = await carRepository.findByProps({ take: 2 });

    expect(total).toBe(4);
    expect(result.length).toBe(2);
  });

  it('should find by car model id', async () => {
    let { result, total } = await carRepository.findByProps({
      carModelId: carModel.id,
    });

    expect(total).toBe(4);
    expect(result.length).toBe(4);

    ({ result, total } = await carRepository.findByProps({
      carModelId: new CarModelId(randomUUID().toString()),
    }));

    expect(total).toBe(0);
    expect(result.length).toBe(0);
  });

  it('should find by plate number', async () => {
    let { result, total } = await carRepository.findByProps({
      plateNumber: new CarPlateNumber('plateNumber'),
    });

    expect(total).toBe(4);
    expect(result.length).toBe(4);

    ({ result, total } = await carRepository.findByProps({
      plateNumber: new CarPlateNumber('plateNumber2'),
    }));

    expect(total).toBe(1);
    expect(result.length).toBe(1);
    expect(result[0].plateNumber.value).toEqual('plateNumber2');
  });

  it('should find by vin', async () => {
    let { result, total } = await carRepository.findByProps({
      vin: new CarVin('vin'),
    });

    expect(total).toBe(4);
    expect(result.length).toBe(4);

    ({ result, total } = await carRepository.findByProps({
      vin: new CarVin('vin3'),
    }));

    expect(total).toBe(1);
    expect(result.length).toBe(1);
    expect(result[0].vin.value).toEqual('vin3');
  });

  it('should find by availability', async () => {
    let { result, total } = await carRepository.findByProps({
      availability: CarAvailability.AVAILABLE,
    });

    expect(total).toBe(2);
    expect(result.length).toBe(2);

    ({ result, total } = await carRepository.findByProps({
      availability: CarAvailability.OUT_OF_SERVICE,
    }));

    expect(total).toBe(1);
    expect(result.length).toBe(1);
    expect(result[0].vin.value).toEqual('vin1');
  });

  it('should find by stationId', async () => {
    let { result, total } = await carRepository.findByProps({
      stationId: new StationId(stationId1),
    });

    expect(total).toBe(2);
    expect(result.length).toBe(2);

    ({ result, total } = await carRepository.findByProps({
      stationId: new StationId(stationId2),
    }));

    expect(total).toBe(1);
    expect(result.length).toBe(1);
    expect(result[0].vin.value).toEqual('vin3');
  });

  it('should find by and conditions', async () => {
    let { result, total } = await carRepository.findByProps({
      plateNumber: new CarPlateNumber('plateNumber4'),
      vin: new CarVin('vin4'),
    });

    expect(total).toBe(1);
    expect(result.length).toBe(1);

    ({ result, total } = await carRepository.findByProps({
      plateNumber: new CarPlateNumber('plateNumber4'),
      vin: new CarVin('vin3'),
    }));

    expect(total).toBe(0);
    expect(result.length).toBe(0);
  });

  it('should soft remove', async () => {
    await carRepository.softRemoveById(cars[0].id.value);

    const deleted = await carRepository.findOne({
      where: { id: cars[0].id.value },
      withDeleted: true,
    });

    expect(deleted.deletedBy).toBe('test-user-id');
  });

  describe('updateLocation', () => {
    let testCar: Car;
    const stationId = randomUUID().toString();
    const locatedAt = DateTime.now();

    beforeAll(async () => {
      testCar = new CarBuilder()
        .withCarModelId(carModel.id)
        .withCarModel(carModel)
        .withAvailability(CarAvailability.AVAILABLE)
        .withLocatedAt(DateTime.now().minus({ seconds: 1 }))
        .build();

      testCar = await carRepository.insertOne(testCar);
    });

    it('should successfully update car location', async () => {
      const updatedCar = await carRepository.updateLocation(
        testCar.id,
        stationId,
        locatedAt,
      );

      expect(updatedCar.id.value).toBe(testCar.id.value);
      expect(updatedCar.stationId).toBe(stationId);
      expect(updatedCar.locatedAt.toMillis()).toBeCloseTo(
        locatedAt.toMillis(),
        -3,
      );
    });

    it('should throw CarNotFoundError when car does not exist', async () => {
      const nonExistentCarId = new CarId(randomUUID().toString());

      try {
        await carRepository.updateLocation(
          nonExistentCarId,
          stationId,
          locatedAt,
        );
        fail('Expected error to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(CarNotFoundError);
        expect((error as CarNotFoundError).code).toBe('CAR_NOT_FOUND');
      }
    });

    it('should throw CarLocationUpdateLocationTooOldError when trying to update with older timestamp', async () => {
      const olderTimestamp = locatedAt.minus({ seconds: 1 });

      try {
        await carRepository.updateLocation(
          testCar.id,
          randomUUID().toString(),
          olderTimestamp,
        );
        fail('Expected error to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(CarLocationUpdateLocationTooOldError);
        expect((error as CarLocationUpdateLocationTooOldError).code).toBe(
          'LOCATION_TOO_OLD',
        );
      }
    });

    it('should throw CarLocationNotUpdatedError when update is not successful for other reasons', async () => {
      const originalQueryBuilder = carRepository.createQueryBuilder;

      // @ts-expect-error - mock return value is not typed
      carRepository.createQueryBuilder = jest.fn().mockReturnValue({
        update: jest.fn().mockReturnThis(),
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ affected: 0 } as never),
      });

      const originalFindOneBy = carRepository.findOneBy;
      // @ts-expect-error - mock return value is not typed
      carRepository.findOneBy = jest.fn().mockResolvedValue({
        id: testCar.id.value,
        locatedAt: null,
      } as never);

      const locatedAt = DateTime.now();

      try {
        await carRepository.updateLocation(testCar.id, stationId, locatedAt);
        fail('Expected error to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(CarLocationNotUpdatedError);
        expect((error as CarLocationNotUpdatedError).code).toBe(
          'CAR_LOCATION_NOT_UPDATED',
        );
      }

      carRepository.createQueryBuilder = originalQueryBuilder;
      carRepository.findOneBy = originalFindOneBy;
    });

    afterAll(async () => {
      await carRepository.delete({
        id: testCar.id.value,
      });
    });
  });

  describe('updateAvailability', () => {
    let testCar: Car;
    const availabilityAt = DateTime.now();

    beforeAll(async () => {
      testCar = new CarBuilder()
        .withCarModelId(carModel.id)
        .withCarModel(carModel)
        .withAvailability(CarAvailability.AVAILABLE)
        .withAvailabilityUpdatedAt(DateTime.now().minus({ minutes: 5 }))
        .build();

      testCar = await carRepository.insertOne(testCar);
    });

    it('should successfully update car availability', async () => {
      await carRepository.updateAvailability(
        testCar.id,
        CarAvailability.ON_RENTAL,
        availabilityAt,
      );

      const updatedCar = await carRepository.getOneByIdOrFail(testCar.id);

      expect(updatedCar.id.value).toBe(testCar.id.value);
      expect(updatedCar.availability).toBe(CarAvailability.ON_RENTAL);
      expect(updatedCar.availabilityUpdatedAt.toMillis()).toBeCloseTo(
        availabilityAt.toMillis(),
        -3,
      );
    });

    it('should throw CarAvailabilityUpdateAvailabilityTooOldError when trying to update with older timestamp', async () => {
      const olderTimestamp = availabilityAt.minus({ seconds: 1 });

      try {
        await carRepository.updateAvailability(
          testCar.id,
          CarAvailability.AVAILABLE,
          olderTimestamp,
        );
        fail('Expected error to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(
          CarAvailabilityUpdateAvailabilityTooOldError,
        );
        expect(
          (error as CarAvailabilityUpdateAvailabilityTooOldError).code,
        ).toBe('AVAILABILITY_TOO_OLD');
      }
    });

    it('should throw CarAvailabilityNotUpdatedError when update is not successful for other reasons', async () => {
      const originalQueryBuilder = carRepository.createQueryBuilder;

      // @ts-expect-error - mock return value is not typed
      carRepository.createQueryBuilder = jest.fn().mockReturnValue({
        update: jest.fn().mockReturnThis(),
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ affected: 0 } as never),
      });

      const originalFindOneBy = carRepository.findOneBy;
      // @ts-expect-error - mock return value is not typed
      carRepository.findOneBy = jest.fn().mockResolvedValue({
        id: testCar.id.value,
        availabilityUpdatedAt: null,
      } as never);

      const availabilityAt = DateTime.now();

      try {
        await carRepository.updateAvailability(
          testCar.id,
          CarAvailability.AVAILABLE,
          availabilityAt,
        );
        fail('Expected error to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(CarAvailabilityNotUpdatedError);
        expect((error as CarAvailabilityNotUpdatedError).code).toBe(
          'CAR_AVAILABILITY_NOT_UPDATED',
        );
      }

      carRepository.createQueryBuilder = originalQueryBuilder;
      carRepository.findOneBy = originalFindOneBy;
    });

    afterAll(async () => {
      await carRepository.delete({
        id: testCar.id.value,
      });
    });
  });

  describe('switchCarAvailability', () => {
    let car1: Car;
    let car2: Car;
    const reservedAt = DateTime.now().minus({ seconds: 1 });

    beforeAll(async () => {
      car1 = new CarBuilder()
        .withCarModelId(carModel.id)
        .withCarModel(carModel)
        .withAvailability(CarAvailability.AVAILABLE)
        .withAvailabilityUpdatedAt(DateTime.now().minus({ minutes: 5 }))
        .build();

      car2 = new CarBuilder()
        .withCarModelId(carModel.id)
        .withCarModel(carModel)
        .withAvailability(CarAvailability.AVAILABLE)
        .withAvailabilityUpdatedAt(DateTime.now().minus({ minutes: 5 }))
        .build();

      car1 = await carRepository.insertOne(car1);
      car2 = await carRepository.insertOne(car2);
    });

    it('should successfully switch car availabilities', async () => {
      await carRepository.switchCarAvailability(
        car1.id,
        CarAvailability.ON_RENTAL,
        car2.id,
        CarAvailability.AVAILABLE,
        reservedAt,
      );

      const updatedCar1 = await carRepository.getOneByIdOrFail(car1.id);
      const updatedCar2 = await carRepository.getOneByIdOrFail(car2.id);

      expect(updatedCar1.availability).toBe(CarAvailability.ON_RENTAL);
      expect(updatedCar2.availability).toBe(CarAvailability.AVAILABLE);
      expect(updatedCar1.availabilityUpdatedAt.toMillis()).toBeCloseTo(
        reservedAt.toMillis(),
        -3,
      );
      expect(updatedCar2.availabilityUpdatedAt.toMillis()).toBeCloseTo(
        reservedAt.toMillis(),
        -3,
      );
    });

    it('should throw CarAvailabilityUpdateAvailabilityTooOldError when trying to update with older timestamp', async () => {
      const newerTimestamp = DateTime.now();
      await carRepository.updateAvailability(
        car1.id,
        CarAvailability.AVAILABLE,
        newerTimestamp,
      );

      const olderTimestamp = newerTimestamp.minus({ seconds: 10 });

      await expect(
        carRepository.switchCarAvailability(
          car1.id,
          CarAvailability.ON_RENTAL,
          car2.id,
          CarAvailability.AVAILABLE,
          olderTimestamp,
        ),
      ).rejects.toThrow(CarAvailabilityUpdateAvailabilityTooOldError);
    });

    it('should throw CarAvailabilityNotUpdatedError when car does not exist', async () => {
      const nonExistentCarId = new CarId(randomUUID().toString());

      await expect(
        carRepository.switchCarAvailability(
          nonExistentCarId,
          CarAvailability.ON_RENTAL,
          car2.id,
          CarAvailability.AVAILABLE,
          reservedAt,
        ),
      ).rejects.toThrow(CarAvailabilityNotUpdatedError);
    });

    afterAll(async () => {
      await carRepository.delete({});
    });
  });

  describe('updateAvailabilityAndLocationAsOf', () => {
    it('should successfully update car availability and location', async () => {
      let car = new CarBuilder()
        .withCarModelId(carModel.id)
        .withCarModel(carModel)
        .withAvailability(CarAvailability.ON_RENTAL)
        .withAvailabilityUpdatedAt(DateTime.now().minus({ minutes: 5 }))
        .withStationId(falso.randUuid())
        .withLocatedAt(DateTime.now().minus({ minutes: 10 }))
        .build();

      car = await carRepository.insertOne(car);

      const newStation = falso.randUuid();

      await carRepository.updateAvailabilityAndLocationAsOf(
        car.id,
        CarAvailability.AVAILABLE,
        newStation,
        DateTime.now().minus({ seconds: 20 }),
      );

      const updatedCar = await carRepository.getOneByIdOrFail(car.id);

      expect(updatedCar.availability).toBe(CarAvailability.AVAILABLE);
      expect(updatedCar.availabilityUpdatedAt.toMillis()).toBeCloseTo(
        DateTime.now().toMillis(),
        -3,
      );
      expect(updatedCar.stationId).toBe(newStation.toString());
      expect(updatedCar.locatedAt.toMillis()).toBeCloseTo(
        DateTime.now().toMillis(),
        -3,
      );
    });

    const forbiddenTransitions: Array<[CarAvailability, CarAvailability]> = [
      [CarAvailability.OUT_OF_SERVICE, CarAvailability.ON_RENTAL],
      [CarAvailability.OUT_OF_SERVICE, CarAvailability.RESERVED],
      [CarAvailability.RESERVED, CarAvailability.OUT_OF_SERVICE],
      [CarAvailability.ON_RENTAL, CarAvailability.RESERVED],
      [CarAvailability.ON_RENTAL, CarAvailability.OUT_OF_SERVICE],
    ];

    it.each(forbiddenTransitions)(
      'should throw CarAvailabilityTransitionForbidden when transition from %s to %s',
      async (from, to) => {
        let car = new CarBuilder()
          .withCarModelId(carModel.id)
          .withCarModel(carModel)
          .withAvailability(from)
          .withAvailabilityUpdatedAt(DateTime.now().minus({ minutes: 5 }))
          .withStationId(falso.randUuid())
          .withLocatedAt(DateTime.now().minus({ minutes: 10 }))
          .build();

        car = await carRepository.insertOne(car);

        await expect(
          carRepository.updateAvailabilityAndLocationAsOf(
            car.id,
            to,
            falso.randUuid(),
            DateTime.now(),
          ),
        ).rejects.toThrow(CarAvailabilityTransitionForbidden);
      },
    );

    const allowedTransitions: Array<[CarAvailability, CarAvailability]> = [
      [CarAvailability.AVAILABLE, CarAvailability.AVAILABLE],
      [CarAvailability.AVAILABLE, CarAvailability.OUT_OF_SERVICE],
      [CarAvailability.AVAILABLE, CarAvailability.ON_RENTAL],
      [CarAvailability.AVAILABLE, CarAvailability.RESERVED],
      [CarAvailability.OUT_OF_SERVICE, CarAvailability.OUT_OF_SERVICE],
      [CarAvailability.OUT_OF_SERVICE, CarAvailability.AVAILABLE],
      [CarAvailability.RESERVED, CarAvailability.RESERVED],
      [CarAvailability.RESERVED, CarAvailability.AVAILABLE],
      [CarAvailability.RESERVED, CarAvailability.ON_RENTAL],
      [CarAvailability.ON_RENTAL, CarAvailability.ON_RENTAL],
      [CarAvailability.ON_RENTAL, CarAvailability.AVAILABLE],
    ];

    it.each(allowedTransitions)(
      'should allow transition from %s to %s',
      async (from, to) => {
        let car = new CarBuilder()
          .withCarModelId(carModel.id)
          .withCarModel(carModel)
          .withAvailability(from)
          .withAvailabilityUpdatedAt(DateTime.now().minus({ minutes: 5 }))
          .withStationId(falso.randUuid())
          .withLocatedAt(DateTime.now().minus({ minutes: 10 }))
          .build();

        car = await carRepository.insertOne(car);
        const newStation = falso.randUuid();
        const asOf = DateTime.now();

        await expect(
          carRepository.updateAvailabilityAndLocationAsOf(
            car.id,
            to,
            newStation,
            asOf,
          ),
        ).resolves.toBeDefined();

        const updatedCar = await carRepository.getOneByIdOrFail(car.id);
        expect(updatedCar.availability).toBe(to);
        expect(updatedCar.stationId).toBe(newStation);
      },
    );

    it('should throw CarUpdateRequestOutdatedError when updating car with as of older than availabilityUpdatedAt', async () => {
      let car = new CarBuilder()
        .withCarModelId(carModel.id)
        .withCarModel(carModel)
        .withAvailability(CarAvailability.ON_RENTAL)
        .withAvailabilityUpdatedAt(DateTime.now().minus({ minutes: 5 }))
        .withStationId(falso.randUuid())
        .withLocatedAt(DateTime.now().minus({ minutes: 10 }))
        .build();

      car = await carRepository.insertOne(car);

      await expect(
        carRepository.updateAvailabilityAndLocationAsOf(
          car.id,
          CarAvailability.AVAILABLE,
          falso.randUuid(),
          DateTime.now().minus({ minutes: 6 }),
        ),
      ).rejects.toThrow(CarUpdateRequestOutdatedError);
    });

    it('should throw CarUpdateRequestOutdatedError when updating car with as of older than locatedAt', async () => {
      let car = new CarBuilder()
        .withCarModelId(carModel.id)
        .withCarModel(carModel)
        .withAvailability(CarAvailability.ON_RENTAL)
        .withAvailabilityUpdatedAt(DateTime.now().minus({ minutes: 10 }))
        .withStationId(falso.randUuid())
        .withLocatedAt(DateTime.now().minus({ minutes: 5 }))
        .build();

      car = await carRepository.insertOne(car);

      await expect(
        carRepository.updateAvailabilityAndLocationAsOf(
          car.id,
          CarAvailability.AVAILABLE,
          falso.randUuid(),
          DateTime.now().minus({ minutes: 6 }),
        ),
      ).rejects.toThrow(CarUpdateRequestOutdatedError);
    });

    it('should throw CarNotFoundError when car does not exist', async () => {
      const nonExistentCarId = new CarId(randomUUID().toString());

      await expect(
        carRepository.updateAvailabilityAndLocationAsOf(
          nonExistentCarId,
          CarAvailability.AVAILABLE,
          falso.randUuid(),
          DateTime.now().minus({ minutes: 1 }),
        ),
      ).rejects.toThrow(CarNotFoundError);
    });

    afterEach(async () => {
      await carRepository.delete({});
    });
  });

  afterAll(async () => {
    await carRepository.delete({});
    await carModelRepository.delete({});
    InvocationContext.clear();
  });
});
