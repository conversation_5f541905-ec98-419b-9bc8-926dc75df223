import { jest } from '@jest/globals';
import axios, { AxiosError } from 'axios';
import { randomUUID } from 'crypto';
import { CarId } from 'src/car/car.type';
import { InternalServerError } from 'src/shared/errors/http.error';
import { VulogCarId, VulogFleetId } from 'src/shared/shared.type';
import { VulogVehicleInfoResponseDto } from 'src/vulog/dto/vulog-vehicle-info-response.dto';
import { VulogClient } from 'src/vulog/vulog-client';

jest.mock('axios');
// @ts-expect-error - This is a mock implementation
axios.get = jest.fn();
// @ts-expect-error - This is a mock implementation
axios.post = jest.fn();

describe('VulogClient', () => {
  let vulogClient: VulogClient;
  const mockFleetId = new VulogFleetId('mock-fleet-id');
  const mockVehicleId = new VulogCarId('mock-vehicle-id');
  const mockCarId = new CarId(randomUUID());

  beforeEach(() => {
    jest.clearAllMocks();

    vulogClient = new VulogClient();

    // @ts-expect-error - This is a mock implementation
    axios.post.mockResolvedValue({
      data: {
        access_token: 'mock-token',
        expires_in: 3600,
        refresh_token: 'mock-refresh-token',
        token_type: 'Bearer',
        refresh_expires_in: 7200,
      },
    });
  });

  describe('getCarState', () => {
    it('should successfully get car state when response is valid', async () => {
      // @ts-expect-error - This is a mock implementation
      axios.get.mockResolvedValueOnce({
        data: {
          id: mockVehicleId.value,
          status: {
            energyLevel: 75,
            locked: true,
            doorsAndWindowsClosed: true,
            doors: {
              frontLeftClosed: true,
              frontRightClosed: true,
            },
            windows: {
              frontLeftClosed: true,
              frontRightClosed: true,
            },
            cablePlugged: false,
            charging: false,
            engineOn: false,
          },
          zones: {
            current: [
              {
                type: 'operational',
                zoneId: 'test-zone-1',
              },
            ],
          },
        },
      });

      const result = await vulogClient.getCarState(
        mockFleetId,
        mockVehicleId,
        mockCarId,
      );

      expect(result).toBeDefined();
      expect(result.carId).toEqual(mockCarId);
      expect(result.batteryLevel?.value).toBe(75);
      expect(result.locked).toBe(true);
    });

    it('should throw InternalServerError when Vulog returns 401 Unauthorized', async () => {
      const error = new AxiosError();
      error.response = {
        status: 401,
        data: { message: 'Unauthorized' },
        statusText: 'Unauthorized',
        headers: {},
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        config: {} as any,
      };

      // @ts-expect-error - This is a mock implementation
      axios.get.mockRejectedValueOnce(error);

      await expect(
        vulogClient.getCarState(mockFleetId, mockVehicleId, mockCarId),
      ).rejects.toThrow(InternalServerError);
    });

    it('should throw InternalServerError when Vulog returns 403 Forbidden', async () => {
      const error = new AxiosError();
      error.response = {
        status: 403,
        data: { message: 'Forbidden' },
        statusText: 'Forbidden',
        headers: {},
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        config: {} as any,
      };

      // @ts-expect-error - This is a mock implementation
      axios.get.mockRejectedValueOnce(error);

      await expect(
        vulogClient.getCarState(mockFleetId, mockVehicleId, mockCarId),
      ).rejects.toThrow(InternalServerError);
    });

    it('should not propagate error when Vulog returns invalid data format', async () => {
      // @ts-expect-error - This is a mock implementation
      axios.get.mockResolvedValueOnce({
        data: {
          id: 'foobar',
          someOtherField: 'value',
        },
      });

      const result = await vulogClient.getCarState(
        mockFleetId,
        mockVehicleId,
        mockCarId,
      );

      expect(result).toBeDefined();
    });

    it('should propagate other errors from Vulog API', async () => {
      const error = new Error('Unknown Vulog API error');
      // @ts-expect-error - This is a mock implementation
      axios.get.mockRejectedValueOnce(error);

      await expect(
        vulogClient.getCarState(mockFleetId, mockVehicleId, mockCarId),
      ).rejects.toThrow('Unknown Vulog API error');
    });
  });

  describe('getVehiclesInfo', () => {
    it('should retrieve all vehicles across paginated responses', async () => {
      // Prepare 7 mock vehicles
      const vehicles = Array.from({ length: 7 }, (_, i) => ({
        id: `vehicle-${i + 1}`,
      }));
      // Mock paginated responses
      // Page 1: 2 vehicles, continuationToken 'token-1'
      // Page 2: 2 vehicles, continuationToken 'token-2'
      // Page 3: 2 vehicles, continuationToken 'token-3'
      // Page 4: 1 vehicle, no continuationToken
      axios.get
        // @ts-expect-error - This is a mock implementation
        .mockResolvedValueOnce({
          data: {
            content: vehicles.slice(0, 2),
            continuationToken: 'token-1',
          },
        })
        .mockResolvedValueOnce({
          data: {
            content: vehicles.slice(2, 4),
            continuationToken: 'token-2',
          },
        })
        .mockResolvedValueOnce({
          data: {
            content: vehicles.slice(4, 6),
            continuationToken: 'token-3',
          },
        })
        .mockResolvedValueOnce({
          data: {
            content: vehicles.slice(6),
            continuationToken: undefined,
          },
        });

      const result = await vulogClient.getVehiclesInfo(mockFleetId);
      expect(result).toHaveLength(7);
      const resultIds = result.map((v: VulogVehicleInfoResponseDto) => v.id);
      expect(resultIds).toEqual(vehicles.map((v) => v.id));
    });
  });
});
