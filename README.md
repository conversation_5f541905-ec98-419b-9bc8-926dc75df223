# Getting Started

This repo stores code for Car Service Lambda functions.

## Setup Tools

This project uses AWS SAM CLI, you can install it with:

```
brew install aws-sam-cli
```

Default node version is 22:

```
nvm use 22
```

## Setup Local Env

Follow the instructions at [./setup/README.md](./setup/README.md)

## Install Dependencies

```
npm install
```

# DB Schema

## Run Migrations on Local DB

Start the local DB:

```
npm run local:container:start
```

Build the migrations into \*.mjs files:

```
npm run build
```

To run pending migrations, you can either run the npm script:

```
npm run migration:local:run
```

Or invoke the health endpoint:

```
curl http://127.0.0.1:3000/health
```

## Generate New Migration

```
npm run migration:local:generate --name="CreateCarModelTable"
```

## Create Empty Migration

```
npm run migration:local:create --name="CreateCarModelTable"
```

# Lambda Functions

## Build Lamda Function Handlers

```
npm run build
```

## Start Local API Gateway

```
npm run local:api
```

If your SAM Local API Gateway has trouble connecting to the DB or Localstack, try this:

```
sam local start-api --add-host="host.docker.internal:host-gateway"
```

Test it with:

```
curl http://127.0.0.1:3000/health

curl http://127.0.0.1:3000/health -H 'Correlation-Id: 7ae810a3-55bf-438e-a9d8-1ce9e1dcfc5e'
```

## Invoke Non-API Functions

```
sam local invoke CarLocationReconciliationFunction
```

# Environment Configuration

When the env var `SECRET_NAME` is set, `env.ts` will attempt to retrieve secrets from AWS Secrets Manager.

The retrieval is designed to only happen once per runtime setup.

Calls to `getConfig*()` will retrieve env config value as follow:

1. if `SECRET_NAME` is set, will look up in secrets
2. if not found, will look up in env vars `process.env`
3. if not found, without default value, and is not optional, will throw error

# Generate JWT Token

Run this command on the root of the project to generate a JWT token. We have dependency on `jsonwebtoken` in `package.json`, so make sure to install it first.

Change the `userId`, `username`, `email`, `name`, and `permissions` to your own values.

Change the `jwt-secret` to your own secret.

```
node -e "
const jwt = require('jsonwebtoken');
const payload = {
  userId: '123',
  username: 'test',
  email: '<EMAIL>',
  name: 'test',
  permissions: {
    all: 'all'
  },
  v: 1,
  iat: 1741345478
};
console.log(jwt.sign(payload, 'jwt-secret'));
"
```

Refer to [resource.ts](./src/auth/authorization/resource.ts) & [permission.ts](./src/auth/authorization/permission.ts) to learn more about available resources & permissions.

# Postman collection

Please use the collection `[CS] Car Service` to test the API.

# Testing the sqs bluesg-events

Remember to run the local setup script.

After that edit the `bluesg-events.json` at the root of the directory with your test event data

Run `npm run test:bluesg-events` to execute the event handler function

# Domain Classes

using DateTime from 'luxon' to handle date and time operations.
