#!/bin/bash

# Configuration (from command-line arguments)
BUCKET_NAME="$1"
DEPLOYMENT_FILE="$2"

# Check if arguments are provided
if [ -z "$BUCKET_NAME" ] || [ -z "$DEPLOYMENT_FILE" ]; then
  echo "Usage: $0 <bucket_name> <deployment_file>"
  exit 1
fi

# Check if the deployment file exists
if [ ! -f "$DEPLOYMENT_FILE" ]; then
  echo "Error: Deployment file '$DEPLOYMENT_FILE' not found."
  exit 1
fi

echo "Uploading with specs:"
cat $DEPLOYMENT_FILE

# Loop through each line in the deployment file
while IFS=' ' read -r ZIP_FILE FUNCTION_NAME; do
  if [ -z "$ZIP_FILE" ]; then
    continue # Skip empty lines
  fi

  # Check if the zip file exists
  if [ ! -f "./dist/$ZIP_FILE" ]; then
    echo "Error: Zip file '$ZIP_FILE' not found in ./dist/. Stopping."
    exit 1
  fi

  # Upload the zip file to S3
  echo "Uploading '$ZIP_FILE' to s3://$BUCKET_NAME/$ZIP_FILE..."
  aws s3 cp "./dist/$ZIP_FILE" "s3://$BUCKET_NAME/$ZIP_FILE"

  if [ $? -ne 0 ]; then
    echo "Error: Failed to upload '$ZIP_FILE'."
    exit $?
  else
    echo "Uploaded '$ZIP_FILE' successfully."
  fi
done < "$DEPLOYMENT_FILE"

echo "S3 upload complete."
