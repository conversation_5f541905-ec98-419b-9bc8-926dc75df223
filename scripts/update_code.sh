#!/bin/bash

# Configuration (from command-line arguments)
BUCKET_NAME="$1"
DEPLOYMENT_FILE="$2"

# Check if arguments are provided
if [ -z "$BUCKET_NAME" ] || [ -z "$DEPLOYMENT_FILE" ]; then
  echo "Usage: $0 <bucket_name> <deployment_file>"
  exit 1
fi

# Check if the deployment file exists
if [ ! -f "$DEPLOYMENT_FILE" ]; then
  echo "Error: Deployment file '$DEPLOYMENT_FILE' not found."
  exit 1
fi

echo "Updating function code with specs:"
cat $DEPLOYMENT_FILE

# Loop through each line in the deployment file
while IFS=' ' read -r ZIP_FILE FUNCTION_NAME; do
  if [ -z "$ZIP_FILE" ] || [ -z "$FUNCTION_NAME" ]; then
    continue # Skip empty lines
  fi

  # Update the Lambda function code
  echo "Updating Lambda function '$FUNCTION_NAME' with '$ZIP_FILE' from S3..."
  aws lambda update-function-code \
    --function-name "$FUNCTION_NAME" \
    --s3-bucket "$BUCKET_NAME" \
    --s3-key "$ZIP_FILE"

  if [ $? -ne 0 ]; then
    echo "Error: Failed to update Lambda function '$FUNCTION_NAME'."
    exit $?
  else
    echo "Updated Lambda function '$FUNCTION_NAME' successfully."
  fi
done < "$DEPLOYMENT_FILE"

echo "Lambda function updates complete."
