#!/bin/bash

# Configuration (function name from command-line argument)
FUNCTION_NAME="$1"

# Check if function name is provided
if [ -z "$FUNCTION_NAME" ]; then
  echo "Usage: $0 <function_name>"
  exit 1
fi

# Invoke the Lambda function
echo "Invoking lambda function $FUNCTION_NAME:"
aws lambda invoke \
  --function-name "$FUNCTION_NAME" \
  --cli-binary-format raw-in-base64-out \
  --log-type Tail \
  --query 'LogResult' \
  --output text \
  output.json | base64 --decode

# Check if the invocation was successful
if [ $? -ne 0 ]; then
  echo "Error: Failed to invoke Lambda function '$FUNCTION_NAME'."
  exit 1
fi

# Display the output
echo
echo "Migrations output:"
cat output.json
echo

# Verify status code
echo "Verifying status code"


# Extract the status code using parameter expansion and substring removal
status_code=$(cat output.json | sed -n 's/.*"statusCode":\([0-9]*\).*/\1/p')

# Check if the status code was found
if [[ -z "$status_code" ]]; then
  echo "Error: Could not extract status code."
  exit 1
fi

# Check if the status code is 200
if [[ "$status_code" -eq 200 ]]; then
  echo "Status code is 200 (OK)"
  exit 0
else
  echo "Status code is $status_code, expected 200"
  exit 1
fi
