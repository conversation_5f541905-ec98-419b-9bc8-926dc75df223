/** @type {import('ts-jest').JestConfigWithTsJest} */
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  globalSetup: '<rootDir>/test/shared/setup/testcontainerSetup.ts',
  globalTeardown: '<rootDir>/test/shared/setup/testcontainerTeardown.ts',
  testMatch: ['**/*.test.ts'],
  coverageDirectory: './coverage',
  transform: {
    '^.+\\.ts$': [
      'ts-jest',
      {
        useESM: true,
        tsconfig: 'tsconfig.json',
      },
    ],
  },
  coveragePathIgnorePatterns: ['/node_modules/', '/dist/'],
  modulePaths: ['<rootDir>'],
  extensionsToTreatAsEsm: ['.ts'],
};
